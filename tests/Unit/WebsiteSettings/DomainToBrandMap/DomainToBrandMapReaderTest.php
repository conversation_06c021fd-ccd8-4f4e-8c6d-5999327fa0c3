<?php

declare(strict_types=1);

namespace Tests\Unit\WebsiteSettings\DomainToBrandMap;

use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReader;
use App\WebsiteSettings\DomainToBrandMap\Exception\CouldNotDetermineBrandForDomainException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

class DomainToBrandMapReaderTest extends TestCase
{
    /** @var SerializedFileInterface|MockObject */
    private MockObject $serializedFileMock;

    private DomainToBrandMapReader $domainToBrandMapReader;

    protected function setUp(): void
    {
        $this->serializedFileMock = $this->createMock(SerializedFileInterface::class);

        $this->domainToBrandMapReader = new DomainToBrandMapReader(
            $this->serializedFileMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getBrandForDomainDataProvider(): array
    {
        return [
            'available brand' => [
                'domainMap'         => self::createDefaultDomainToBrandMap(),
                'domain'            => 'ru.brandweb.com',
                'expectedBrand'     => 'brandweb',
                'expectedException' => null,
            ],
            'another brand'   => [
                'domainMap'         => self::createDefaultDomainToBrandMap(),
                'domain'            => 'us-int.brandley.com',
                'expectedBrand'     => 'brandley',
                'expectedException' => null,
            ],
            'not found brand' => [
                'domainMap'         => self::createDefaultDomainToBrandMap(),
                'domain'            => 'de.rpley.com',
                'expectedBrand'     => null,
                'expectedException' => CouldNotDetermineBrandForDomainException::class,
            ],
        ];
    }

    /**
     * @return array<string, string>
     */
    private static function createDefaultDomainToBrandMap(): array
    {
        return [
            'de.brandweb.com'       => 'brandweb',
            'ru.brandweb.com'       => 'brandweb',
            'ru-int.brandweb.com'   => 'brandweb',
            'fr.brandley.com'       => 'brandley',
            'us.brandley.com'       => 'brandley',
            'us-int.brandley.com'   => 'brandley',
            'www.brandmixer.com'    => 'brandmixer',
            'www.brandmixer.co.uk'  => 'brandmixeruk',
            'www.brandmixer.com.tw' => 'brandmixertw',
        ];
    }

    /**
     * @param array<string, string> $domainMap
     */
    #[DataProvider('getBrandForDomainDataProvider')]
    public function testGetBrandForDomain(
        array $domainMap,
        string $domain,
        ?string $expectedBrand,
        ?string $expectedException
    ): void
    {
        $this->serializedFileMock->method('getContents')->willReturn($domainMap);

        // check hasDomain
        self::assertSame(
            $expectedBrand !== null,
            $this->domainToBrandMapReader->hasDomain($domain),
        );

        // check getBrandForDomain
        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        self::assertSame(
            $expectedBrand,
            $this->domainToBrandMapReader->getBrandForDomain($domain),
        );
    }
}
