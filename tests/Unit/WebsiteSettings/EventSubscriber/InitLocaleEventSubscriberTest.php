<?php

declare(strict_types=1);

namespace Tests\Unit\WebsiteSettings\EventSubscriber;

use App\WebsiteSettings\EventSubscriber\InitLocaleEventSubscriber;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Stub\Locale\Settings\LocaleSettingsHelperStub;

class InitLocaleEventSubscriberTest extends TestCase
{
    public function testSetActiveLocale(): void
    {
        $localeSettingsHelperStub = new LocaleSettingsHelperStub();
        $kernel = $this->createMock(HttpKernelInterface::class);
        $request = new Request();
        $requestEvent = new RequestEvent(
            kernel     : $kernel,
            request    : $request,
            requestType: HttpKernelInterface::MAIN_REQUEST,
        );

        (new InitLocaleEventSubscriber($localeSettingsHelperStub))->setActiveLocale($requestEvent);

        self::assertEquals(
            $localeSettingsHelperStub->getSettings()->locale->code,
            $request->getLocale(),
        );
    }
}
