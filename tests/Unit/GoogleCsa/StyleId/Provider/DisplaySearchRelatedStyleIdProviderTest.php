<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\StyleId\Provider;

use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettings;
use App\Generic\Device\Device;
use App\GoogleCsa\StyleId\Provider\DisplaySearchRelatedStyleIdProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\DisplaySearchRelated\Settings\DisplaySearchRelatedSettingsStubBuilder;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;

final class DisplaySearchRelatedStyleIdProviderTest extends TestCase
{
    private DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder;

    private SearchRequestStub $searchRequestStub;

    private ActiveTrackingEntryHelperStub $activeTrackingEntryHelperStub;

    protected function setUp(): void
    {
        parent::setUp();

        $this->displaySearchRelatedSettingsStubBuilder = new DisplaySearchRelatedSettingsStubBuilder();
        $this->displaySearchRelatedSettingsStubBuilder->setEnabled(true);

        $this->searchRequestStub = new SearchRequestStub();

        $this->activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
    }

    /**
     * @return mixed[]
     */
    public static function getStyleIdDataProvider(): array
    {
        return [
            'not DSR endpoint'                           => [
                'expectedStyleId' => null,
                'setTestCallback' => static function (
                    DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchRequestStub->setIsLandingPage(false);
                },
            ],
            'mobile by display search related settings'  => [
                'expectedStyleId' => 7,
                'setTestCallback' => static function (
                    DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $trackingEntryStubBuilder->setDevice(Device::MOBILE);
                    $displaySearchRelatedSettingsStubBuilder->setStyleIdMobile(7);
                    $searchRequestStub->setDisplaySearchRelated();
                },
            ],
            'desktop by display search related settings' => [
                'expectedStyleId' => 99,
                'setTestCallback' => static function (
                    DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $trackingEntryStubBuilder->setDevice(Device::DESKTOP);
                    $displaySearchRelatedSettingsStubBuilder->setStyleIdDesktop(99);
                    $searchRequestStub->setDisplaySearchRelated();
                },
            ],
        ];
    }

    #[dataProvider('getStyleIdDataProvider')]
    public function testGetStyleId(?int $expectedStyleId, callable $setTestCallback): void
    {
        $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder()
            ->setIsEmpty(false)
            ->setQuery('pizza');

        $setTestCallback(
            $this->displaySearchRelatedSettingsStubBuilder,
            $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder(),
            $this->searchRequestStub,
        );

        /** @var DisplaySearchRelatedSettings $displaySearchRelatedSettings */
        $displaySearchRelatedSettings = $this->displaySearchRelatedSettingsStubBuilder->create();

        $styleIdProvider = new DisplaySearchRelatedStyleIdProvider(
            $displaySearchRelatedSettings,
            $this->activeTrackingEntryHelperStub,
            $this->searchRequestStub,
        );

        self::assertSame(
            $expectedStyleId,
            $styleIdProvider->getStyleId(),
        );
    }
}
