<?php

declare(strict_types=1);

namespace Unit\GoogleCsa\Request;

use App\Component\Terms\GoogleRelatedTerms\GoogleRelatedTermsComponent;
use App\Component\Terms\GoogleRelatedTerms\GoogleRelatedTermsTarget;
use App\GoogleCsa\Parameter\GoogleCsaReferrerAdCreativeParameter;
use App\GoogleCsa\Request\GoogleCsaRelatedSearchRequestFactory;
use App\JsonTemplate\View\DataRequest\GoogleCsaRelatedSearchUnitViewDataRequest;
use App\Search\Query\SearchQueryNormalizer;
use PHPUnit\Framework\Attributes\DataProvider;
use Psr\Log\LoggerInterface;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\GoogleCsa\Generator\GoogleRelatedTermsContainerGeneratorStub;
use Tests\Stub\GoogleCsa\StyleId\GoogleCsaStyleIdParameterStub;
use Tests\Stub\JsonTemplate\View\ViewStub;
use Tests\Stub\Monetization\Settings\MonetizationSettingsStubBuilder;
use Tests\Stub\RelatedTerms\Request\RelatedTermsRequestStub;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Request\SeaRequestStub;
use Visymo\GoogleCsa\RelatedSearch\Unit\RelatedSearchUnitFactory;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

final class GoogleCsaRelatedTermsRequestFactoryTest extends PhpUnitTestCase
{
    /**
     * @return mixed[]
     */
    public static function createFromViewDataProvider(): array
    {
        return [
            'monetization enabled'  => [
                'monetizationEnabled' => true,
                'relatedAmount'       => 5,
            ],
            'monetization disabled' => [
                'monetizationEnabled' => false,
                'relatedAmount'       => 5,
            ],
            'no related'            => [
                'monetizationEnabled' => true,
                'relatedAmount'       => null,
            ],
        ];
    }

    #[DataProvider('createFromViewDataProvider')]
    public function testCreateFromView(bool $monetizationEnabled, ?int $relatedAmount): void
    {
        $viewStub = new ViewStub();

        if ($relatedAmount !== null) {
            $googleRelatedTermsComponent = new GoogleRelatedTermsComponent(
                amount                  : $relatedAmount,
                route                   : null,
                target                  : GoogleRelatedTermsTarget::CONTENT,
                termsUrlParameterEnabled: false,
                containerSuffix         : null,
                fallbackRelatedTerms    : null,
            );

            $viewStub->getDataRequest()->googleCsa()
                ->addRelatedSearchUnit(
                    new GoogleCsaRelatedSearchUnitViewDataRequest(
                        amount                  : $relatedAmount,
                        component               : $googleRelatedTermsComponent,
                        containerSuffix         : null,
                        route                   : null,
                        forContent              : true,
                        addVisymoRelatedTerms   : false,
                        termsUrlParameterEnabled: false,
                    ),
                );
        }

        $googleCsaRelatedSearchRequest = $this
            ->getGoogleCsaRelatedSearchRequestFactory($monetizationEnabled)
            ->createFromView($viewStub);

        $actualData = [
            'request' => $googleCsaRelatedSearchRequest,
            'units'   => [],
        ];

        foreach ($googleCsaRelatedSearchRequest->units ?? [] as $unit) {
            $actualData['units'][] = [
                'container'        => $unit->getContainer(),
                'related_searches' => $unit->getRelatedSearches(),
            ];
        }

        $assertionFile = $this->initJsonAssertionFile($actualData);
        $assertionFile->assertSame();
    }

    private function getGoogleCsaRelatedSearchRequestFactory(
        bool $monetizationEnabled
    ): GoogleCsaRelatedSearchRequestFactory
    {
        $googleCsaStyleIdParameterStub = new GoogleCsaStyleIdParameterStub();
        $googleCsaStyleIdParameterStub->setStyleId(1231231230);

        $seaRequestStub = new SeaRequestStub();
        $seaRequestStub->setReferrerAdCreative('Search for ipad');

        $searchRequest = new SearchRequestStub();
        $searchRequest
            ->reset()
            ->setQuery('ipad')
            ->setPage(1);

        $googleCsaReferrerAdCreativeParameter = new GoogleCsaReferrerAdCreativeParameter(
            seaRequest   : $seaRequestStub,
            searchRequest: $searchRequest,
        );

        $relatedTermsRequestStub = new RelatedTermsRequestStub();

        $monetizationSettings = (new MonetizationSettingsStubBuilder())
            ->setEnabled($monetizationEnabled)
            ->create();

        $debugRequestStub = new DebugRequestStub();
        $debugRequestStub->setShowGoogleTestAd(false);

        return new GoogleCsaRelatedSearchRequestFactory(
            debugRequest                        : $debugRequestStub,
            googleCsaStyleIdParameter           : $googleCsaStyleIdParameterStub,
            googleCsaReferrerAdCreativeParameter: $googleCsaReferrerAdCreativeParameter,
            relatedSearchUnitFactory            : new RelatedSearchUnitFactory(),
            relatedTermsRequest                 : $relatedTermsRequestStub,
            logger                              : $this->createMock(LoggerInterface::class),
            searchQueryNormalizer               : new SearchQueryNormalizer(),
            monetizationSettings                : $monetizationSettings,
            googleRelatedTermsContainerGenerator: new GoogleRelatedTermsContainerGeneratorStub()
        );
    }
}
