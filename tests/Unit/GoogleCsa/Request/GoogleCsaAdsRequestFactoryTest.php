<?php

declare(strict_types=1);

namespace Unit\GoogleCsa\Request;

use App\GoogleCsa\Request\GoogleCsaAdsRequestFactory;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Stub\GoogleCsa\StyleId\GoogleCsaStyleIdParameterStub;
use Tests\Stub\JsonTemplate\View\ViewStub;
use Tests\Stub\Monetization\Settings\MonetizationSettingsStubBuilder;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

final class GoogleCsaAdsRequestFactoryTest extends PhpUnitTestCase
{
    /**
     * @return mixed[]
     */
    public static function createFromViewDataProvider(): array
    {
        return [
            'monetization enabled'  => [
                'monetizationEnabled' => true,
                'topAdAmount'         => 5,
            ],
            'monetization disabled' => [
                'monetizationEnabled' => false,
                'topAdAmount'         => 5,
            ],
            'no top ads'            => [
                'monetizationEnabled' => true,
                'topAdAmount'         => null,
            ],
        ];
    }

    #[DataProvider('createFromViewDataProvider')]
    public function testCreateFromView(bool $monetizationEnabled, ?int $topAdAmount): void
    {
        $viewStub = new ViewStub();
        $viewStub->getDataRequest()->googleCsa()
            ->setTopAdContainer('csa-top')
            ->setTopAdAmount($topAdAmount);

        $googleCsaAdsRequest = $this
            ->getGoogleCsaAdsRequestFactory($monetizationEnabled)
            ->createFromView($viewStub);
        $actualData = [
            'request' => $googleCsaAdsRequest,
        ];

        $assertionFile = $this->initJsonAssertionFile($actualData);
        $assertionFile->assertSame();
    }

    private function getGoogleCsaAdsRequestFactory(bool $monetizationEnabled): GoogleCsaAdsRequestFactory
    {
        $googleCsaStyleIdParameterStub = new GoogleCsaStyleIdParameterStub();
        $googleCsaStyleIdParameterStub->setStyleId(**********);

        $searchRequest = new SearchRequestStub();
        $searchRequest
            ->reset()
            ->setQuery('ipad')
            ->setPage(1);

        $websiteSettingsStub = new WebsiteSettingsStub();
        $websiteSettingsStub->getGoogleAdSense()
            ->setEnabled(true)
            ->setContractType(ContractType::ONLINE);
        $websiteSettingsHelperMock = $this->createConfiguredMock(
            WebsiteSettingsHelper::class,
            [
                'getSettings' => $websiteSettingsStub,
            ],
        );

        $monetizationSettings = (new MonetizationSettingsStubBuilder())
            ->setEnabled($monetizationEnabled)
            ->create();

        return new GoogleCsaAdsRequestFactory(
            googleCsaStyleIdParameter: $googleCsaStyleIdParameterStub,
            searchRequest            : $searchRequest,
            websiteSettingsHelper    : $websiteSettingsHelperMock,
            monetizationSettings     : $monetizationSettings,
        );
    }
}
