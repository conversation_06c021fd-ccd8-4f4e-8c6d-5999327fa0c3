<?php

declare(strict_types=1);

namespace Tests\Unit\Account\Settings;

use App\Account\Settings\AccountSettingsFactory;
use App\Account\Settings\AccountSettingsRepository;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Account\Settings\AccountSettingsStubBuilder;

class AccountSettingsRepositoryTest extends TestCase
{
    private WebsiteConfigurationHelper & MockObject $websiteConfigurationHelperMock;

    private AccountSettingsFactory & MockObject $accountSettingsFactoryMock;

    private AccountSettingsRepository $accountSettingsRepository;

    protected function setUp(): void
    {
        $this->websiteConfigurationHelperMock = $this->createMock(WebsiteConfigurationHelper::class);

        $this->accountSettingsFactoryMock = $this->createMock(AccountSettingsFactory::class);
        $this->accountSettingsRepository = new AccountSettingsRepository(
            $this->websiteConfigurationHelperMock,
            $this->accountSettingsFactoryMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function accountSettingsDataProvider(): array
    {
        return [
            'account found'     => [
                'accountId'     => 1,
                'accountConfig' => [],
                'expectCreate'  => true,
            ],
            'account not found' => [
                'accountId'     => 1,
                'accountConfig' => null,
                'expectCreate'  => false,
            ],
        ];
    }

    /**
     * @param mixed[]|null $accountConfig
     */
    #[DataProvider('accountSettingsDataProvider')]
    public function testGetAccountSettings(int $accountId, ?array $accountConfig, bool $expectCreate): void
    {
        $websiteConfiguration = new WebsiteConfiguration(
            [
                WebsiteConfiguration::KEY_ACCOUNTS => [
                    $accountId => $accountConfig,
                ],
            ],
        );

        $this->websiteConfigurationHelperMock
            ->method('getConfiguration')
            ->willReturn($websiteConfiguration);

        $this->accountSettingsFactoryMock->expects($expectCreate ? $this->once() : $this->never())
            ->method('create')
            ->willReturn((new AccountSettingsStubBuilder())->create());

        $this->accountSettingsRepository->getByAccountId($accountId);
    }
}
