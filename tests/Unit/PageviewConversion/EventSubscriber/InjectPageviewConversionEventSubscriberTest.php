<?php

declare(strict_types=1);

namespace Tests\Unit\PageviewConversion\EventSubscriber;

use App\PageviewConversion\EventSubscriber\InjectPageviewConversionEventSubscriber;
use App\PageviewConversion\Settings\PageviewConversionSettings;
use App\PageviewConversion\Url\PageviewConversionUrlGenerator;
use App\Search\Request\SearchRequestInterface;
use App\Template\Event\RenderTemplateFootersEvent;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

class InjectPageviewConversionEventSubscriberTest extends TestCase
{
    public function testInjectPageviewConversionAddsItemWhenEnabled(): void
    {
        $twigMock = $this->createMock(Environment::class);
        $searchRequestMock = $this->createMock(SearchRequestInterface::class);
        $urlGeneratorMock = $this->createMock(PageviewConversionUrlGenerator::class);
        $settings = new PageviewConversionSettings(
            enabled          : true,
            enabledForRequest: true,
        );
        $searchRequestMock->method('isLandingPage')->willReturn(true);
        $urlGeneratorMock->method('generateLanding')->willReturn('url');
        $twigMock->method('render')->willReturn('rendered');

        $eventMock = $this->createMock(RenderTemplateFootersEvent::class);
        $eventMock->expects($this->once())->method('addItem')->with('rendered');

        $subscriber = new InjectPageviewConversionEventSubscriber($twigMock, $searchRequestMock, $urlGeneratorMock, $settings);
        $subscriber->injectPageviewConversion($eventMock);
    }

    public function testInjectPageviewConversionDoesNothingIfNotEnabled(): void
    {
        $twigMock = $this->createMock(Environment::class);
        $searchRequestMock = $this->createMock(SearchRequestInterface::class);
        $urlGeneratorMock = $this->createMock(PageviewConversionUrlGenerator::class);
        $settings = new PageviewConversionSettings(
            enabled          : true,
            enabledForRequest: false,
        );
        $eventMock = $this->createMock(RenderTemplateFootersEvent::class);
        $eventMock->expects($this->never())->method('addItem');

        $subscriber = new InjectPageviewConversionEventSubscriber($twigMock, $searchRequestMock, $urlGeneratorMock, $settings);
        $subscriber->injectPageviewConversion($eventMock);
    }

    public function testInjectPageviewConversionUsesLandingRelatedUrlForNonLandingPage(): void
    {
        $twigMock = $this->createMock(Environment::class);
        $searchRequestMock = $this->createMock(SearchRequestInterface::class);
        $urlGeneratorMock = $this->createMock(PageviewConversionUrlGenerator::class);
        $settings = new PageviewConversionSettings(
            enabled          : true,
            enabledForRequest: true,
        );
        $searchRequestMock->method('isLandingPage')->willReturn(false);
        $urlGeneratorMock->method('generateLandingRelated')->willReturn('related-url');
        $twigMock->expects($this->once())
            ->method('render')
            ->with(
                '@theme/pageview_conversion/pageview_conversion_scripts.html.twig',
                ['pageview_conversion_url' => 'related-url']
            )
            ->willReturn('rendered');

        $eventMock = $this->createMock(RenderTemplateFootersEvent::class);
        $eventMock->expects($this->once())->method('addItem')->with('rendered');

        $subscriber = new InjectPageviewConversionEventSubscriber($twigMock, $searchRequestMock, $urlGeneratorMock, $settings);
        $subscriber->injectPageviewConversion($eventMock);
    }
}
