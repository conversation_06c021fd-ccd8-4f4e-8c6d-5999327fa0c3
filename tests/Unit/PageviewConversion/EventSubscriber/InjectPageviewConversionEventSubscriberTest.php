<?php

declare(strict_types=1);

namespace Tests\Unit\PageviewConversion\EventSubscriber;

use App\PageviewConversion\EventSubscriber\InjectPageviewConversionEventSubscriber;
use App\PageviewConversion\Settings\PageviewConversionSettings;
use App\PageviewConversion\Url\PageviewConversionUrlGenerator;
use App\Search\Request\SearchRequestInterface;
use App\Template\Event\RenderTemplateFootersEvent;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

class InjectPageviewConversionEventSubscriberTest extends TestCase
{
    public function testInjectPageviewConversionAddsItemWhenEnabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $searchRequest = $this->createMock(SearchRequestInterface::class);
        $urlGenerator = $this->createMock(PageviewConversionUrlGenerator::class);
        $settings = new PageviewConversionSettings(
            enabled          : true,
            enabledForRequest: true,
        );
        $searchRequest->method('isLandingPage')->willReturn(true);
        $urlGenerator->method('generateLanding')->willReturn('url');
        $twig->method('render')->willReturn('rendered');

        $event = $this->createMock(RenderTemplateFootersEvent::class);
        $event->expects($this->once())->method('addItem')->with('rendered');

        $subscriber = new InjectPageviewConversionEventSubscriber($twig, $searchRequest, $urlGenerator, $settings);
        $subscriber->injectPageviewConversion($event);
    }

    public function testInjectPageviewConversionDoesNothingIfNotEnabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $searchRequest = $this->createMock(SearchRequestInterface::class);
        $urlGenerator = $this->createMock(PageviewConversionUrlGenerator::class);
        $settings = new PageviewConversionSettings(
            enabled          : true,
            enabledForRequest: false,
        );
        $event = $this->createMock(RenderTemplateFootersEvent::class);
        $event->expects($this->never())->method('addItem');

        $subscriber = new InjectPageviewConversionEventSubscriber($twig, $searchRequest, $urlGenerator, $settings);
        $subscriber->injectPageviewConversion($event);
    }

    public function testInjectPageviewConversionUsesLandingRelatedUrlForNonLandingPage(): void
    {
        $twig = $this->createMock(Environment::class);
        $searchRequest = $this->createMock(SearchRequestInterface::class);
        $urlGenerator = $this->createMock(PageviewConversionUrlGenerator::class);
        $settings = new PageviewConversionSettings(
            enabled          : true,
            enabledForRequest: true,
        );
        $searchRequest->method('isLandingPage')->willReturn(false);
        $urlGenerator->method('generateLandingRelated')->willReturn('related-url');
        $twig->expects($this->once())
            ->method('render')
            ->with(
                '@theme/pageview_conversion/pageview_conversion_scripts.html.twig',
                ['pageview_conversion_url' => 'related-url']
            )
            ->willReturn('rendered');

        $event = $this->createMock(RenderTemplateFootersEvent::class);
        $event->expects($this->once())->method('addItem')->with('rendered');

        $subscriber = new InjectPageviewConversionEventSubscriber($twig, $searchRequest, $urlGenerator, $settings);
        $subscriber->injectPageviewConversion($event);
    }
}
