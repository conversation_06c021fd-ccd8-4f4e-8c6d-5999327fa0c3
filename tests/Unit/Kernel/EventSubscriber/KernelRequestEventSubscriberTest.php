<?php

declare(strict_types=1);

namespace Tests\Unit\Kernel\EventSubscriber;

use App\Kernel\EventSubscriber\KernelRequestEventSubscriber;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\JsonTemplate\Settings\JsonTemplateSettingsStubBuilder;
use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Loader\FilesystemLoader;
use Visymo\Shared\Infrastructure\Stub\Domain\Logger\MemoryLoggerStub;

class KernelRequestEventSubscriberTest extends TestCase
{
    private Environment & MockObject $twigMock;

    private FilesystemLoader & MockObject $filesystemLoaderMock;

    private MemoryLoggerStub $memoryLoggerStub;

    private DebugRequestStub $debugRequestStub;

    protected function setUp(): void
    {
        $this->filesystemLoaderMock = $this->createMock(FilesystemLoader::class);
        $this->twigMock = $this->createConfiguredMock(
            Environment::class,
            [
                'getLoader' => $this->filesystemLoaderMock,
            ],
        );
        $this->memoryLoggerStub = new MemoryLoggerStub();
        $this->debugRequestStub = new DebugRequestStub();
    }

    public function testAddJsonTemplateOverridesWithoutOverrides(): void
    {
        $this->expectNotToPerformAssertions();

        $jsonTemplateSettings = (new JsonTemplateSettingsStubBuilder())
            ->setTemplateOverrides(null)
            ->create();

        $kernelRequestEventSubscriber = new KernelRequestEventSubscriber(
            twig                : $this->twigMock,
            jsonTemplateSettings: $jsonTemplateSettings,
            projectDir          : __DIR__,
            logger              : $this->memoryLoggerStub,
            debugRequest        : $this->debugRequestStub,
        );

        $kernelRequestEventSubscriber->addJsonTemplateOverrides();
    }

    public function testAddJsonTemplateOverridesWithOverride(): void
    {
        $projectDir = __DIR__;
        $jsonTemplateSettings = (new JsonTemplateSettingsStubBuilder())
            ->setTemplateOverrides(['test'])
            ->create();

        $this->filesystemLoaderMock->expects($this->once())
            ->method('prependPath')
            ->with(
                sprintf(
                    '%s/resources/json_template_overrides/%s',
                    $projectDir,
                    'test',
                ),
                'themeJson',
            );

        $kernelRequestEventSubscriber = new KernelRequestEventSubscriber(
            twig                : $this->twigMock,
            jsonTemplateSettings: $jsonTemplateSettings,
            projectDir          : $projectDir,
            logger              : $this->memoryLoggerStub,
            debugRequest        : $this->debugRequestStub,
        );

        $kernelRequestEventSubscriber->addJsonTemplateOverrides();
    }

    public function testAddJsonTemplateOverridesWithInvalidOverride(): void
    {
        $projectDir = __DIR__;
        $jsonTemplateSettings = (new JsonTemplateSettingsStubBuilder())
            ->setTemplateOverrides(['test'])
            ->create();

        $this->filesystemLoaderMock->expects($this->once())
            ->method('prependPath')
            ->with(
                sprintf(
                    '%s/resources/json_template_overrides/%s',
                    $projectDir,
                    'test',
                ),
                'themeJson',
            )
            ->willThrowException(new LoaderError('test'));

        $kernelRequestEventSubscriber = new KernelRequestEventSubscriber(
            twig                : $this->twigMock,
            jsonTemplateSettings: $jsonTemplateSettings,
            projectDir          : $projectDir,
            logger              : $this->memoryLoggerStub,
            debugRequest        : $this->debugRequestStub,
        );

        $kernelRequestEventSubscriber->addJsonTemplateOverrides();

        $expectedLog = [
            'warning' => [
                [
                    'message' => 'Unknown override group in json template configuration: {templateOverride}',
                    'context' => [
                        'templateOverride' => 'test',
                    ],
                ],
            ],
        ];
        $actualLog = $this->memoryLoggerStub->getNormalizedLogs();

        static::assertSame($expectedLog, $actualLog);
    }
}
