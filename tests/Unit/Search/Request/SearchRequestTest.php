<?php

declare(strict_types=1);

namespace Tests\Unit\Search\Request;

use App\Search\Query\SearchQueryNormalizer;
use App\Search\Request\SearchRequest;
use App\Search\Request\SearchRequestFlag;
use App\Search\Request\SearchRequestInterface;
use App\Search\Request\SearchRequestQueryHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Unit\Http\Request\AbstractRequestTestCase;

class SearchRequestTest extends AbstractRequestTestCase
{
    /**
     * @return mixed[]
     */
    public static function urlDataProvider(): array
    {
        return [
            'complete, no page, opposite default values' => [
                'url'                   => '/?q=ipad+air&pg=3&pkw=n&bkw=0&idyn=N&rkb=i&rkln=4&suggest=1&locale=nl_NL&dummy=gone',
                'expectedUrlParameters' => [
                    SearchRequestInterface::PARAMETER_QUERY => 'ipad air',
                    SearchRequestInterface::PARAMETER_PAGE  => 3,
                ],
            ],
            'deprecated query'                           => [
                'url'                   => '/?query=gehaktbal',
                'expectedUrlParameters' => [
                    SearchRequestInterface::PARAMETER_QUERY => 'gehaktbal',
                    SearchRequestInterface::PARAMETER_PAGE  => 1,
                ],
            ],
            'empty values'                               => [
                'url'                   => '/?q=%20%20%20&pg=0&pp=0&ss=&twin=&pkw=&bkw=&idyn=&rkb&rkln=0',
                'expectedUrlParameters' => [
                    SearchRequestInterface::PARAMETER_PAGE => 1,
                ],
            ],
            'default values'                             => [
                'url'                   => '/?pg=1&ss=n&twin=y&pkw=y&bkw=y&idyn=y&suggest=0',
                'expectedUrlParameters' => [
                    SearchRequestInterface::PARAMETER_PAGE => 1,
                ],
            ],
            'invalid values'                             => [
                'url'                   => '/?q=query&ss=invalid&locale=invalid',
                'expectedUrlParameters' => [
                    SearchRequestInterface::PARAMETER_QUERY => 'query',
                    SearchRequestInterface::PARAMETER_PAGE  => 1,
                ],
            ],
            'html5 entity and emoji'                     => [
                'url'                   => '/?q=query+%26apos%3B😎',
                'expectedUrlParameters' => [
                    SearchRequestInterface::PARAMETER_QUERY => 'query \'😎',
                    SearchRequestInterface::PARAMETER_PAGE  => 1,
                ],
            ],
            'none'                                       => [
                'url'                   => '/',
                'expectedUrlParameters' => [
                    SearchRequestInterface::PARAMETER_PAGE => 1,
                ],
            ],
        ];
    }

    /**
     * @param array<string, string> $expectedUrlParameters
     */
    #[DataProvider('urlDataProvider')]
    public function testUrlParameters(string $url, array $expectedUrlParameters): void
    {
        $this->setMainRequestWithUrl($url);

        $searchRequest = new SearchRequest(
            requestManager           : $this->requestManager,
            requestPropertyNormalizer: $this->requestPropertyNormalizer,
            requestInfo              : $this->requestInfo,
            searchRequestQueryHelper : new SearchRequestQueryHelper($this->requestManager),
            searchQueryNormalizer    : new SearchQueryNormalizer(),
        );

        $actualUrlParameters = $searchRequest->getUrlParameters();

        // The order of parameters is irrelevant
        ksort($expectedUrlParameters);
        ksort($actualUrlParameters);

        self::assertSame($expectedUrlParameters, $actualUrlParameters);
    }

    /**
     * @return mixed[]
     */
    public static function queryDataProvider(): array
    {
        return [
            'null'                                                  => [
                'url'            => '/',
                'expectedQuery'  => null,
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'empty'                                                 => [
                'url'            => '/?q=',
                'expectedQuery'  => null,
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'normal'                                                => [
                'url'            => '/?q=pizza',
                'expectedQuery'  => 'pizza',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'emoticon'                                              => [
                'url'            => '/?q=🍕',
                'expectedQuery'  => '🍕',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'trim'                                                  => [
                'url'            => '/?q=          pizza',
                'expectedQuery'  => 'pizza',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'with %20'                                              => [
                'url'            => '/?q=iphone%2011%20pro',
                'expectedQuery'  => 'iphone 11 pro',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'with pluses'                                           => [
                'url'            => '/?q=test+feature',
                'expectedQuery'  => 'test feature',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'with escaped pluses'                                   => [
                'url'            => '/?q=test%2bfeature',
                'expectedQuery'  => 'test feature',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'utf-8'                                                 => [
                'url'            => '/?q=ピザ',
                'expectedQuery'  => 'ピザ',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            // SERP-246
            'invalid utf-8'                                         => [
                'url'            => '/?q='.urldecode('%C2'),
                'expectedQuery'  => 'Â',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            // SERP-755
            'invalid special character'                             => [
                'url'            => '/?q='.urldecode('%FA'),
                'expectedQuery'  => 'ú',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'EUC-JP'                                                => [
                'url'            => '/?q='.mb_convert_encoding('ピザ', 'EUC-JP', 'UTF-8'),
                'expectedQuery'  => '¥Ô¥¶',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            // SERP-1407
            'null character single'                                 => [
                'url'            => '/?q=%00 %00',
                'expectedQuery'  => null,
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'null character multiple'                               => [
                'url'            => '/?q=%00pizza%00%00margherita',
                'expectedQuery'  => 'pizza margherita',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'landings page null'                                    => [
                'url'            => '/ws',
                'expectedQuery'  => null,
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'landings page empty'                                   => [
                'url'            => '/ws?q=',
                'expectedQuery'  => null,
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'landings page normal'                                  => [
                'url'            => '/ws?q=pizza',
                'expectedQuery'  => 'pizza',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'landings page emoticon'                                => [
                'url'            => '/ws?q=🍕',
                'expectedQuery'  => '🍕',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'landings page trim'                                    => [
                'url'            => '/ws?q=          pizza',
                'expectedQuery'  => 'pizza',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with %20'                                => [
                'url'            => '/ws?q=iphone%2011%20pro',
                'expectedQuery'  => 'iphone 11 pro',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with pluses'                             => [
                'url'            => '/ws?q=test+feature',
                'expectedQuery'  => 'test feature',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with escaped pluses'                     => [
                'url'            => '/ws?q=test%2bfeature',
                'expectedQuery'  => 'test feature',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Search using advanced search page with escaped pluses' => [
                'url'            => '/search?q=%2Btest%20%2Bfeature',
                'expectedQuery'  => 'test feature',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'Search using advanced search page with pluses'         => [
                'url'            => '/search?q=+test +feature',
                'expectedQuery'  => 'test feature',
                'isLandingsPage' => false,
                'isQueryInPath'  => false,
            ],
            'landings page utf-8'                                   => [
                'url'            => '/ws?q=ピザ',
                'expectedQuery'  => 'ピザ',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            // SERP-246
            'landings page invalid utf-8'                           => [
                'url'            => '/ws?q='.urldecode('%C2'),
                'expectedQuery'  => 'Â',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            // SERP-755
            'landings page invalid special character'               => [
                'url'            => '/ws?q='.urldecode('%FA'),
                'expectedQuery'  => 'ú',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'landings page EUC-JP'                                  => [
                'url'            => '/ws?q='.mb_convert_encoding('ピザ', 'EUC-JP', 'UTF-8'),
                'expectedQuery'  => '¥Ô¥¶',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            // SERP-1407
            'landings page null character single'                   => [
                'url'            => '/ws?q=%00 %00',
                'expectedQuery'  => null,
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'landings page null character multiple'                 => [
                'url'            => '/ws?q=%00pizza%00%00margherita',
                'expectedQuery'  => 'pizza margherita',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'landings page keyword and url'                         => [
                'url'            => '/ws?q={keyword:tablet}{_url}',
                'expectedQuery'  => 'tablet',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'landings page keyword in caps and url'                 => [
                'url'            => '/ws?q={KEYWORD:tablet}{_url}',
                'expectedQuery'  => 'tablet',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with url bracket'                        => [
                'url'            => '/ws?q=iphone{_url}',
                'expectedQuery'  => 'iphone',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with escape url bracket'                 => [
                'url'            => '/ws?q=iphone%7B_url%7D',
                'expectedQuery'  => 'iphone',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with url and %20'                        => [
                'url'            => '/ws?q=fm%20radio%20south%20africa{_url}',
                'expectedQuery'  => 'fm radio south africa',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with double encoded space %2520'         => [
                'url'            => '/ws?q=imprumuturi%2520transilvania',
                'expectedQuery'  => 'imprumuturi transilvania',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with bing ads'                           => [
                'url'            => '/ws?q=test[]',
                'expectedQuery'  => 'test',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'Landings page with brackets'                           => [
                'url'            => '/ws?q={mustache}',
                'expectedQuery'  => '{mustache}',
                'isLandingsPage' => true,
                'isQueryInPath'  => false,
            ],
            'seo landing page null'                                 => [
                'url'            => '/mk/ /',
                'expectedQuery'  => null,
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page empty'                                => [
                'url'            => '/mk//',
                'expectedQuery'  => 'mk',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page normal'                               => [
                'url'            => '/mk/pizza',
                'expectedQuery'  => 'pizza',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page emoticon'                             => [
                'url'            => '/mk/🍕',
                'expectedQuery'  => '🍕',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page trim'                                 => [
                'url'            => '/mk/          pizza',
                'expectedQuery'  => 'pizza',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page with %20'                             => [
                'url'            => '/mk/iphone%2011%20pro',
                'expectedQuery'  => 'iphone 11 pro',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page with pluses'                          => [
                'url'            => '/mk/test+feature',
                'expectedQuery'  => 'test feature',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page with escaped pluses'                  => [
                'url'            => '/mk/test%2bfeature',
                'expectedQuery'  => 'test feature',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page utf-8'                                => [
                'url'            => '/mk/ピザ',
                'expectedQuery'  => 'ピザ',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            // SERP-246
            'seo landing page invalid utf-8'                        => [
                'url'            => '/mk/'.urldecode('%C2'),
                'expectedQuery'  => 'Â',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            // SERP-755
            'seo landing page invalid special character'            => [
                'url'            => '/mk/'.urldecode('%FA'),
                'expectedQuery'  => 'ú',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page EUC-JP'                               => [
                'url'            => '/mk/'.mb_convert_encoding('ピザ', 'EUC-JP', 'UTF-8'),
                'expectedQuery'  => '¥Ô¥¶',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            // SERP-1407
            'seo landing page null character single'                => [
                'url'            => '/mk/%00 %00',
                'expectedQuery'  => null,
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
            'seo landing page null character multiple'              => [
                'url'            => '/mk/%00pizza%00%00margherita',
                'expectedQuery'  => 'pizza margherita',
                'isLandingsPage' => false,
                'isQueryInPath'  => true,
            ],
        ];
    }

    #[DataProvider('queryDataProvider')]
    public function testQueryParameter(
        string $url,
        ?string $expectedQuery,
        bool $isLandingsPage,
        bool $isQueryInPath
    ): void
    {
        $request = $this->setMainRequestWithUrl($url);
        $request->attributes->set(SearchRequestFlag::IS_LANDING_PAGE, $isLandingsPage);
        $request->attributes->set(SearchRequestFlag::QUERY_IN_PATH, $isQueryInPath);

        if ($isQueryInPath) {
            $request->attributes->set(SearchRequestInterface::ATTRIBUTE_QUERY, $expectedQuery);
        }

        $searchRequestQueryHelper = new SearchRequestQueryHelper(
            requestManager: $this->requestManager,
        );
        $searchRequest = new SearchRequest(
            requestManager           : $this->requestManager,
            requestPropertyNormalizer: $this->requestPropertyNormalizer,
            requestInfo              : $this->requestInfo,
            searchRequestQueryHelper : $searchRequestQueryHelper,
            searchQueryNormalizer    : new SearchQueryNormalizer(),
        );

        self::assertSame($expectedQuery, $searchRequest->getQuery());
        self::assertSame((string)$expectedQuery, $searchRequest->getQueryAsString());
    }
}
