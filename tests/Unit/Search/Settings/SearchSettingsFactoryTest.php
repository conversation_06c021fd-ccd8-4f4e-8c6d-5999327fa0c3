<?php

declare(strict_types=1);

namespace Tests\Unit\Search\Settings;

use App\Search\Settings\SearchSettingsFactory;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Unit\Module\AbstractModuleSettingsFactoryTestCase;

final class SearchSettingsFactoryTest extends AbstractModuleSettingsFactoryTestCase
{
    private SearchSettingsFactory $searchSettingsFactory;

    private DebugRequestStub $debugRequestStub;

    protected function setUp(): void
    {
        parent::setUp();

        $this->debugRequestStub = new DebugRequestStub();

        $this->searchSettingsFactory = new SearchSettingsFactory(
            $this->websiteConfigurationHelperMock,
            $this->debugRequestStub,
        );
    }

    public function testDisabled(): void
    {
        $this->setBrandConfig(
            [
                'search' => [
                    'enabled' => false,
                ],
            ],
        );
        $this->debugRequestStub->setEnableModule(false);

        $searchSettings = $this->searchSettingsFactory->create();

        self::assertFalse($searchSettings->enabled);
        self::assertFalse($searchSettings->seoEnabled);
    }

    public function testEnabled(): void
    {
        $this->setBrandConfig(
            [
                'search' => [
                    'enabled'          => true,
                    'seo_enabled'      => true,
                    'style_id_desktop' => 123,
                    'style_id_mobile'  => 456,
                ],
            ],
        );

        $searchSettings = $this->searchSettingsFactory->create();

        self::assertTrue($searchSettings->enabled);
        self::assertTrue($searchSettings->seoEnabled);
    }
}
