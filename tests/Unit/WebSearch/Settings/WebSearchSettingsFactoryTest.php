<?php

declare(strict_types=1);

namespace Tests\Unit\WebSearch\Settings;

use App\WebSearch\Settings\WebSearchSettingsFactory;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Unit\Module\AbstractModuleSettingsFactoryTestCase;

final class WebSearchSettingsFactoryTest extends AbstractModuleSettingsFactoryTestCase
{
    private WebSearchSettingsFactory $webSearchSettingsFactory;

    private DebugRequestStub $debugRequestStub;

    protected function setUp(): void
    {
        parent::setUp();

        $this->debugRequestStub = new DebugRequestStub();

        $this->webSearchSettingsFactory = new WebSearchSettingsFactory(
            $this->websiteConfigurationHelperMock,
            $this->debugRequestStub,
        );
    }

    public function testDisabled(): void
    {
        $this->setBrandConfig(
            [
                'web_search' => [
                    'enabled' => false,
                ],
            ],
        );
        $this->debugRequestStub->setEnableModule(false);

        $webSearchSettings = $this->webSearchSettingsFactory->create();

        self::assertFalse($webSearchSettings->enabled);
    }

    public function testEnabled(): void
    {
        $this->setBrandConfig(
            [
                'web_search' => [
                    'enabled'          => true,
                    'style_id_desktop' => 123,
                    'style_id_mobile'  => 456,
                ],
            ],
        );

        $webSearchSettings = $this->webSearchSettingsFactory->create();

        self::assertTrue($webSearchSettings->enabled);
    }
}
