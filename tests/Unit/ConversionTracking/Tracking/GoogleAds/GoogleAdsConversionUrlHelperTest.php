<?php /** @noinspection LongLine */

declare(strict_types=1);

namespace Tests\Unit\ConversionTracking\Tracking\GoogleAds;

use App\ConversionTracking\Tracking\GoogleAds\GoogleAdsConversionUrlHelper;
use App\Tracking\Model\ClickId\Factory\ClickIdFactory;
use PHPUnit\Framework\Attributes\DataProvider;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

/**
 * @phpcs:disable Generic.Files.LineLength.MaxExceeded
 */
class GoogleAdsConversionUrlHelperTest extends PhpUnitTestCase
{
    private GoogleAdsConversionUrlHelper $googleAdsConversionUrlHelper;

    private ClickIdFactory $clickIdFactory;

    protected function setUp(): void
    {
        $this->googleAdsConversionUrlHelper = new GoogleAdsConversionUrlHelper();
        $this->clickIdFactory = new ClickIdFactory();
    }

    /**
     * @return mixed[]
     */
    public static function isGoogleAdServicesUrlDataProvider(): array
    {
        return [
            'secure google ad conversion url'   => [
                'url'                         => 'https://www.googleadservices.com/pagead/conversion/1234/'.
                                                 '?guid=ON&script=0&label=tracking-label&gclaw=combined-click-id',
                'expectedGoogleAdServicesUrl' => true,
            ],
            'insecure google ad conversion url' => [
                'url'                         => 'http://www.googleadservices.com/pagead/conversion/1234/'.
                                                 '?guid=ON&script=0&label=tracking-label&gclaw=combined-click-id',
                'expectedGoogleAdServicesUrl' => false,
            ],
            'bing conversion url'               => [
                'url'                         => 'https://bat.bing.com/action/0?ti=0&Ver=2&evt=custom&transaction_id=order-id-123&ev=1',
                'expectedGoogleAdServicesUrl' => false,
            ],
            'empty url'                         => [
                'url'                         => '',
                'expectedGoogleAdServicesUrl' => false,
            ],
        ];
    }

    #[DataProvider('isGoogleAdServicesUrlDataProvider')]
    public function testIsGoogleAdServicesUrl(string $url, bool $expectedGoogleAdServicesUrl): void
    {
        self::assertSame(
            $expectedGoogleAdServicesUrl,
            $this->googleAdsConversionUrlHelper->isGoogleAdServicesUrl($url),
        );
    }

    /**
     * @return mixed[]
     */
    public static function getGoogleConversionTagParametersDataProvider(): array
    {
        $clickId = uuid_create(UUID_TYPE_RANDOM);

        return [
            'null'                      => [
                'clickId'            => null,
                'clickIdSource'      => null,
                'expectedParameters' => [],
            ],
            'google click id'           => [
                'clickId'            => $clickId,
                'clickIdSource'      => 'gclid',
                'expectedParameters' => [
                    'gclaw' => $clickId,
                ],
            ],
            'empty google click id'     => [
                'clickId'            => null,
                'clickIdSource'      => 'gclid',
                'expectedParameters' => [],
            ],
            'google app measurement id' => [
                'clickId'            => $clickId,
                'clickIdSource'      => 'gbraid',
                'expectedParameters' => [
                    'gclgb' => $clickId,
                ],
            ],
            'empty app measurement id'  => [
                'clickId'            => null,
                'clickIdSource'      => 'gbraid',
                'expectedParameters' => [],
            ],
            'google web measurement id' => [
                'clickId'            => $clickId,
                'clickIdSource'      => 'wbraid',
                'expectedParameters' => [
                    'gclgb' => $clickId,
                ],
            ],
            'empty web measurement id'  => [
                'clickId'            => null,
                'clickIdSource'      => 'wbraid',
                'expectedParameters' => [],
            ],
            'microsoft click id'        => [
                'clickId'            => '1234',
                'clickIdSource'      => 'msclkid',
                'expectedParameters' => [],
            ],
        ];
    }

    /**
     * @param array<string, string> $expectedParameters
     */
    #[DataProvider('getGoogleConversionTagParametersDataProvider')]
    public function testGetGoogleConversionTagParameters(
        ?string $clickId,
        ?string $clickIdSource,
        ?array $expectedParameters
    ): void
    {
        $array = $this->googleAdsConversionUrlHelper->getGoogleConversionTagParameters(
            $this->clickIdFactory->create((string)$clickId, (string)$clickIdSource),
        );

        self::assertSame($expectedParameters, $array);
    }
}
