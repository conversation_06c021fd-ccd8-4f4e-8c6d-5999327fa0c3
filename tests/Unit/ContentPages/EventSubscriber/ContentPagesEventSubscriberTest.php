<?php

declare(strict_types=1);

namespace Tests\Unit\ContentPages\EventSubscriber;

use App\ContentPages\EventSubscriber\ContentPagesEventSubscriber;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\ContentPage\Request\ContentPageRequestStub;

final class ContentPagesEventSubscriberTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function excludedPublicIdDataProvider(): array
    {
        return [
            'with public id' => [
                'publicId' => 123,
                'previousPublicId' => null,
                'expectedExcludedIds' => [123],
            ],
            'with previous public id' => [
                'publicId' => null,
                'previousPublicId' => 456,
                'expectedExcludedIds' => [456],
            ],
            'with both ids, public id takes precedence' => [
                'publicId' => 123,
                'previousPublicId' => 456,
                'expectedExcludedIds' => [123],
            ],
            'with no ids' => [
                'publicId' => null,
                'previousPublicId' => null,
                'expectedExcludedIds' => null,
            ],
        ];
    }

    /**
     * @param ?array<int> $expectedExcludedIds
     */
    #[DataProvider('excludedPublicIdDataProvider')]
    public function testOnJsonTemplateViewCreatedAddsExcludedPublicId(
        ?int $publicId,
        ?int $previousPublicId,
        ?array $expectedExcludedIds
    ): void {
        $contentPageRequest = new ContentPageRequestStub();
        $contentPageRequest->setPublicId($publicId);
        $contentPageRequest->setPreviousPublicId($previousPublicId);

        $dataRequest = new ViewDataRequest();

        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn($dataRequest);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new ContentPagesEventSubscriber($contentPageRequest);
        $subscriber->onJsonTemplateViewCreated($event);

        self::assertSame($expectedExcludedIds, $dataRequest->contentPages()->getExcludedPublicIds());
    }
}
