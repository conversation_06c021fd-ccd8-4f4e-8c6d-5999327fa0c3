<?php

declare(strict_types=1);

namespace Tests\Unit\Debug\Controller;

use App\Debug\Controller\DebugJsonTemplatePreviewController;
use App\Debug\Service\JsonTemplatePreviewRenderer;
use App\Search\Registry\RouteRegistryInterface;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;
use Tests\Stub\Debug\Request\JsonTemplatePreviewRequestStub;

class DebugJsonTemplatePreviewControllerTest extends TestCase
{
    public function testPreviewReturnsRenderedTemplate(): void
    {
        $controller = new DebugJsonTemplatePreviewController();
        $expectedResponse = new Response('rendered content');
        $jsonTemplate = ['key' => 'value'];

        $request = new JsonTemplatePreviewRequestStub();
        $request->setJsonTemplate($jsonTemplate);

        $jsonTemplatePreviewRendererMock = $this->createMock(JsonTemplatePreviewRenderer::class);

        $jsonTemplatePreviewRendererMock
            ->expects($this->once())
            ->method('renderPreview')
            ->with($jsonTemplate)
            ->willReturn($expectedResponse);

        $response = $controller->preview(
            $request,
            $jsonTemplatePreviewRendererMock,
            $this->createMock(RouteRegistryInterface::class),
        );

        self::assertSame($expectedResponse, $response);
    }
}
