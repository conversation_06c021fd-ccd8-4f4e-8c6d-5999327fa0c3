<?php

declare(strict_types=1);

namespace Tests\Unit\Debug\EventSubscriber;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\EventSubscriber\InjectDebugInfoEventSubscriber;
use App\Debug\Helper\DebugHelper;
use App\Debug\Info\DebugInfoInterface;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\Office\Request\OfficeRequestStub;
use Twig\Environment;

final class InjectDebugInfoEventSubscriberTest extends TestCase
{
    private DebugRequestStub $debugRequestStub;

    private OfficeRequestStub $officeRequestStub;

    private Environment & MockObject $twigMock;

    private DebugInfoProviderInterface & MockObject $debugInfoProviderMock;

    private InjectDebugInfoEventSubscriber $subscriber;

    protected function setUp(): void
    {
        $this->debugRequestStub = new DebugRequestStub();
        $this->officeRequestStub = new OfficeRequestStub();
        $this->twigMock = $this->createMock(Environment::class);
        $this->debugInfoProviderMock = $this->createMock(DebugInfoProviderInterface::class);

        $this->subscriber = new InjectDebugInfoEventSubscriber(
            $this->twigMock,
            $this->officeRequestStub,
            $this->debugRequestStub,
            new DebugHelper($this->debugRequestStub, $this->officeRequestStub),
            [$this->debugInfoProviderMock],
        );
    }

    public function testGetSubscribedEvents(): void
    {
        $events = InjectDebugInfoEventSubscriber::getSubscribedEvents();

        self::assertArrayHasKey('template.render_headers', $events);
        self::assertArrayHasKey('template.render_footers', $events);
        self::assertArrayHasKey('kernel.response.no_redirect', $events);
    }

    public function testInjectDebugDataWhenNotOffice(): void
    {
        $this->officeRequestStub->setIsOffice(false);

        $event = new RenderTemplateHeadersEvent();
        $this->subscriber->injectDebugData($event);

        self::assertCount(0, $event->getItems());
    }

    public function testInjectDebugDataWhenOffice(): void
    {
        $this->officeRequestStub->setIsOffice(true);
        $this->twigMock->expects(self::once())
            ->method('render')
            ->with('@theme/debug/debug_data.html.twig')
            ->willReturn('<debug-data>');

        $event = new RenderTemplateHeadersEvent();
        $this->subscriber->injectDebugData($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('<debug-data>', $event->getItems()[0]);
    }

    public function testInjectDebugInfoWhenDisabled(): void
    {
        $this->debugRequestStub->setDebugInfo(false);

        $event = new RenderTemplateFootersEvent();
        $this->subscriber->injectDebugInfo($event);

        self::assertCount(0, $event->getItems());
    }

    public function testInjectDebugInfoWhenEnabled(): void
    {
        $this->debugRequestStub->setDebugInfo(true);

        $event = new RenderTemplateFootersEvent();
        $this->subscriber->injectDebugInfo($event);

        self::assertCount(1, $event->getItems());
        self::assertSame(InjectDebugInfoEventSubscriber::DEBUG_INFO_HTML_PLACEHOLDER, $event->getItems()[0]);
    }

    public function testOnResponseWhenDebugInfoDisabled(): void
    {
        $this->debugRequestStub->setDebugInfo(false);
        $this->debugInfoProviderMock->expects(self::never())->method('getDebugInfo');

        $response = new Response('content');
        $event = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );

        $this->subscriber->onResponse($event);

        self::assertSame('content', $response->getContent());
    }

    public function testOnResponseWhenDebugInfoEnabled(): void
    {
        $this->debugRequestStub->setDebugInfo(true);

        $debugInfo = $this->createMock(DebugInfoInterface::class);
        $debugInfo->expects(self::once())
            ->method('output')
            ->with($this->twigMock)
            ->willReturn('<debug-output>');

        $this->debugInfoProviderMock->expects(self::once())
            ->method('getDebugInfo')
            ->willReturn([$debugInfo]);

        $response = new Response(InjectDebugInfoEventSubscriber::DEBUG_INFO_HTML_PLACEHOLDER);
        $event = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );

        $this->subscriber->onResponse($event);

        self::assertSame('<debug-output>', $response->getContent());
    }

    public function testGetKeys(): void
    {
        $this->debugInfoProviderMock->expects(self::once())
            ->method('getKeys')
            ->willReturn(['key1', 'key2']);

        $keys = $this->subscriber->getKeys();

        self::assertSame(['key1', 'key2'], $keys);
    }
}
