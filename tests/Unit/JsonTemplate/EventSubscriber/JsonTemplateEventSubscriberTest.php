<?php

declare(strict_types=1);

namespace Tests\Unit\JsonTemplate\EventSubscriber;

use App\JsonTemplate\EventSubscriber\JsonTemplateEventSubscriber;
use App\JsonTemplate\Template\JsonTemplate;
use App\JsonTemplate\Template\Options\JsonTemplateOptions;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class JsonTemplateEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedSetsHeader(): void
    {
        $projectDir = __DIR__;
        $filePath = sprintf('%s/resources/shared/templates_json/article/article.json', $projectDir);
        $expectedHeader = '/resources/shared/templates_json/article/article.json';
        $jsonTemplateOptions = new JsonTemplateOptions(
            keywordHighlight       : null,
            organicKeywordHighlight: null,
            organicLinkType        : null,
            pageHeadTagsType       : 'article',
        );

        $jsonTemplate = new JsonTemplate(
            filePath       : $filePath,
            variant        : null,
            options        : $jsonTemplateOptions,
            layoutTemplate : '@theme/layout_default_components.html.twig',
            componentConfig: [],
        );

        $response = new Response();
        $viewMock = $this->createMock(ViewInterface::class);
        $viewMock->method('getResponse')->willReturn($response);
        $viewMock->method('getJsonTemplate')->willReturn($jsonTemplate);

        $event = new JsonTemplateViewCreatedEvent($viewMock);
        $subscriber = new JsonTemplateEventSubscriber($projectDir);
        $subscriber->onJsonTemplateViewCreated($event);

        self::assertSame($expectedHeader, $response->headers->get(
            JsonTemplateEventSubscriber::HEADER_X_JSON_TEMPLATE_FILE
        ));
    }
}
