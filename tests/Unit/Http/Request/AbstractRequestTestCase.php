<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Request;

use App\AdBot\Request\AdBotRequestInterface;
use App\Http\Request\FlagBag\RequestFlagBagFactory;
use App\Http\Request\Info\RequestInfo;
use App\Http\Request\Main\MainRequest;
use App\Http\Request\Manager\RequestManager;
use App\Http\Request\Normalizer\RequestPropertyNormalizer;
use App\Http\Request\ParameterBag\RequestHeaderParameterBagFactory;
use App\Http\Request\ParameterBag\RequestParameterBagFactory;
use App\Http\Url\DevelopHostHelper;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

abstract class AbstractRequestTestCase extends PhpUnitTestCase
{
    protected MockObject & RequestStack $requestStackMock;

    protected MockObject & LoggerInterface $loggerMock;

    protected MainRequest $mainRequest;

    protected RequestManager $requestManager;

    protected RequestPropertyNormalizer $requestPropertyNormalizer;

    protected RequestInfo $requestInfo;

    protected function setUp(): void
    {
        $this->requestStackMock = $this->createMock(RequestStack::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->mainRequest = new MainRequest($this->requestStackMock);
        $this->requestManager = new RequestManager(
            mainRequest                     : $this->mainRequest,
            requestParameterBagFactory      : new RequestParameterBagFactory($this->loggerMock),
            requestHeaderParameterBagFactory: new RequestHeaderParameterBagFactory($this->loggerMock),
            requestFlagBagFactory           : new RequestFlagBagFactory(),
        );
        $this->requestPropertyNormalizer = new RequestPropertyNormalizer();
        $this->requestInfo = new RequestInfo(
            requestManager           : $this->requestManager,
            requestPropertyNormalizer: $this->requestPropertyNormalizer,
            mainRequest              : $this->mainRequest,
            developHostHelper        : new DevelopHostHelper(null),
        );
    }

    protected function setMainRequestWithUrl(string $url, bool $adBot = false, ?string $route = null): Request
    {
        $request = $this->createRequestWithUrl($url, $adBot, $route);

        $this->requestStackMock
            ->method('getMainRequest')
            ->willReturn($request);

        return $request;
    }

    protected function createRequestWithUrl(string $url, bool $adBot = false, ?string $route = null): Request
    {
        $request = Request::create($url);
        $request->headers->set(AdBotRequestInterface::HEADER_X_LOADBALANCER_IS_AD_BOT, $adBot ? '1' : '0');
        $request->headers->set('X-Forwarded-For', '127.0.0.1');

        if ($route === null) {
            /** @var string $path */
            $path = parse_url($url, PHP_URL_PATH);

            // Only use first part, for example only `/mk` for /mk/query/`
            $pos = mb_strpos($path, '/', 1);

            if ($pos !== false) {
                $path = mb_substr($path, 0, $pos);
            }

            $route = match ($path) {
                '/ws'   => 'route_web_search',
                '/wsa'  => 'route_web_search_advertised',
                '/mk'   => 'route_search_seo_mk',
                default => 'route_search',
            };
        }

        $request->attributes->set('_route', $route);

        return $request;
    }
}
