<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Response\EventSubscriber;

use App\Http\Response\EventSubscriber\SplitTestHeadersEventSubscriber;
use App\SplitTest\SplitTestExtendedReaderInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;

class SplitTestHeadersEventSubscriberTest extends TestCase
{
    private SplitTestExtendedReaderInterface & MockObject $splitTestExtendedReaderMock;

    private SplitTestHeadersEventSubscriber $subscriber;

    protected function setUp(): void
    {
        $this->splitTestExtendedReaderMock = $this->createMock(SplitTestExtendedReaderInterface::class);

        $this->subscriber = new SplitTestHeadersEventSubscriber(
            $this->splitTestExtendedReaderMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function splitTestDataProvider(): array
    {
        return [
            'with id and variant'   => [
                'id'                    => 123,
                'variant'               => 'test-variant',
                'expectedIdHeader'      => '123',
                'expectedVariantHeader' => 'test-variant',
            ],
            'with id only'          => [
                'id'                    => 456,
                'variant'               => null,
                'expectedIdHeader'      => '456',
                'expectedVariantHeader' => null,
            ],
            'with variant only'     => [
                'id'                    => null,
                'variant'               => 'variant-only',
                'expectedIdHeader'      => null,
                'expectedVariantHeader' => 'variant-only',
            ],
            'with no id or variant' => [
                'id'                    => null,
                'variant'               => null,
                'expectedIdHeader'      => null,
                'expectedVariantHeader' => null,
            ],
        ];
    }

    #[DataProvider('splitTestDataProvider')]
    public function testSetSplitTestHeadersResponse(
        ?int $id,
        ?string $variant,
        ?string $expectedIdHeader,
        ?string $expectedVariantHeader
    ): void
    {
        $this->splitTestExtendedReaderMock
            ->method('getId')
            ->willReturn($id);

        $this->splitTestExtendedReaderMock
            ->method('getVariant')
            ->willReturn($variant);

        $response = new Response();
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->setSplitTestHeadersResponse($responseEvent);

        if ($expectedIdHeader === null) {
            self::assertFalse($response->headers->has('X-Log-Split_Test_Id'));
        } else {
            self::assertSame($expectedIdHeader, $response->headers->get('X-Log-Split_Test_Id'));
        }

        if ($expectedVariantHeader === null) {
            self::assertFalse($response->headers->has('X-Log-Split_Test_Variant'));
        } else {
            self::assertSame($expectedVariantHeader, $response->headers->get('X-Log-Split_Test_Variant'));
        }
    }

    private function createResponseEvent(Response $response): ResponseEvent
    {
        return new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );
    }
}
