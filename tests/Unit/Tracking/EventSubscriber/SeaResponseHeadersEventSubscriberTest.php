<?php

declare(strict_types=1);

namespace Tests\Unit\Tracking\EventSubscriber;

use App\Brand\Settings\BrandSettingsHelper;
use App\Generic\Device\Device;
use App\Tracking\EventSubscriber\SeaResponseHeadersEventSubscriber;
use App\Tracking\Helper\TrafficHelper;
use App\Tracking\Model\Network;
use App\Tracking\Model\TrafficType;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Stub\Brand\Settings\BrandSettingsStub;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;
use Tests\Stub\Tracking\Request\SeaRequestStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;

class SeaResponseHeadersEventSubscriberTest extends TestCase
{
    private TrafficHelper & MockObject $trafficHelperMock;

    private BrandSettingsHelper & MockObject $brandSettingsHelperMock;

    private WebsiteSettingsHelper & MockObject $websiteSettingsHelperMock;

    private ActiveTrackingEntryHelperStub $activeTrackingEntryHelperStub;

    private SeaResponseHeadersEventSubscriber $subscriber;

    private SeaRequestStub $seaRequestStub;

    protected function setUp(): void
    {
        $this->seaRequestStub = new SeaRequestStub();
        $this->activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
        $this->brandSettingsHelperMock = $this->createMock(BrandSettingsHelper::class);
        $this->trafficHelperMock = $this->createMock(TrafficHelper::class);
        $this->websiteSettingsHelperMock = $this->createMock(WebsiteSettingsHelper::class);

        $this->subscriber = new SeaResponseHeadersEventSubscriber(
            $this->activeTrackingEntryHelperStub,
            $this->trafficHelperMock,
            $this->brandSettingsHelperMock,
            $this->websiteSettingsHelperMock,
            $this->seaRequestStub,
        );
    }

    public function testOnResponseWithEmptyTrackingEntry(): void
    {
        $trackingEntry = (new TrackingEntryStubBuilder())
            ->clear()
            ->setIsEmpty(true)
            ->create();

        $this->activeTrackingEntryHelperStub
            ->setActiveTrackingEntry($trackingEntry);

        $this->seaRequestStub
            ->clear()
            ->setNetwork(Network::YOUTUBE);

        $brandSettings = new BrandSettingsStub();
        $brandSettings->setPartnerSlug('test-partner');

        $this->brandSettingsHelperMock
            ->method('getSettings')
            ->willReturn($brandSettings);

        $response = new Response();
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->onResponse($responseEvent);

        self::assertTrue($response->headers->has('X-Log-Network'));
        self::assertTrue($response->headers->has('X-Log-Partner_Slug'));
        self::assertFalse($response->headers->has('X-Log-Account_Id'));
        self::assertFalse($response->headers->has('X-Log-Campaign_Id'));
        self::assertFalse($response->headers->has('X-Log-Campaign_Name'));
        self::assertFalse($response->headers->has('X-Log-Publisher'));
        self::assertFalse($response->headers->has('X-Log-Landing_Page_Query'));
        self::assertFalse($response->headers->has('X-Log-Traffic_Type'));
        self::assertFalse($response->headers->has('X-Log-Device'));
    }

    public function testOnResponseWithFilledTrackingEntry(): void
    {
        $trackingEntry = (new TrackingEntryStubBuilder())
            ->clear()
            ->setIsEmpty(false)
            ->setQuery('test query')
            ->setAccountId(12345)
            ->setCampaignId(67890)
            ->setCampaignName('Test Campaign')
            ->setDevice(Device::MOBILE)
            ->setPublisher('TestPublisher')
            ->create();

        $this->activeTrackingEntryHelperStub
            ->setActiveTrackingEntry($trackingEntry);

        $this->trafficHelperMock
            ->method('getTrafficType')
            ->willReturn(TrafficType::ORGANIC);

        $brandSettings = new BrandSettingsStub();
        $brandSettings->setPartnerSlug('test-partner');

        $this->brandSettingsHelperMock
            ->method('getSettings')
            ->willReturn($brandSettings);

        $response = new Response();
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->onResponse($responseEvent);

        self::assertSame('12345', $response->headers->get('X-Log-Account_Id'));
        self::assertSame('67890', $response->headers->get('X-Log-Campaign_Id'));
        self::assertSame('Test Campaign', $response->headers->get('X-Log-Campaign_Name'));
        self::assertSame('TestPublisher', $response->headers->get('X-Log-Publisher'));
        self::assertSame('test%20query', $response->headers->get('X-Log-Landing_Page_Query'));
        self::assertSame('test-partner', $response->headers->get('X-Log-Partner_Slug'));
        self::assertSame('organic', $response->headers->get('X-Log-Traffic_Type'));
        self::assertSame('mobile', $response->headers->get('X-Log-Device'));
    }

    /**
     * @return array<string,array<string,mixed>>
     */
    public static function googleAdSenseContractTypeProvider(): array
    {
        return [
            'online_contract'  => [
                'enabled'        => true,
                'contractType'   => ContractType::ONLINE,
                'expectedHeader' => 'online',
            ],
            'offline_contract' => [
                'enabled'        => true,
                'contractType'   => ContractType::DIRECT,
                'expectedHeader' => 'direct',
            ],
            'disabled_adsense' => [
                'enabled'        => false,
                'contractType'   => ContractType::ONLINE,
                'expectedHeader' => null,
            ],
        ];
    }

    #[DataProvider('googleAdSenseContractTypeProvider')]
    public function testSetGoogleAdSenseContractTypeHeader(
        bool $enabled,
        ContractType $contractType,
        ?string $expectedHeader
    ): void
    {
        $trackingEntry = (new TrackingEntryStubBuilder())
            ->clear()
            ->setIsEmpty(false)
            ->setQuery('test query')
            ->create();

        $this->activeTrackingEntryHelperStub
            ->setActiveTrackingEntry($trackingEntry);

        // Set up website settings
        $websiteSettings = new WebsiteSettingsStub();
        $websiteSettings->getGoogleAdSense()
            ->setEnabled($enabled)
            ->setContractType($contractType);

        $this->websiteSettingsHelperMock
            ->method('getSettings')
            ->willReturn($websiteSettings);

        $response = new Response();
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->setGoogleAdSenseContractTypeHeader($responseEvent);

        if ($expectedHeader === null) {
            self::assertFalse($response->headers->has('X-Log-Google_AdSense_Contract_Type'));
        } else {
            self::assertSame($expectedHeader, $response->headers->get('X-Log-Google_AdSense_Contract_Type'));
        }
    }

    public function testSetGoogleAdSenseContractTypeHeaderWithEmptyTrackingEntry(): void
    {
        $trackingEntry = (new TrackingEntryStubBuilder())
            ->setIsEmpty(true)
            ->create();

        $this->activeTrackingEntryHelperStub
            ->setActiveTrackingEntry($trackingEntry);

        $response = new Response();
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->setGoogleAdSenseContractTypeHeader($responseEvent);

        self::assertFalse($response->headers->has('X-Log-Google_AdSense_Contract_Type'));
    }

    /**
     * @return mixed[]
     */
    public static function networkDataProvider(): array
    {
        return [
            'network from tracking entry' => [
                'trackingEntryNetwork' => Network::GOOGLE_SEARCH,
                'seaRequestNetwork'    => null,
                'expectedHeader'       => 'g',
            ],
            'network from sea request'    => [
                'trackingEntryNetwork' => null,
                'seaRequestNetwork'    => Network::MICROSOFT_OWNED,
                'expectedHeader'       => 'o',
            ],
            'no network'                  => [
                'trackingEntryNetwork' => null,
                'seaRequestNetwork'    => null,
                'expectedHeader'       => null,
            ],
        ];
    }

    #[DataProvider('networkDataProvider')]
    public function testOnResponse(
        ?Network $trackingEntryNetwork,
        ?Network $seaRequestNetwork,
        ?string $expectedHeader
    ): void
    {
        $builder = (new TrackingEntryStubBuilder())
            ->clear()
            ->setQuery('test query')
            ->setDevice(Device::DESKTOP);

        if ($trackingEntryNetwork !== null) {
            $builder->setNetwork($trackingEntryNetwork);
        }

        $trackingEntry = $builder->create();

        $this->activeTrackingEntryHelperStub
            ->setActiveTrackingEntry($trackingEntry);

        $this->seaRequestStub
            ->setNetwork($seaRequestNetwork);

        $this->trafficHelperMock
            ->method('getTrafficType')
            ->willReturn(TrafficType::ORGANIC);

        $response = new Response();
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->onResponse($responseEvent);

        if ($expectedHeader === null) {
            self::assertFalse($response->headers->has('X-Log-Network'));
        } else {
            self::assertSame($expectedHeader, $response->headers->get('X-Log-Network'));
        }
    }

    private function createResponseEvent(Response $response): ResponseEvent
    {
        return new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );
    }
}
