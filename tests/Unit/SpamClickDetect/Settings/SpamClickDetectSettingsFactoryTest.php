<?php

declare(strict_types=1);

namespace Tests\Unit\SpamClickDetect\Settings;

use App\SpamClickDetect\Settings\SpamClickDetectSettingsFactory;
use Tests\Stub\Http\Request\Info\RequestInfoStub;
use Tests\Unit\Module\AbstractModuleSettingsFactoryTestCase;

final class SpamClickDetectSettingsFactoryTest extends AbstractModuleSettingsFactoryTestCase
{
    private RequestInfoStub $requestInfoStub;

    private SpamClickDetectSettingsFactory $spamClickDetectSettingsFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->requestInfoStub = new RequestInfoStub();
        $this->spamClickDetectSettingsFactory = new SpamClickDetectSettingsFactory(
            $this->websiteConfigurationHelperMock,
            $this->requestInfoStub,
        );
    }

    public function testDisabled(): void
    {
        $this->setBrandConfig([]);

        $spamClickDetectSettings = $this->spamClickDetectSettingsFactory->create();

        self::assertFalse($spamClickDetectSettings->enabled);
    }

    public function testEnabled(): void
    {
        $this->setBrandConfig(
            [
                'spam_click_detect' => [
                    'enabled' => true,
                    'routes'  => ['route1', 'route2'],
                ],
            ],
        );

        $this->requestInfoStub->setRoute('route2');
        $spamClickDetectSettings = $this->spamClickDetectSettingsFactory->create();

        self::assertTrue($spamClickDetectSettings->enabled);
        self::assertTrue($spamClickDetectSettings->enabledForRequest);
    }

    public function testEnabledButNotForRequest(): void
    {
        $this->setBrandConfig(
            [
                'spam_click_detect' => [
                    'enabled' => true,
                    'routes'  => ['route1', 'route2'],
                ],
            ],
        );

        $this->requestInfoStub->setRoute('route3');
        $spamClickDetectSettings = $this->spamClickDetectSettingsFactory->create();

        self::assertTrue($spamClickDetectSettings->enabled);
        self::assertFalse($spamClickDetectSettings->enabledForRequest);
    }
}
