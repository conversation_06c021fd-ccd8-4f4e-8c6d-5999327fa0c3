<?php

declare(strict_types=1);

namespace Tests\Unit\SpamClickDetect\EventSubscriber;

use App\ConversionTracking\Helper\AdClickCounterHelper;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\SpamClickDetect\EventSubscriber\InjectAdClickSpamDetectEventSubscriber;
use App\SpamClickDetect\Settings\SpamClickDetectSettings;
use App\Template\Event\RenderTemplateHeadersEvent;
use PHPUnit\Framework\TestCase;
use Tests\Stub\GoogleCsa\GoogleCsaStubBuilder;
use Twig\Environment;
use Visymo\GoogleCsa\Ads\Unit\AdUnit;

class InjectAdClickSpamDetectEventSubscriberTest extends TestCase
{
    public function testRenderTemplateHeadersAddsItemWhenEnabledAndHasUnits(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $twigMock = $this->createMock(Environment::class);
        $settings = new SpamClickDetectSettings(
            enabled          : true,
            enabledForRequest: true,
        );
        $adClickCounterHelperMock = $this->createMock(AdClickCounterHelper::class);

        $googleCsa = (new GoogleCsaStubBuilder())
            ->withPublisherId('test-publisher')
            ->withQuery('test-query')
            ->create();
        $googleCsa->ads()->addUnit(new AdUnit('csa-top', null, 3, true));
        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $adClickCounterHelperMock->method('getGenericAdClickCount')->willReturn(5);
        $twigMock->method('render')->willReturn('rendered');

        $eventMock = $this->createMock(RenderTemplateHeadersEvent::class);
        $eventMock->expects($this->once())->method('addItem')->with('rendered');

        $subscriber = new InjectAdClickSpamDetectEventSubscriber(
            $googleCsaRegistry,
            $twigMock,
            $settings,
            $adClickCounterHelperMock,
        );
        $subscriber->renderTemplateHeaders($eventMock);
    }

    public function testRenderTemplateHeadersDoesNothingIfNotEnabled(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $twigMock = $this->createMock(Environment::class);
        $settings = new SpamClickDetectSettings(
            enabled          : true,
            enabledForRequest: false,
        );
        $adClickCounterHelperMock = $this->createMock(AdClickCounterHelper::class);
        $eventMock = $this->createMock(RenderTemplateHeadersEvent::class);
        $eventMock->expects($this->never())->method('addItem');

        $subscriber = new InjectAdClickSpamDetectEventSubscriber(
            $googleCsaRegistry,
            $twigMock,
            $settings,
            $adClickCounterHelperMock,
        );
        $subscriber->renderTemplateHeaders($eventMock);
    }

    public function testRenderTemplateHeadersDoesNothingIfNoUnits(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $twigMock = $this->createMock(Environment::class);
        $settings = new SpamClickDetectSettings(
            enabled          : true,
            enabledForRequest: true,
        );
        $adClickCounterHelperMock = $this->createMock(AdClickCounterHelper::class);

        $googleCsa = (new GoogleCsaStubBuilder())
            ->withPublisherId('test-publisher')
            ->withQuery('test-query')
            ->create();
        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $eventMock = $this->createMock(RenderTemplateHeadersEvent::class);
        $eventMock->expects($this->never())->method('addItem');

        $subscriber = new InjectAdClickSpamDetectEventSubscriber(
            $googleCsaRegistry,
            $twigMock,
            $settings,
            $adClickCounterHelperMock,
        );
        $subscriber->renderTemplateHeaders($eventMock);
    }
}
