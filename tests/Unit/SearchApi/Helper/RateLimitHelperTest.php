<?php

declare(strict_types=1);

namespace Tests\Unit\SearchApi\Helper;

use App\SearchApi\Helper\RateLimitHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\Office\Request\OfficeRequestStub;
use Tests\Stub\Search\Request\SearchRequestStub;

class RateLimitHelperTest extends TestCase
{
    private const int RATE_LIMIT = 10;

    /**
     * @return mixed[]
     */
    public static function isRateLimitExceededDataProvider(): array
    {
        return [
            'below rate limit'            => [
                'debugRateLimitExceeded'  => false,
                'isOfficeRequest'         => false,
                'getSearchRequestRate'    => 1,
                'expectRateLimitExceeded' => false,
            ],
            'rate limit debug'            => [
                'debugRateLimitExceeded'  => true,
                'isOfficeRequest'         => false,
                'getSearchRequestRate'    => 1000,
                'expectRateLimitExceeded' => true,
            ],
            'rate limit exceeded'         => [
                'debugRateLimitExceeded'  => false,
                'isOfficeRequest'         => false,
                'getSearchRequestRate'    => 15,
                'expectRateLimitExceeded' => true,
            ],
            'rate limit office under'     => [
                'debugRateLimitExceeded'  => false,
                'isOfficeRequest'         => true,
                'getSearchRequestRate'    => 10,
                'expectRateLimitExceeded' => false,
            ],
            'rate limit office over'      => [
                'debugRateLimitExceeded'  => false,
                'isOfficeRequest'         => true,
                'getSearchRequestRate'    => 100,
                'expectRateLimitExceeded' => false,
            ],
            'rate limit office and debug' => [
                'debugRateLimitExceeded'  => true,
                'isOfficeRequest'         => true,
                'getSearchRequestRate'    => 1,
                'expectRateLimitExceeded' => true,
            ],
        ];
    }

    #[DataProvider('isRateLimitExceededDataProvider')]
    public function testIsRateLimitExceeded(
        bool $debugRateLimitExceeded,
        bool $isOfficeRequest,
        int $getSearchRequestRate,
        bool $expectRateLimitExceeded
    ): void
    {
        $debugRequestStub = (new DebugRequestStub())
            ->setRateLimitExceeded($debugRateLimitExceeded);

        $searchRequestStub = (new SearchRequestStub())
            ->setSearchRequestRate($getSearchRequestRate);

        $rateLimitHelper = new RateLimitHelper(
            $debugRequestStub,
            $searchRequestStub,
            (new OfficeRequestStub())->setIsOffice($isOfficeRequest),
            self::RATE_LIMIT,
        );

        self::assertSame($expectRateLimitExceeded, $rateLimitHelper->isRateLimitExceeded());
    }
}
