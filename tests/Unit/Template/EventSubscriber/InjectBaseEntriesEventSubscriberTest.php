<?php

declare(strict_types=1);

namespace Tests\Unit\Template\EventSubscriber;

use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\Template\EventSubscriber\InjectBaseEntriesEventSubscriber;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

class InjectBaseEntriesEventSubscriberTest extends TestCase
{
    public function testRenderBaseFunctionEntryHeader(): void
    {
        $twigMock = $this->createMock(Environment::class);
        $twigMock->method('render')->with('@theme/template/base_function_entry_header.html.twig')
            ->willReturn('header-func');
        $eventMock = $this->createMock(RenderTemplateHeadersEvent::class);
        $eventMock->expects($this->once())->method('addItem')->with('header-func');
        $subscriber = new InjectBaseEntriesEventSubscriber($twigMock);
        $subscriber->renderBaseFunctionEntryHeader($eventMock);
    }

    public function testRenderBaseEntryHeader(): void
    {
        $twigMock = $this->createMock(Environment::class);
        $twigMock->method('render')->with('@theme/template/base_entry_header.html.twig')
            ->willReturn('header-base');
        $eventMock = $this->createMock(RenderTemplateHeadersEvent::class);
        $eventMock->expects($this->once())->method('addItem')->with('header-base');
        $subscriber = new InjectBaseEntriesEventSubscriber($twigMock);
        $subscriber->renderBaseEntryHeader($eventMock);
    }

    public function testRenderComponentOverridesEntryHeader(): void
    {
        $twigMock = $this->createMock(Environment::class);
        $twigMock->method('render')->with('@theme/template/component_overrides_entry_header.html.twig')
            ->willReturn('header-override');
        $eventMock = $this->createMock(RenderTemplateHeadersEvent::class);
        $eventMock->expects($this->once())->method('addItem')->with('header-override');
        $subscriber = new InjectBaseEntriesEventSubscriber($twigMock);
        $subscriber->renderComponentOverridesEntryHeader($eventMock);
    }

    public function testRenderBaseEntryFooter(): void
    {
        $twigMock = $this->createMock(Environment::class);
        $twigMock->method('render')->with('@theme/template/base_entry_footer.html.twig')
            ->willReturn('footer-base');
        $eventMock = $this->createMock(RenderTemplateFootersEvent::class);
        $eventMock->expects($this->once())->method('addItem')->with('footer-base');
        $subscriber = new InjectBaseEntriesEventSubscriber($twigMock);
        $subscriber->renderBaseEntryFooter($eventMock);
    }
}
