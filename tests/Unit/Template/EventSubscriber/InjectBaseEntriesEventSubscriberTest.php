<?php

declare(strict_types=1);

namespace Tests\Unit\Template\EventSubscriber;

use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\Template\EventSubscriber\InjectBaseEntriesEventSubscriber;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

class InjectBaseEntriesEventSubscriberTest extends TestCase
{
    public function testRenderBaseFunctionEntryHeader(): void
    {
        $twig = $this->createMock(Environment::class);
        $twig->method('render')->with('@theme/template/base_function_entry_header.html.twig')
            ->willReturn('header-func');
        $event = $this->createMock(RenderTemplateHeadersEvent::class);
        $event->expects($this->once())->method('addItem')->with('header-func');
        $subscriber = new InjectBaseEntriesEventSubscriber($twig);
        $subscriber->renderBaseFunctionEntryHeader($event);
    }

    public function testRenderBaseEntryHeader(): void
    {
        $twig = $this->createMock(Environment::class);
        $twig->method('render')->with('@theme/template/base_entry_header.html.twig')
            ->willReturn('header-base');
        $event = $this->createMock(RenderTemplateHeadersEvent::class);
        $event->expects($this->once())->method('addItem')->with('header-base');
        $subscriber = new InjectBaseEntriesEventSubscriber($twig);
        $subscriber->renderBaseEntryHeader($event);
    }

    public function testRenderComponentOverridesEntryHeader(): void
    {
        $twig = $this->createMock(Environment::class);
        $twig->method('render')->with('@theme/template/component_overrides_entry_header.html.twig')
            ->willReturn('header-override');
        $event = $this->createMock(RenderTemplateHeadersEvent::class);
        $event->expects($this->once())->method('addItem')->with('header-override');
        $subscriber = new InjectBaseEntriesEventSubscriber($twig);
        $subscriber->renderComponentOverridesEntryHeader($event);
    }

    public function testRenderBaseEntryFooter(): void
    {
        $twig = $this->createMock(Environment::class);
        $twig->method('render')->with('@theme/template/base_entry_footer.html.twig')
            ->willReturn('footer-base');
        $event = $this->createMock(RenderTemplateFootersEvent::class);
        $event->expects($this->once())->method('addItem')->with('footer-base');
        $subscriber = new InjectBaseEntriesEventSubscriber($twig);
        $subscriber->renderBaseEntryFooter($event);
    }
}
