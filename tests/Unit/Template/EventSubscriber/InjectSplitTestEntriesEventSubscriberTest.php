<?php

declare(strict_types=1);

namespace Tests\Unit\Template\EventSubscriber;

use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Template\Event\RenderTemplateHtmlClassEvent;
use App\Template\EventSubscriber\InjectSplitTestEntriesEventSubscriber;
use PHPUnit\Framework\TestCase;

final class InjectSplitTestEntriesEventSubscriberTest extends TestCase
{
    public function testRenderTemplateHtmlClassAddsVariantWhenActive(): void
    {
        $splitTestReaderMock = $this->createMock(SplitTestExtendedReaderInterface::class);
        $splitTestReaderMock->method('getVariant')->willReturn('test-variant');

        $event = new RenderTemplateHtmlClassEvent();
        $subscriber = new InjectSplitTestEntriesEventSubscriber($splitTestReaderMock);
        $subscriber->renderTemplateHtmlClass($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('variant-test-variant', $event->getItems()[0]);
    }

    public function testRenderTemplateHtmlClassDoesNothingWhenNoVariant(): void
    {
        $splitTestReaderMock = $this->createMock(SplitTestExtendedReaderInterface::class);
        $splitTestReaderMock->method('getVariant')->willReturn(null);

        $event = new RenderTemplateHtmlClassEvent();
        $subscriber = new InjectSplitTestEntriesEventSubscriber($splitTestReaderMock);
        $subscriber->renderTemplateHtmlClass($event);

        self::assertEmpty($event->getItems());
    }
}
