<?php

declare(strict_types=1);

namespace Tests\Unit\Template\EventSubscriber;

use App\BingAds\Helper\BingAdsHelper;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\Template\DelayedContainer\DelayedContainerHelper;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\Template\EventSubscriber\InjectDelayedContainerFallbackScriptsEventSubscriber;
use App\Tracking\Helper\TrafficHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Http\Request\Info\RequestInfoStub;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;
use Twig\Environment;

final class InjectDelayedContainerFallbackScriptsEventSubscriberTest extends TestCase
{
    private DelayedContainerHelper $delayedContainerHelper;

    private Environment & MockObject $twig;

    private BingAdsHelper & MockObject $bingAdsHelperMock;

    private TrafficHelper & MockObject $trafficHelperMock;

    protected function setUp(): void
    {
        parent::setUp();

        $googleCsaRegistry = new GoogleCsaRegistry();
        $this->bingAdsHelperMock = $this->createMock(BingAdsHelper::class);
        $this->trafficHelperMock = $this->createMock(TrafficHelper::class);
        $activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
        $requestInfoStub = new RequestInfoStub();

        $this->delayedContainerHelper = new DelayedContainerHelper(
            $googleCsaRegistry,
            $this->bingAdsHelperMock,
            $this->trafficHelperMock,
            $activeTrackingEntryHelperStub,
            $requestInfoStub,
        );
        $this->twig = $this->createMock(Environment::class);
    }

    /**
     * @return array<string, array{
     *     isRequired: bool,
     *     expectRender: bool,
     *     template: string,
     *     templateParams: array<string, mixed>,
     *     expectedContent: string
     * }>
     */
    public static function renderTemplateHeadersDataProvider(): array
    {
        return [
            'required'     => [
                'isRequired'      => true,
                'expectRender'    => true,
                'template'        => '@theme/delayed_container/delayed_container_header.html.twig',
                'templateParams'  => [],
                'expectedContent' => 'rendered_header',
            ],
            'not required' => [
                'isRequired'      => false,
                'expectRender'    => false,
                'template'        => '',
                'templateParams'  => [],
                'expectedContent' => '',
            ],
        ];
    }

    /**
     * @param array<string, mixed> $templateParams
     */
    #[DataProvider('renderTemplateHeadersDataProvider')]
    public function testRenderTemplateHeaders(
        bool $isRequired,
        bool $expectRender,
        string $template,
        array $templateParams,
        string $expectedContent
    ): void {
        $this->trafficHelperMock->method('isPaidTraffic')->willReturn($isRequired);
        $this->bingAdsHelperMock->method('hasUnits')->willReturn($isRequired);

        if ($expectRender) {
            $this->twig->expects($this->once())
                ->method('render')
                ->with($template, $templateParams)
                ->willReturn($expectedContent);
        } else {
            $this->twig->expects($this->never())->method('render');
        }

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber(
            $this->delayedContainerHelper,
            $this->twig
        );
        $subscriber->renderTemplateHeaders($event);

        if ($expectRender) {
            self::assertCount(1, $event->getItems());
            self::assertSame($expectedContent, $event->getItems()[0]);
        } else {
            self::assertEmpty($event->getItems());
        }
    }

    /**
     * @return array<string, array{
     *     isRequired: bool,
     *     expectRender: bool,
     *     template: string,
     *     templateParams: array<string, mixed>,
     *     expectedContent: string
     * }>
     */
    public static function renderTemplateFootersDataProvider(): array
    {
        return [
            'required'     => [
                'isRequired'      => true,
                'expectRender'    => true,
                'template'        => '@theme/delayed_container/delayed_container_footer.html.twig',
                'templateParams'  => [],
                'expectedContent' => 'rendered_footer',
            ],
            'not required' => [
                'isRequired'      => false,
                'expectRender'    => false,
                'template'        => '',
                'templateParams'  => [],
                'expectedContent' => '',
            ],
        ];
    }

    /**
     * @param array<string, mixed> $templateParams
     */
    #[DataProvider('renderTemplateFootersDataProvider')]
    public function testRenderTemplateFooters(
        bool $isRequired,
        bool $expectRender,
        string $template,
        array $templateParams,
        string $expectedContent
    ): void {
        $this->trafficHelperMock->method('isPaidTraffic')->willReturn($isRequired);
        $this->bingAdsHelperMock->method('hasUnits')->willReturn($isRequired);

        if ($expectRender) {
            $this->twig->expects($this->once())
                ->method('render')
                ->with($template, $templateParams)
                ->willReturn($expectedContent);
        } else {
            $this->twig->expects($this->never())->method('render');
        }

        $event = new RenderTemplateFootersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber(
            $this->delayedContainerHelper,
            $this->twig
        );
        $subscriber->renderTemplateFooters($event);

        if ($expectRender) {
            self::assertCount(1, $event->getItems());
            self::assertSame($expectedContent, $event->getItems()[0]);
        } else {
            self::assertEmpty($event->getItems());
        }
    }

    public function testRenderScriptsAddsItemWhenNotRenderedBefore(): void
    {
        $this->trafficHelperMock->method('isPaidTraffic')->willReturn(false);
        $this->bingAdsHelperMock->method('hasUnits')->willReturn(false);
        $this->twig->method('render')
            ->with('@theme/delayed_container/delayed_container_scripts.html.twig', ['show' => true])
            ->willReturn('rendered_scripts');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber(
            $this->delayedContainerHelper,
            $this->twig
        );
        $subscriber->renderScripts($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('rendered_scripts', $event->getItems()[0]);
    }

    public function testRenderScriptsDoesNothingWhenAlreadyRendered(): void
    {
        $event1 = new RenderTemplateHeadersEvent();
        $event2 = new RenderTemplateHeadersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber(
            $this->delayedContainerHelper,
            $this->twig
        );

        $subscriber->renderScripts($event1);
        $subscriber->renderScripts($event2);

        self::assertEmpty($event2->getItems());
    }
}
