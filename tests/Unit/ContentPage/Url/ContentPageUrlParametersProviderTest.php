<?php

declare(strict_types=1);

namespace Tests\Unit\ContentPage\Url;

use App\ContentPage\Request\ContentPageRequestInterface;
use App\ContentPage\Url\ContentPageUrlParametersProvider;
use App\Http\Url\PersistentUrlParametersPageType;
use App\JsonTemplate\View\JsonTemplateViewRegistry;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\ContentPage\Request\ContentPageRequestStub;
use Tests\Stub\JsonTemplate\View\ViewStub;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPage\Response\ContentPageResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

class ContentPageUrlParametersProviderTest extends TestCase
{
    private ContentPageRequestStub $contentPageRequestStub;

    private ViewStub $viewStub;

    private ContentPageUrlParametersProvider $contentPageUrlParametersProvider;

    protected function setUp(): void
    {
        parent::setUp();

        $this->contentPageRequestStub = new ContentPageRequestStub();
        $this->viewStub = new ViewStub();

        $jsonTemplateViewRegistry = new JsonTemplateViewRegistry();
        $jsonTemplateViewRegistry->setView($this->viewStub);

        $this->contentPageUrlParametersProvider = new ContentPageUrlParametersProvider(
            contentPageRequest      : $this->contentPageRequestStub,
            jsonTemplateViewRegistry: $jsonTemplateViewRegistry,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getPersistentUrlParametersDataProvider(): array
    {
        $randomPublicId = mt_rand();
        $randomPreviousPublicId = mt_rand();
        $randomSearchResponsePublicId = mt_rand();

        return [
            'empty'                                                => [
                'publicId'               => null,
                'previousPublicId'       => null,
                'searchResponsePublicId' => null,
                'expectedUrlParameters'  => [
                    PersistentUrlParametersPageType::DEFAULT->value             => [],
                    PersistentUrlParametersPageType::NEW_SEARCH->value          => [],
                    PersistentUrlParametersPageType::CONVERSION_TRACKING->value => [],
                    PersistentUrlParametersPageType::RELATED_TERMS->value       => [],
                ],
            ],
            'all'                                                  => [
                'publicId'               => $randomPublicId,
                'previousPublicId'       => $randomPreviousPublicId,
                'searchResponsePublicId' => $randomSearchResponsePublicId,
                'expectedUrlParameters'  => [
                    PersistentUrlParametersPageType::DEFAULT->value             => [
                        ContentPageRequestInterface::PARAMETER_CONTENT_PAGE_PUBLIC_ID => (string)$randomPublicId,
                    ],
                    PersistentUrlParametersPageType::NEW_SEARCH->value          => [],
                    PersistentUrlParametersPageType::CONVERSION_TRACKING->value => [],
                    PersistentUrlParametersPageType::RELATED_TERMS->value       => [
                        ContentPageRequestInterface::PARAMETER_PREVIOUS_CONTENT_PAGE_PUBLIC_ID => (string)$randomSearchResponsePublicId,
                    ],
                ],
            ],
            'request ids only'                                     => [
                'publicId'               => $randomPublicId,
                'previousPublicId'       => $randomPreviousPublicId,
                'searchResponsePublicId' => null,
                'expectedUrlParameters'  => [
                    PersistentUrlParametersPageType::DEFAULT->value             => [
                        ContentPageRequestInterface::PARAMETER_CONTENT_PAGE_PUBLIC_ID => (string)$randomPublicId,
                    ],
                    PersistentUrlParametersPageType::NEW_SEARCH->value          => [],
                    PersistentUrlParametersPageType::CONVERSION_TRACKING->value => [],
                    PersistentUrlParametersPageType::RELATED_TERMS->value       => [
                        ContentPageRequestInterface::PARAMETER_PREVIOUS_CONTENT_PAGE_PUBLIC_ID => (string)$randomPreviousPublicId,
                    ],
                ],
            ],
            'previous id only'                                     => [
                'publicId'               => null,
                'previousPublicId'       => $randomPreviousPublicId,
                'searchResponsePublicId' => null,
                'expectedUrlParameters'  => [
                    PersistentUrlParametersPageType::DEFAULT->value             => [],
                    PersistentUrlParametersPageType::NEW_SEARCH->value          => [],
                    PersistentUrlParametersPageType::CONVERSION_TRACKING->value => [],
                    PersistentUrlParametersPageType::RELATED_TERMS->value       => [
                        ContentPageRequestInterface::PARAMETER_PREVIOUS_CONTENT_PAGE_PUBLIC_ID => (string)$randomPreviousPublicId,
                    ],
                ],
            ],
            'content page request enabled with search response id' => [
                'publicId'               => null,
                'previousPublicId'       => null,
                'searchResponsePublicId' => $randomSearchResponsePublicId,
                'expectedUrlParameters'  => [
                    PersistentUrlParametersPageType::DEFAULT->value             => [],
                    PersistentUrlParametersPageType::NEW_SEARCH->value          => [],
                    PersistentUrlParametersPageType::CONVERSION_TRACKING->value => [],
                    PersistentUrlParametersPageType::RELATED_TERMS->value       => [
                        ContentPageRequestInterface::PARAMETER_PREVIOUS_CONTENT_PAGE_PUBLIC_ID => (string)$randomSearchResponsePublicId,
                    ],
                ],
            ],
        ];
    }

    /**
     * @param mixed[] $expectedUrlParameters
     */
    #[DataProvider('getPersistentUrlParametersDataProvider')]
    public function testGetPersistentUrlParameters(
        ?int $publicId,
        ?int $previousPublicId,
        ?int $searchResponsePublicId,
        array $expectedUrlParameters
    ): void
    {
        $this->contentPageRequestStub
            ->setPublicId($publicId)
            ->setPreviousPublicId($previousPublicId);

        if ($searchResponsePublicId !== null) {
            $this->viewStub->getDataRequest()->contentPage()->enable();
            $contentPage = ContentPage::create(
                id                  : 1,
                publicId            : $searchResponsePublicId,
                locale              : 'en_US',
                collectionSlug      : 'default',
                isHomepage          : false,
                isFallbackLocale    : false,
                isFallbackCollection: false,
                title               : 'Title',
                slug                : 'Slug',
                category            : null,
                excerpt             : 'Excerpt',
                readingTime         : 1,
                publishedAt         : null,
                meta                : null,
                image               : null,
                paragraphs          : [],
                keywords            : [],
            );

            $this->viewStub->getDataRegistry()->setContentPage(
                ContentPageResponseContext::create(
                    source: 'content_api',
                    page  : $contentPage,
                    errors: [],
                ),
            );
        } else {
            $this->viewStub->getDataRegistry()->setContentPage(
                ContentPageResponseContext::createEmpty(),
            );
        }

        foreach (PersistentUrlParametersPageType::cases() as $pageType) {
            // Assert parameters are set for page type
            self::assertArrayHasKey(
                $pageType->value,
                $expectedUrlParameters,
                sprintf('Expected parameters set for page type %s', $pageType->value),
            );

            // Assert the output stays the same each time
            self::assertSame(
                $expectedUrlParameters[$pageType->value],
                $this->contentPageUrlParametersProvider->getPersistentUrlParameters($pageType),
            );
            self::assertSame(
                $expectedUrlParameters[$pageType->value],
                $this->contentPageUrlParametersProvider->getPersistentUrlParameters($pageType),
            );
        }
    }
}
