<?php

declare(strict_types=1);

namespace Tests\Unit\ContentPage\EventSubscriber;

use App\ContentPage\EventSubscriber\ContentPageEventSubscriber;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\ContentPage\Request\ContentPageRequestStub;

final class ContentPageEventSubscriberTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function publicIdDataProvider(): array
    {
        return [
            'with public id' => [
                'publicId' => 123,
                'previousPublicId' => null,
                'expectedPublicId' => 123,
            ],
            'with previous public id' => [
                'publicId' => null,
                'previousPublicId' => 456,
                'expectedPublicId' => 456,
            ],
            'with both ids, public id takes precedence' => [
                'publicId' => 123,
                'previousPublicId' => 456,
                'expectedPublicId' => 123,
            ],
            'with no ids' => [
                'publicId' => null,
                'previousPublicId' => null,
                'expectedPublicId' => null,
            ],
        ];
    }

    #[DataProvider('publicIdDataProvider')]
    public function testOnJsonTemplateViewCreatedSetsPublicId(
        ?int $publicId,
        ?int $previousPublicId,
        ?int $expectedPublicId
    ): void {
        $contentPageRequest = new ContentPageRequestStub();
        $contentPageRequest->setPublicId($publicId);
        $contentPageRequest->setPreviousPublicId($previousPublicId);

        $dataRequest = new ViewDataRequest();

        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn($dataRequest);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new ContentPageEventSubscriber($contentPageRequest);
        $subscriber->onJsonTemplateViewCreated($event);

        self::assertSame($expectedPublicId, $dataRequest->contentPage()->getPublicId());
    }
}
