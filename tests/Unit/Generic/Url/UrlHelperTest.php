<?php

declare(strict_types=1);

namespace Tests\Unit\Generic\Url;

use App\Generic\Url\UrlHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class UrlHelperTest extends TestCase
{
    private UrlHelper $urlHelper;

    protected function setUp(): void
    {
        parent::setUp();

        $this->urlHelper = new UrlHelper();
    }

    /**
     * @return mixed[]
     */
    public static function urlDataProvider(): array
    {
        return [
            'remove, without path'                   => [
                'url'         => 'https://www.example.com?param1=value1&param2=value2',
                'parameter'   => 'param1',
                'value'       => null,
                'expectedUrl' => 'https://www.example.com?param2=value2',
            ],
            'remove, domain only'                    => [
                'url'         => 'https://www.example.com',
                'parameter'   => 'param1',
                'value'       => null,
                'expectedUrl' => 'https://www.example.com',
            ],
            'remove, with path'                      => [
                'url'         => 'https://www.example.com/mypath?param1=value1&param2=value2',
                'parameter'   => 'param1',
                'value'       => null,
                'expectedUrl' => 'https://www.example.com/mypath?param2=value2',
            ],
            'remove, with path ending with slash'    => [
                'url'         => 'https://www.example.com/mypath/?param1=value1&param2=value2',
                'parameter'   => 'param1',
                'value'       => null,
                'expectedUrl' => 'https://www.example.com/mypath/?param2=value2',
            ],
            'remove, removing unknown url parameter' => [
                'url'         => 'https://www.example.com/mypath/?param1=value1&param2=value2',
                'parameter'   => 'hello',
                'value'       => null,
                'expectedUrl' => 'https://www.example.com/mypath/?param1=value1&param2=value2',
            ],
            'remove, results in no query string'     => [
                'url'         => 'http://www.example.com/mypath/?param1=value1',
                'parameter'   => 'param1',
                'value'       => null,
                'expectedUrl' => 'http://www.example.com/mypath/',
            ],
            'add, without path'                      => [
                'url'         => 'https://www.example.com?param2=value2',
                'parameter'   => 'param1',
                'value'       => 'value1',
                'expectedUrl' => 'https://www.example.com?param2=value2&param1=value1',
            ],
            'add, domain only'                       => [
                'url'         => 'https://www.example.com',
                'parameter'   => 'param1',
                'value'       => 'value1',
                'expectedUrl' => 'https://www.example.com?param1=value1',
            ],
            'add, with path'                         => [
                'url'         => 'https://www.example.com/mypath?param2=value2',
                'parameter'   => 'param1',
                'value'       => 765765,
                'expectedUrl' => 'https://www.example.com/mypath?param2=value2&param1=765765',
            ],
            'add, with path ending with slash'       => [
                'url'         => 'https://www.example.com/mypath/?param1=value1&param2=value2',
                'parameter'   => 'hello',
                'value'       => 'world',
                'expectedUrl' => 'https://www.example.com/mypath/?param1=value1&param2=value2&hello=world',
            ],
        ];
    }

    #[DataProvider('urlDataProvider')]
    public function testParameterValue(
        string $url,
        string $parameter,
        string|int|null $value,
        string $expectedUrl
    ): void
    {
        self::assertEquals(
            $expectedUrl,
            $this->urlHelper->setParameterValue($url, $parameter, $value),
        );
    }
}
