<?php

declare(strict_types=1);

namespace Tests\Unit\Generic\Generator;

use App\Generic\Generator\HexColorGenerator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

final class HexColorGeneratorTest extends TestCase
{
    private HexColorGenerator $hexColorGenerator;

    protected function setUp(): void
    {
        parent::setUp();

        $this->hexColorGenerator = new HexColorGenerator();
    }

    /**
     * @return mixed[]
     */
    public static function lightenDataProvider(): array
    {
        return [
            'black 10'  => [
                'color'      => '#000000',
                'percentage' => 10,
                'expected'   => '#1a1a1a',
            ],
            'white 10 ' => [
                'color'      => '#ffffff',
                'percentage' => 10,
                'expected'   => '#ffffff',
            ],
            'red 5'     => [
                'color'      => '#ff0000',
                'percentage' => 5,
                'expected'   => '#ff1a1a',
            ],
            'blue 0'    => [
                'color'      => '#0000ff',
                'percentage' => 0,
                'expected'   => '#0000ff',
            ],
        ];
    }

    #[DataProvider('lightenDataProvider')]
    public function testLighten(string $color, int $percentage, string $expected): void
    {
        $result = $this->hexColorGenerator->lighten($color, $percentage);

        self::assertSame($expected, $result);
    }

    /**
     * @return mixed[]
     */
    public static function darkenDataProvider(): array
    {
        return [
            'black 10'  => [
                'color'      => '#000000',
                'percentage' => 10,
                'expected'   => '#000000',
            ],
            'white 10 ' => [
                'color'      => '#ffffff',
                'percentage' => 10,
                'expected'   => '#e6e6e6',
            ],
            'red 5'     => [
                'color'      => '#ff0000',
                'percentage' => 5,
                'expected'   => '#e60000',
            ],
            'blue 0'    => [
                'color'      => '#0000ff',
                'percentage' => 0,
                'expected'   => '#0000ff',
            ],
        ];
    }

    #[DataProvider('darkenDataProvider')]
    public function testDarken(string $color, int $percentage, string $expected): void
    {
        $result = $this->hexColorGenerator->darken($color, $percentage);

        self::assertSame($expected, $result);
    }
}
