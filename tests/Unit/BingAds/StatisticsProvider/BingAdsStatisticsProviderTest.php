<?php

declare(strict_types=1);

namespace Tests\Unit\BingAds\StatisticsProvider;

use App\BingAds\StatisticsProvider\BingAdsStatisticsProvider;
use App\BingAds\StatisticsProvider\BingAdsStatisticsResolver;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Tests\Unit\Statistics\Provider\AbstractStatisticsProviderTestCase;
use Visymo\Shared\Domain\OptionsResolver\InvalidOptionException;
use Visymo\Shared\Infrastructure\Bridge\Symfony\OptionsResolver\SymfonyOptionsResolverBridge;

class BingAdsStatisticsProviderTest extends AbstractStatisticsProviderTestCase
{
    private BingAdsStatisticsResolver $bingAdsStatisticsResolver;

    protected function setUp(): void
    {
        parent::setUp();

        $this->bingAdsStatisticsResolver = new BingAdsStatisticsResolver(
            new SymfonyOptionsResolverBridge(new OptionsResolver()),
            $this->genericRequestMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function payloadDataProvider(): array
    {
        $pageviewId = uuid_create(UUID_TYPE_RANDOM);

        return [
            'complete' => [
                'payload'            => [
                    'key'  => 'ba',
                    'pvid' => $pageviewId,
                    'hl'   => true,
                    'as'   => 4,
                    'lt'   => 123,
                ],
                'expectedStatistics' => [
                    'visit_id'    => self::$visitId,
                    'pageview_id' => $pageviewId,
                    'bing_ads'    => [
                        'has_loaded'   => true,
                        'amount_shown' => 4,
                        'load_time'    => 123,
                    ],
                ],
            ],
            'extra'    => [
                'payload'            => [
                    'extra'    => true,
                    'key'      => 'ba',
                    'visit_id' => self::$visitId,
                    'pvid'     => $pageviewId,
                    'hl'       => true,
                    'as'       => 4,
                    'lt'       => 123,
                ],
                'expectedStatistics' => [
                    'visit_id'    => self::$visitId,
                    'pageview_id' => $pageviewId,
                    'bing_ads'    => [
                        'has_loaded'   => true,
                        'amount_shown' => 4,
                        'load_time'    => 123,
                    ],
                ],
            ],
            'bad'      => [
                'payload'            => [
                    'key'      => 'ba',
                    'visit_id' => self::$visitId,
                    'pvid'     => $pageviewId,
                    'bad'      => true,
                ],
                'expectedStatistics' => null,
            ],
        ];
    }

    /**
     * @param array<string, mixed> $payload
     * @param mixed[]|null         $expectedStatistics
     */
    #[DataProvider('payloadDataProvider')]
    public function testGetFromPayload(array $payload, ?array $expectedStatistics): void
    {
        if ($expectedStatistics === null) {
            $this->expectException(InvalidOptionException::class);
        }

        $bingAdsStatisticsProvider = new BingAdsStatisticsProvider($this->bingAdsStatisticsResolver);
        self::assertSame('bing_ads', $bingAdsStatisticsProvider::getContextKey());
        self::assertSame('ba', $bingAdsStatisticsProvider::getPayloadKey());

        $actualStatistics = $bingAdsStatisticsProvider->getFromPayload($payload);

        self::assertArrayHasKey('response_timestamp', $actualStatistics['bing_ads']);

        unset($actualStatistics['bing_ads']['response_timestamp']);

        self::assertSame($expectedStatistics, $actualStatistics);
    }
}
