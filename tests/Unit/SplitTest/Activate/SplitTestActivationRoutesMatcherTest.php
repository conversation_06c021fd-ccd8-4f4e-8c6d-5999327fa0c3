<?php

declare(strict_types=1);

namespace Tests\Unit\SplitTest\Activate;

use App\SplitTest\Activate\SplitTestActivationRoutesMatcher;
use App\SplitTest\Settings\SplitTestActivation;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Tests\Stub\Http\Request\Info\RequestInfoStub;
use Tests\Stub\SplitTest\Request\SplitTestRequestStub;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

class SplitTestActivationRoutesMatcherTest extends TestCase
{
    private RequestInfoStub $requestInfoStub;

    private SplitTestRequestStub $splitTestRequestStub;

    private SplitTestActivationRoutesMatcher $splitTestActivationRoutesMatcher;

    protected function setUp(): void
    {
        $this->requestInfoStub = new RequestInfoStub();
        $this->splitTestRequestStub = new SplitTestRequestStub();
        $logger = $this->createMock(LoggerInterface::class);

        $this->splitTestActivationRoutesMatcher = new SplitTestActivationRoutesMatcher(
            $this->requestInfoStub,
            $this->splitTestRequestStub,
            $logger,
        );
    }

    /**
     * @return mixed[]
     */
    public static function activationRouteDataProvider(): array
    {
        return [
            'any_route'                                               => [
                'splitTestRoutes'         => [],
                'activeRoute'             => 'route_web_search',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => true,
            ],
            'web_search'                                              => [
                'splitTestRoutes'         => ['route_web_search'],
                'activeRoute'             => 'route_web_search',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => true,
            ],
            'web_search_advertised'                                   => [
                'splitTestRoutes'         => ['route_web_search_advertised'],
                'activeRoute'             => 'route_web_search_advertised',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => true,
            ],
            'display_search_related'                                  => [
                'splitTestRoutes'         => ['route_display_search_related'],
                'activeRoute'             => 'route_display_search_related',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => true,
            ],
            'display_search_related_web'                              => [
                'splitTestRoutes'         => ['route_display_search_related_web'],
                'activeRoute'             => 'route_display_search_related_web',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => true,
            ],
            'display_search'                                          => [
                'splitTestRoutes'         => ['route_display_search'],
                'activeRoute'             => 'route_display_search',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => true,
            ],
            'display_search_advertised'                               => [
                'splitTestRoutes'         => ['route_display_search_advertised'],
                'activeRoute'             => 'route_display_search_advertised',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => true,
            ],
            'wrong_route'                                             => [
                'splitTestRoutes'         => ['route_web_search'],
                'activeRoute'             => 'route_web_search_advertised',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => false,
            ],
            'empty_route'                                             => [
                'splitTestRoutes'         => ['route_web_search'],
                'activeRoute'             => '',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => false,
            ],
            'multiple_routes'                                         => [
                'splitTestRoutes'         => ['route_web_search', 'route_web_search_advertised'],
                'activeRoute'             => 'route_web_search_advertised',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => true,
            ],
            'do not match always_match for new tests'                 => [
                'splitTestRoutes'         => ['route_web_search'],
                'activeRoute'             => 'route_tracking_pixel_endpoint',
                'hasAlwaysMatchRouteFlag' => true,
                'forActivatedTest'        => false,
                'expectedMatch'           => false,
            ],
            'always match always_match for activated tests'           => [
                'splitTestRoutes'         => ['route_web_search'],
                'activeRoute'             => 'route_tracking_pixel_endpoint',
                'hasAlwaysMatchRouteFlag' => true,
                'forActivatedTest'        => true,
                'expectedMatch'           => true,
            ],
            'do not match tracking pixel related'                     => [
                'splitTestRoutes'         => ['route_web_search', 'route_web_search_advertised'],
                'activeRoute'             => 'route_google_related_terms_tracking_pixel_endpoint',
                'hasAlwaysMatchRouteFlag' => true,
                'forActivatedTest'        => false,
                'expectedMatch'           => false,
            ],
            'always match tracking pixel related for activated tests' => [
                'splitTestRoutes'         => ['route_web_search', 'route_web_search_advertised'],
                'activeRoute'             => 'route_google_related_terms_tracking_pixel_endpoint',
                'hasAlwaysMatchRouteFlag' => true,
                'forActivatedTest'        => true,
                'expectedMatch'           => true,
            ],
            'non existing split test route'                           => [
                'splitTestRoutes'         => ['non_existing_route'],
                'activeRoute'             => 'route_web_search',
                'hasAlwaysMatchRouteFlag' => false,
                'forActivatedTest'        => false,
                'expectedMatch'           => false,
            ],
        ];
    }

    /**
     * @param string[] $splitTestRoutes
     */
    #[DataProvider('activationRouteDataProvider')]
    public function testIsActivationRoute(
        array $splitTestRoutes,
        string $activeRoute,
        bool $hasAlwaysMatchRouteFlag,
        bool $forActivatedTest,
        bool $expectedMatch
    ): void
    {
        $splitTestActivation = new SplitTestActivation(
            device   : null,
            service  : null,
            dateStart: new \DateTime('2022-01-01 00:00:00', TimezoneEnum::AMSTERDAM->toDateTimeZone()),
            dateEnd  : new \DateTime('2099-12-31 23:59:59', TimezoneEnum::AMSTERDAM->toDateTimeZone()),
            domains  : [],
            routes   : $splitTestRoutes,
        );
        $this->requestInfoStub
            ->setRoute($activeRoute);

        $this->splitTestRequestStub->setAlwaysMatchRouteFlag($hasAlwaysMatchRouteFlag);

        $isActivationRoute = $this->splitTestActivationRoutesMatcher->isActivationRoute(
            $splitTestActivation,
            $forActivatedTest,
        );

        self::assertSame($expectedMatch, $isActivationRoute);
    }
}
