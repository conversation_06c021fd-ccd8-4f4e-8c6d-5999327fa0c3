<?php

declare(strict_types=1);

namespace Tests\Unit\SplitTest\Activate;

use App\Generic\Device\Device;
use App\SplitTest\Activate\ActiveSplitTest;
use App\SplitTest\Activate\ActiveSplitTestFactory;
use App\SplitTest\Activate\SplitTestActivateHelper;
use App\SplitTest\Activate\SplitTestActivationMatcherInterface;
use App\SplitTest\Activate\SplitTestActivationRoutesMatcher;
use App\SplitTest\Activate\SplitTestActivator;
use App\SplitTest\Settings\SplitTestActivation;
use App\SplitTest\Settings\SplitTestChannels;
use App\SplitTest\Settings\SplitTestListSettings;
use App\SplitTest\Settings\SplitTestListSettingsRepository;
use App\SplitTest\Settings\SplitTestSettings;
use App\SplitTest\Settings\SplitTestVariant;
use App\SplitTest\SplitTestExtended;
use App\SplitTest\SplitTestExtendedWriterInterface;
use App\Tracking\Model\TrafficSource;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Tests\Stub\AdBot\Request\AdBotRequestStub;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\Domain\Settings\DomainSettingsHelperStub;
use Tests\Stub\Domain\Settings\DomainSettingsStubBuilder;
use Tests\Stub\FriendlyBot\Request\FriendlyBotRequestStub;
use Tests\Stub\Http\Request\Info\RequestInfoStub;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\SplitTest\Request\SplitTestRequestStub;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Domain\Generator\RandomPercentageGenerator;
use Visymo\Shared\Infrastructure\Stub\Domain\DateTime\DateTimeFactoryStub;

class SplitTestActivateHelperTest extends TestCase
{
    private SplitTestExtended $splitTestExtended;

    private AdBotRequestStub $adBotRequestStub;

    private FriendlyBotRequestStub $friendlyBotRequestStub;

    private DebugRequestStub $debugRequestStub;

    private SearchRequestStub $searchRequestStub;

    private DomainSettingsHelperStub $domainSettingsHelperStub;

    private WebsiteSettingsHelper & MockObject $websiteSettingsHelperMock;

    private SplitTestListSettingsRepository & MockObject $splitTestListSettingsRepositoryMock;

    private RandomPercentageGenerator & MockObject $randomPercentageGeneratorMock;

    private ActiveTrackingEntryHelperStub $activeTrackingEntryHelperStub;

    private ActiveSplitTestFactory $activeSplitTestFactory;

    private DateTimeFactoryStub $dateTimeFactoryStub;

    private RequestInfoStub $requestInfoStub;

    private SplitTestActivateHelper $splitTestActivateHelper;

    protected function setUp(): void
    {
        $this->splitTestExtended = new SplitTestExtended();

        $this->adBotRequestStub = (new AdBotRequestStub())
            ->setIsAdBot(false);
        $this->friendlyBotRequestStub = (new FriendlyBotRequestStub())
            ->setIsFriendlyBot(false);
        $this->debugRequestStub = (new DebugRequestStub())
            ->setSplitTestVariant(null);
        $splitTestRequestStub = new SplitTestRequestStub();

        $this->searchRequestStub = new SearchRequestStub();
        $this->domainSettingsHelperStub = new DomainSettingsHelperStub();
        $this->websiteSettingsHelperMock = $this->createMock(WebsiteSettingsHelper::class);
        $this->splitTestListSettingsRepositoryMock = $this->createMock(SplitTestListSettingsRepository::class);
        $this->randomPercentageGeneratorMock = $this->createMock(RandomPercentageGenerator::class);

        $this->activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();

        $this->activeSplitTestFactory = new ActiveSplitTestFactory();

        $this->dateTimeFactoryStub = new DateTimeFactoryStub();
        $this->requestInfoStub = new RequestInfoStub();

        $logger = $this->createMock(LoggerInterface::class);
        $splitTestActivationRoutesMatcher = new SplitTestActivationRoutesMatcher(
            $this->requestInfoStub,
            $splitTestRequestStub,
            $logger,
        );

        $this->splitTestActivateHelper = new SplitTestActivateHelper(
            splitTestExtendedWriter        : $this->splitTestExtended,
            splitTestActivationMatchers    : [
                                                 new SplitTestActivator(
                                                     activeTrackingEntryHelper     : $this->activeTrackingEntryHelperStub,
                                                     dateTimeFactory               : $this->dateTimeFactoryStub,
                                                     websiteSettingsHelper         : $this->websiteSettingsHelperMock,
                                                     splitTestSettingsRoutesMatcher: $splitTestActivationRoutesMatcher,
                                                     domainSettingsHelper          : $this->domainSettingsHelperStub,
                                                 ),
                                             ],
            adBotRequest                   : $this->adBotRequestStub,
            searchRequest                  : $this->searchRequestStub,
            debugRequest                   : $this->debugRequestStub,
            splitTestListSettingsRepository: $this->splitTestListSettingsRepositoryMock,
            randomPercentageGenerator      : $this->randomPercentageGeneratorMock,
            activeTrackingEntryHelper      : $this->activeTrackingEntryHelperStub,
            activeSplitTestFactory         : $this->activeSplitTestFactory,
            friendlyBotRequest             : $this->friendlyBotRequestStub,
        );
    }

    /**
     * @return mixed[]
     */
    public static function splitTestDataProvider(): array
    {
        $applicationTimezone = TimezoneEnum::AMSTERDAM->toDateTimeZone();

        $dateYesterday = new \DateTime('2020-09-04 00:00:00', $applicationTimezone);
        $dateToday = new \DateTime('2020-09-05 00:00:00', $applicationTimezone);
        $dateTodayPlusTwelveHours = new \DateTime('2020-09-05 12:00:00', $applicationTimezone);
        $dateTodayPlusOne = new \DateTime('2020-09-06 00:00:00', $applicationTimezone);
        $dateTodayPlusTwo = new \DateTime('2020-09-07 00:00:00', $applicationTimezone);
        $dateTodayPlusThree = new \DateTime('2020-09-08 00:00:00', $applicationTimezone);

        $splitTests = [
            new SplitTestSettings(
                1337,
                new SplitTestActivation(
                    null,
                    null,
                    $dateYesterday,
                    $dateTodayPlusTwelveHours,
                    [],
                    [],
                ),
                new SplitTestChannels('advertised_ab_ta', 'landing_ab_ta'),
                [
                    new SplitTestVariant(
                        'hello_world',
                        '1234',
                        new SplitTestChannels('advertised_ab_tb', 'landing_ab_tb'),
                        30,
                    ),
                    new SplitTestVariant(
                        'hello_pluto',
                        '1235',
                        new SplitTestChannels('advertised_ab_tc', 'landing_ab_tc'),
                        20,
                    ),
                ],
            ),
            new SplitTestSettings(
                1334,
                new SplitTestActivation(
                    Device::TABLET,
                    SplitTestActivation::SERVICE_VALUE_GOOGLE,
                    $dateYesterday,
                    $dateTodayPlusThree,
                    ['nl.seekweb.com'],
                    ['route_web_search', 'route_web_search_advertised'],
                ),
                new SplitTestChannels('advertised_ab_td', 'landing_ab_td'),
                [
                    new SplitTestVariant('hello_mars', '1236', new SplitTestChannels('advertised_ab_te', 'landing_ab_te'), 30),
                    new SplitTestVariant('hello_sun', '1237', new SplitTestChannels('advertised_ab_tf', 'landing_ab_tf'), 30),
                ],
            ),
            new SplitTestSettings(
                1338,
                new SplitTestActivation(
                    null,
                    SplitTestActivation::SERVICE_VALUE_GOOGLE,
                    $dateTodayPlusOne,
                    $dateTodayPlusThree,
                    ['be.seekweb.com', 'nl.seekweb.com', 'uk.seekweb.com'],
                    [],
                ),
                new SplitTestChannels('advertised_ab_tg', 'landing_ab_tg'),
                [
                    new SplitTestVariant('hello_jupiter', '1238', new SplitTestChannels('advertised_ab_th', 'landing_ab_th'), 20),
                    new SplitTestVariant('hello_saturnus', '1239', new SplitTestChannels('advertised_ab_ti', 'landing_ab_ti'), 50),
                ],
            ),
        ];

        return [
            'ad bot'                                      => [
                'device'                  => Device::DESKTOP,
                'trafficSource'           => null,
                'isLandingPage'           => false,
                'adBot'                   => true,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateToday,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 40,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => null,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => null,
                'expectedContainerSuffix' => null,
            ],
            'empty tracking entry'                        => [
                'device'                  => Device::DESKTOP,
                'trafficSource'           => null,
                'isLandingPage'           => false,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => true,
                'dateNow'                 => $dateToday,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 40,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => null,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => null,
                'expectedContainerSuffix' => null,
            ],
            'no tests'                                    => [
                'device'                  => Device::DESKTOP,
                'trafficSource'           => TrafficSource::GOOGLE,
                'isLandingPage'           => false,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateToday,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 6,
                'splitTests'              => [],
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => null,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => null,
                'expectedContainerSuffix' => null,
            ],
            'variant hello_pluto advertised'              => [
                'device'                  => Device::TABLET,
                'trafficSource'           => TrafficSource::GOOGLE,
                'isLandingPage'           => false,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateToday,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 45,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => 1337,
                'expectedSplitVariant'    => 'hello_pluto',
                'expectedChannel'         => 'advertised_ab_tc',
                'expectedContainerSuffix' => '1235',
            ],
            'variant hello_pluto landingpage'             => [
                'device'                  => Device::TABLET,
                'trafficSource'           => TrafficSource::GOOGLE,
                'isLandingPage'           => true,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateToday,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search',
                'randomPercentage'        => 45,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => 1337,
                'expectedSplitVariant'    => 'hello_pluto',
                'expectedChannel'         => 'landing_ab_tc',
                'expectedContainerSuffix' => '1235',
            ],
            'variant hello_sun'                           => [
                'device'                  => Device::TABLET,
                'trafficSource'           => TrafficSource::GOOGLE,
                'isLandingPage'           => false,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateTodayPlusOne,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 50,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => 1334,
                'expectedSplitVariant'    => 'hello_sun',
                'expectedChannel'         => 'advertised_ab_tf',
                'expectedContainerSuffix' => '1237',
            ],
            'not hello_sun'                               => [
                'device'                  => Device::TABLET,
                'trafficSource'           => TrafficSource::GOOGLE,
                'isLandingPage'           => false,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateTodayPlusOne,
                'host'                    => 'xx.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 50,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => null,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => null,
                'expectedContainerSuffix' => null,
            ],
            'variant hello_jupiter'                       => [
                'device'                  => Device::DESKTOP,
                'trafficSource'           => TrafficSource::GOOGLE,
                'isLandingPage'           => false,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateTodayPlusTwo,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 17,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => 1338,
                'expectedSplitVariant'    => 'hello_jupiter',
                'expectedChannel'         => 'advertised_ab_th',
                'expectedContainerSuffix' => '1238',
            ],
            'control variant'                             => [
                'device'                  => Device::DESKTOP,
                'trafficSource'           => TrafficSource::GOOGLE,
                'isLandingPage'           => false,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateTodayPlusTwo,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 71,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => 1338,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => 'advertised_ab_tg',
                'expectedContainerSuffix' => null,
            ],
            'no matching split test'                      => [
                'device'                  => Device::DESKTOP,
                'trafficSource'           => TrafficSource::MICROSOFT,
                'isLandingPage'           => false,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateTodayPlusThree,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search_advertised',
                'randomPercentage'        => 45,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => true,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => null,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => null,
                'expectedContainerSuffix' => null,
            ],
            'no match because of google adsense disabled' => [
                'device'                  => Device::TABLET,
                'trafficSource'           => TrafficSource::MICROSOFT,
                'isLandingPage'           => true,
                'adBot'                   => false,
                'isEmptyTrackingEntry'    => false,
                'dateNow'                 => $dateToday,
                'host'                    => 'nl.seekweb.com',
                'route'                   => 'route_web_search',
                'randomPercentage'        => 50,
                'splitTests'              => $splitTests,
                'googleAdSenseEnabled'    => false,
                'contractType'            => ContractType::DIRECT,
                'expectedSplitTestId'     => null,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => null,
                'expectedContainerSuffix' => null,
            ],
        ];
    }

    /**
     * @param SplitTestSettings[] $splitTests
     */
    #[DataProvider('splitTestDataProvider')]
    public function testSplitTest(
        Device $device,
        ?TrafficSource $trafficSource,
        bool $isLandingPage,
        bool $adBot,
        bool $isEmptyTrackingEntry,
        \DateTime $dateNow,
        string $host,
        string $route,
        int $randomPercentage,
        array $splitTests,
        bool $googleAdSenseEnabled,
        ContractType $contractType,
        ?int $expectedSplitTestId,
        ?string $expectedSplitVariant,
        ?string $expectedChannel,
        ?string $expectedContainerSuffix
    ): void
    {
        $this->searchRequestStub->setIsLandingPage($isLandingPage);

        $this->activeTrackingEntryHelperStub
            ->getTrackingEntryStubBuilder()
            ->setIsEmpty($isEmptyTrackingEntry)
            ->setCreatedAt($dateNow)
            ->setQuery('pizza')
            ->setDevice($device)
            ->setTrafficSource($trafficSource);

        $this->adBotRequestStub->setIsAdBot($adBot);

        $this->splitTestListSettingsRepositoryMock->method('getSplitTestListSettings')->willReturn(
            new SplitTestListSettings($splitTests),
        );

        $this->domainSettingsHelperStub->setSettings(
            (new DomainSettingsStubBuilder())->setHost($host)->create(),
        );

        $this->websiteSettingsHelperMock->method('getSettings')->willReturn(
            $this->getWebsiteSettings($googleAdSenseEnabled, $contractType),
        );

        $this->dateTimeFactoryStub->setDateTime($dateNow);
        $this->randomPercentageGeneratorMock->method('generateRandomPercentage')->willReturn($randomPercentage);

        $this->requestInfoStub
            ->setRoute($route);

        $this->splitTestActivateHelper->startSplitTest();

        self::assertSame($expectedSplitTestId, $this->splitTestExtended->getId());
        self::assertSame($expectedSplitVariant, $this->splitTestExtended->getVariant());
        self::assertSame($expectedChannel, $this->splitTestExtended->getChannel());
        self::assertSame($expectedContainerSuffix, $this->splitTestExtended->getContainerSuffix());
    }

    /**
     * @return mixed[]
     */
    public static function splitTestValueValidationDataProvider(): array
    {
        return [
            'existing control channel landing page' => [
                'activeSplitTest'         => new ActiveSplitTest(1337, null),
                'isLandingPage'           => true,
                'expectedSplitTestId'     => 1337,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => 'landing_ab_ta',
                'expectedContainerSuffix' => null,
            ],
            'existing control channel advertised'   => [
                'activeSplitTest'         => new ActiveSplitTest(1337, null),
                'isLandingPage'           => false,
                'expectedSplitTestId'     => 1337,
                'expectedSplitVariant'    => null,
                'expectedChannel'         => 'advertised_ab_ta',
                'expectedContainerSuffix' => null,
            ],
            'existing variant landing page'         => [
                'activeSplitTest'         => new ActiveSplitTest(1337, 'hello_world'),
                'isLandingPage'           => true,
                'expectedSplitTestId'     => 1337,
                'expectedSplitVariant'    => 'hello_world',
                'expectedChannel'         => 'landing_ab_tb',
                'expectedContainerSuffix' => '1234',
            ],
            'existing variant advertised'           => [
                'activeSplitTest'         => new ActiveSplitTest(1337, 'hello_world'),
                'isLandingPage'           => false,
                'expectedSplitTestId'     => 1337,
                'expectedSplitVariant'    => 'hello_world',
                'expectedChannel'         => 'advertised_ab_tb',
                'expectedContainerSuffix' => '1234',
            ],
            'new test landing page'                 => [
                'activeSplitTest'         => new ActiveSplitTest(1332, 'hi_there'),
                'isLandingPage'           => true,
                'expectedSplitTestId'     => 1332,
                'expectedSplitVariant'    => 'hello_pluto',
                'expectedChannel'         => 'landing_ab_tc',
                'expectedContainerSuffix' => '1235',
            ],
            'new test advertised'                   => [
                'activeSplitTest'         => new ActiveSplitTest(1332, 'hi_there'),
                'isLandingPage'           => false,
                'expectedSplitTestId'     => 1332,
                'expectedSplitVariant'    => 'hello_pluto',
                'expectedChannel'         => 'advertised_ab_tc',
                'expectedContainerSuffix' => '1235',
            ],
        ];
    }

    #[DataProvider('splitTestValueValidationDataProvider')]
    public function testActiveSplitTestValue(
        ActiveSplitTest $activeSplitTest,
        bool $isLandingPage,
        ?int $expectedSplitTestId,
        ?string $expectedSplitVariant,
        string $expectedChannel,
        ?string $expectedContainerSuffix
    ): void
    {
        $applicationTimezone = TimezoneEnum::AMSTERDAM->toDateTimeZone();
        $this->searchRequestStub->setIsLandingPage($isLandingPage);

        $dateYesterday = new \DateTime('2020-09-04 00:00:00', $applicationTimezone);
        $dateToday = new \DateTime('2020-09-05 00:00:00', $applicationTimezone);
        $randomPercentage = 40;

        $splitTests = [
            new SplitTestSettings(
                $activeSplitTest->getSplitTestId(),
                new SplitTestActivation(
                    null,
                    null,
                    $dateYesterday,
                    null,
                    [],
                    [],
                ),
                new SplitTestChannels('advertised_ab_ta', 'landing_ab_ta'),
                [
                    new SplitTestVariant('hello_world', '1234', new SplitTestChannels('advertised_ab_tb', 'landing_ab_tb'), 30),
                    new SplitTestVariant('hello_pluto', '1235', new SplitTestChannels('advertised_ab_tc', 'landing_ab_tc'), 20),
                ],
            ),
        ];
        $this->splitTestListSettingsRepositoryMock->method('getSplitTestListSettings')->willReturn(
            new SplitTestListSettings($splitTests),
        );
        $this->websiteSettingsHelperMock->method('getSettings')->willReturn(
            $this->getWebsiteSettings(
                true,
                ContractType::DIRECT,
            ),
        );

        $this->activeTrackingEntryHelperStub
            ->getTrackingEntryStubBuilder()
            ->setIsEmpty(false)
            ->setCreatedAt($dateYesterday)
            ->setActiveSplitTest($activeSplitTest)
            ->setQuery('pizza')
            ->setDevice(Device::DESKTOP)
            ->clearClickIds();

        $this->dateTimeFactoryStub->setDateTime($dateToday);
        $this->randomPercentageGeneratorMock->method('generateRandomPercentage')->willReturn($randomPercentage);

        $this->splitTestActivateHelper->startSplitTest();

        self::assertSame($expectedSplitTestId, $this->splitTestExtended->getId());
        self::assertSame($expectedSplitVariant, $this->splitTestExtended->getVariant());
        self::assertSame($expectedChannel, $this->splitTestExtended->getChannel());
        self::assertSame($expectedContainerSuffix, $this->splitTestExtended->getContainerSuffix());
    }

    /**
     * @return mixed[]
     */
    public static function activationMatchersDataProvider(): array
    {
        return [
            'no match'           => [
                'matcherOneReturnValue'     => null,
                'matcherTwoReturnValue'     => null,
                'matcherThreeReturnValue'   => null,
                'matcherTwoIsCalled'        => true,
                'matcherThreeIsCalled'      => true,
                'expectNewSplitTestStarted' => false,
            ],
            'one matches'        => [
                'matcherOneReturnValue'     => true,
                'matcherTwoReturnValue'     => null,
                'matcherThreeReturnValue'   => null,
                'matcherTwoIsCalled'        => false,
                'matcherThreeIsCalled'      => false,
                'expectNewSplitTestStarted' => true,
            ],
            'two matches'        => [
                'matcherOneReturnValue'     => null,
                'matcherTwoReturnValue'     => true,
                'matcherThreeReturnValue'   => null,
                'matcherTwoIsCalled'        => true,
                'matcherThreeIsCalled'      => false,
                'expectNewSplitTestStarted' => true,
            ],
            'three matches'      => [
                'matcherOneReturnValue'     => null,
                'matcherTwoReturnValue'     => null,
                'matcherThreeReturnValue'   => true,
                'matcherTwoIsCalled'        => true,
                'matcherThreeIsCalled'      => true,
                'expectNewSplitTestStarted' => true,
            ],
            'two prevents start' => [
                'matcherOneReturnValue'     => null,
                'matcherTwoReturnValue'     => false,
                'matcherThreeReturnValue'   => null,
                'matcherTwoIsCalled'        => true,
                'matcherThreeIsCalled'      => false,
                'expectNewSplitTestStarted' => false,
            ],
        ];
    }

    #[DataProvider('activationMatchersDataProvider')]
    public function testActivationMatchers(
        ?bool $matcherOneReturnValue,
        ?bool $matcherTwoReturnValue,
        ?bool $matcherThreeReturnValue,
        bool $matcherTwoIsCalled,
        bool $matcherThreeIsCalled,
        bool $expectNewSplitTestStarted
    ): void
    {
        $this->activeTrackingEntryHelperStub
            ->getTrackingEntryStubBuilder()
            ->setIsEmpty(false)
            ->setQuery('pizza')
            ->setActiveSplitTest(null);

        $applicationTimezone = TimezoneEnum::AMSTERDAM->toDateTimeZone();
        $dateToday = new \DateTime('2020-09-05 00:00:00', $applicationTimezone);
        $this->dateTimeFactoryStub->setDateTime($dateToday);

        $splitTests = [
            new SplitTestSettings(
                1337,
                new SplitTestActivation(
                    null,
                    null,
                    $dateToday,
                    null,
                    [],
                    [],
                ),
                new SplitTestChannels('advertised_ab_ta', 'landing_ab_ta'),
                [],
            ),
        ];
        $this->splitTestListSettingsRepositoryMock->method('getSplitTestListSettings')->willReturn(
            new SplitTestListSettings($splitTests),
        );
        $this->websiteSettingsHelperMock->method('getSettings')->willReturn(
            $this->getWebsiteSettings(
                true,
                ContractType::DIRECT,
            ),
        );

        $splitTestExtendedMock = $this->createMock(SplitTestExtendedWriterInterface::class);
        $splitTestExtendedMock
            ->expects($expectNewSplitTestStarted ? $this->once() : $this->never())
            ->method('setVariant');

        $splitTestActivationMatcherOneMock = $this->createMock(SplitTestActivationMatcherInterface::class);
        $splitTestActivationMatcherOneMock
            ->expects($this->atLeastOnce())
            ->method('splitTestMatchesActivationConditions')
            ->willReturn($matcherOneReturnValue);

        $splitTestActivationMatcherTwoMock = $this->createMock(SplitTestActivationMatcherInterface::class);
        $splitTestActivationMatcherTwoMock
            ->expects($matcherTwoIsCalled ? $this->atLeastOnce() : $this->never())
            ->method('splitTestMatchesActivationConditions')
            ->willReturn($matcherTwoReturnValue);

        $splitTestActivationMatcherThreeMock = $this->createMock(SplitTestActivationMatcherInterface::class);
        $splitTestActivationMatcherThreeMock
            ->expects($matcherThreeIsCalled ? $this->atLeastOnce() : $this->never())
            ->method('splitTestMatchesActivationConditions')
            ->willReturn($matcherThreeReturnValue);

        $splitTestActivationMatchers = [
            $splitTestActivationMatcherOneMock,
            $splitTestActivationMatcherTwoMock,
            $splitTestActivationMatcherThreeMock,
        ];

        $splitTestActivateHelper = new SplitTestActivateHelper(
            $splitTestExtendedMock,
            $splitTestActivationMatchers,
            $this->adBotRequestStub,
            $this->searchRequestStub,
            $this->debugRequestStub,
            $this->splitTestListSettingsRepositoryMock,
            $this->randomPercentageGeneratorMock,
            $this->activeTrackingEntryHelperStub,
            $this->activeSplitTestFactory,
            $this->friendlyBotRequestStub,
        );

        $splitTestActivateHelper->startSplitTest();
    }

    private function getWebsiteSettings(
        bool $googleAdSenseEnabled,
        ContractType $contractType
    ): WebsiteSettingsStub
    {
        $websiteSettingsStub = new WebsiteSettingsStub();
        $websiteSettingsStub->getGoogleAdSense()
            ->setEnabled($googleAdSenseEnabled)
            ->setContractType($contractType);

        return $websiteSettingsStub;
    }
}
