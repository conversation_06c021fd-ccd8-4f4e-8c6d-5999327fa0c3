<?php

declare(strict_types=1);

namespace Tests\Unit\DisplaySearch\Settings;

use App\DisplaySearch\Settings\DisplaySearchSettingsFactory;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Unit\Module\AbstractModuleSettingsFactoryTestCase;

final class DisplaySearchSettingsFactoryTest extends AbstractModuleSettingsFactoryTestCase
{
    private DisplaySearchSettingsFactory $displaySearchSettingsFactory;

    private DebugRequestStub $debugRequestStub;

    protected function setUp(): void
    {
        parent::setUp();

        $this->debugRequestStub = new DebugRequestStub();

        $this->displaySearchSettingsFactory = new DisplaySearchSettingsFactory(
            $this->websiteConfigurationHelperMock,
            $this->debugRequestStub,
        );
    }

    public function testDisabled(): void
    {
        $this->setBrandConfig([]);
        $this->debugRequestStub->setEnableModule(false);

        $displaySearchSettings = $this->displaySearchSettingsFactory->create();

        self::assertFalse($displaySearchSettings->enabled);
    }

    public function testEnabled(): void
    {
        $this->setBrandConfig(
            [
                'display_search' => [
                    'enabled' => true,
                ],
            ],
        );

        $displaySearchSettings = $this->displaySearchSettingsFactory->create();

        self::assertTrue($displaySearchSettings->enabled);
    }
}
