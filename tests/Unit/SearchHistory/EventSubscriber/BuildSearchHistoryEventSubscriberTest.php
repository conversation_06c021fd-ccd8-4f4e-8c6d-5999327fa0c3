<?php

declare(strict_types=1);

namespace Tests\Unit\SearchHistory\EventSubscriber;

use App\AdBot\Request\AdBotRequestInterface;
use App\JsonTemplate\Event\JsonTemplateSearchSubmittedEvent;
use App\Preferences\Helper\PreferencesHelper;
use App\SearchHistory\EventSubscriber\BuildSearchHistoryEventSubscriber;
use App\SearchHistory\Helper\SearchHistoryHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

final class BuildSearchHistoryEventSubscriberTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function searchHistoryDataProvider(): array
    {
        return [
            'adds query when allowed' => [
                'isAdBot' => false,
                'previousSearchesEnabled' => true,
                'expectAddQuery' => true,
            ],
            'does nothing for ad bot' => [
                'isAdBot' => true,
                'previousSearchesEnabled' => true,
                'expectAddQuery' => false,
            ],
            'does nothing when history disabled' => [
                'isAdBot' => false,
                'previousSearchesEnabled' => false,
                'expectAddQuery' => false,
            ],
            'does nothing for ad bot with history disabled' => [
                'isAdBot' => true,
                'previousSearchesEnabled' => false,
                'expectAddQuery' => false,
            ],
        ];
    }

    #[DataProvider('searchHistoryDataProvider')]
    public function testAddDefaultSearchQuery(
        bool $isAdBot,
        bool $previousSearchesEnabled,
        bool $expectAddQuery
    ): void {
        $searchHistoryHelperMock = $this->createMock(SearchHistoryHelper::class);
        $preferencesHelperMock = $this->createMock(PreferencesHelper::class);
        $adBotRequestMock = $this->createMock(AdBotRequestInterface::class);

        $adBotRequestMock->method('isAdBot')->willReturn($isAdBot);
        $preferencesHelperMock->method('getPreviousSearches')->willReturn($previousSearchesEnabled);

        $response = new Response();

        if ($expectAddQuery) {
            $searchHistoryHelperMock->expects($this->once())
                ->method('addQuery')
                ->with('query', $response);
        } else {
            $searchHistoryHelperMock->expects($this->never())->method('addQuery');
        }

        $event = new JsonTemplateSearchSubmittedEvent('query', $response);
        $subscriber = new BuildSearchHistoryEventSubscriber($searchHistoryHelperMock, $preferencesHelperMock, $adBotRequestMock);
        $subscriber->addDefaultSearchQuery($event);
    }
}
