<?php

declare(strict_types=1);

namespace Tests\Stub\ImageSearch\Request;

use App\ImageSearch\Filter\ImageColorFilter;
use App\ImageSearch\Filter\ImagePeriodFilter;
use App\ImageSearch\Filter\ImageSizeFilter;
use App\ImageSearch\Filter\ImageTypeFilter;
use App\ImageSearch\Request\ImageSearchRequestInterface;

final class ImageSearchRequestStub implements ImageSearchRequestInterface
{
    private string $colorFilter;

    private string $periodFilter;

    private string $imageTypeFilter;

    private string $imageSizeFilter;

    public function __construct()
    {
        $this->colorFilter = ImageColorFilter::SUPPORTED_VALUES[array_rand(ImageColorFilter::SUPPORTED_VALUES)];
        $this->periodFilter = ImagePeriodFilter::SUPPORTED_VALUES[array_rand(ImagePeriodFilter::SUPPORTED_VALUES)];
        $this->imageTypeFilter = ImageTypeFilter::SUPPORTED_VALUES[array_rand(ImageTypeFilter::SUPPORTED_VALUES)];
        $this->imageSizeFilter = ImageSizeFilter::SUPPORTED_VALUES[array_rand(ImageSizeFilter::SUPPORTED_VALUES)];
    }

    public function getPeriod(): string
    {
        return $this->periodFilter;
    }

    public function getColor(): string
    {
        return $this->colorFilter;
    }

    public function getImageType(): string
    {
        return $this->imageTypeFilter;
    }

    public function getImageSize(): string
    {
        return $this->imageSizeFilter;
    }

    public function setPeriodFilter(string $periodFilter): self
    {
        $this->periodFilter = $periodFilter;

        return $this;
    }

    public function setColorFilter(string $colorFilter): self
    {
        $this->colorFilter = $colorFilter;

        return $this;
    }

    public function setImageTypeFilter(string $imageTypeFilter): self
    {
        $this->imageTypeFilter = $imageTypeFilter;

        return $this;
    }

    public function setImageSizeFilter(string $imageSizeFilter): self
    {
        $this->imageSizeFilter = $imageSizeFilter;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getValues(): array
    {
        return array_filter($this->toArray(), static fn ($value) => $value !== null);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_COLOR      => $this->getColor(),
            self::PARAMETER_PERIOD     => $this->getPeriod(),
            self::PARAMETER_IMAGE_TYPE => $this->getImageType(),
            self::PARAMETER_IMAGE_SIZE => $this->getImageSize(),
        ];
    }
}
