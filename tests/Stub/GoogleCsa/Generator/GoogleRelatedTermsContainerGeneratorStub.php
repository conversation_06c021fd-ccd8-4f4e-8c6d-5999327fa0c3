<?php

declare(strict_types=1);

namespace Tests\Stub\GoogleCsa\Generator;

use App\GoogleCsa\Generator\GoogleRelatedTermsContainerGeneratorInterface;
use App\JsonTemplate\Component\ComponentInterface;

final class GoogleRelatedTermsContainerGeneratorStub implements GoogleRelatedTermsContainerGeneratorInterface
{
    private static int $containerCount = 0;

    /** @var array<string, string> */
    private array $componentContainerMapping = [];

    public function generateContainerForComponent(
        ComponentInterface $component,
        ?string $suffix
    ): string
    {
        if (isset($this->componentContainerMapping[$component->getId()])) {
            return $this->componentContainerMapping[$component->getId()];
        }

        self::$containerCount++;

        if ($suffix !== null) {
            return $this->componentContainerMapping[$component->getId()] = sprintf(
                self::CONTAINER_SUFFIXED_TEMPLATE,
                $suffix,
                self::$containerCount
            );
        }

        return $this->componentContainerMapping[$component->getId()] = sprintf(self::CONTAINER_TEMPLATE, self::$containerCount);
    }

    public function reset(): void
    {
        self::$containerCount = 0;
    }
}
