<?php

declare(strict_types=1);

namespace Tests\Stub\GoogleCsa;

use Visymo\GoogleCsa\GoogleCsa;

class GoogleCsaStubBuilder
{
    private string $publisherId;

    private ?string $query = null;

    private bool $adTest = false;

    public function withPublisherId(string $publisherId): self
    {
        $this->publisherId = $publisherId;

        return $this;
    }

    public function withQuery(string $query): self
    {
        $this->query = $query;

        return $this;
    }

    public function withAdTest(bool $adTest): self
    {
        $this->adTest = $adTest;

        return $this;
    }

    public function create(): GoogleCsa
    {
        return new GoogleCsa(
            publisherId: $this->publisherId,
            query      : $this->query,
            adTest     : $this->adTest,
        );
    }
}
