<?php

declare(strict_types=1);

namespace Tests\Stub\Vendor;

use GuzzleHttp\ClientInterface;
use GuzzleHttp\Promise\PromiseInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

class GuzzleClientStub implements ClientInterface
{
    private ResponseInterface $requestResponse;

    /** @var mixed[] */
    private array $capturedRequestCalls = [];

    /**
     * @inheritDoc
     *
     * @param mixed[] $options
     */
    public function send(RequestInterface $request, array $options = []): ResponseInterface
    {
        throw new \RuntimeException(sprintf('%s is not implemented', __METHOD__));
    }

    /**
     * @inheritDoc
     *
     * @param mixed[] $options
     */
    public function sendAsync(RequestInterface $request, array $options = []): PromiseInterface
    {
        throw new \RuntimeException(sprintf('%s is not implemented', __METHOD__));
    }

    /**
     * @inheritDoc
     *
     * @param mixed[] $options
     */
    public function request(string $method, $uri, array $options = []): ResponseInterface
    {
        if (!isset($this->requestResponse)) {
            throw new \RuntimeException(sprintf('%s is not implemented', __METHOD__));
        }

        $this->capturedRequestCalls[] = [
            'method'  => $method,
            'uri'     => $uri,
            'options' => $options,
        ];

        return $this->requestResponse;
    }

    public function setRequestResponse(ResponseInterface $response): self
    {
        $this->requestResponse = $response;

        return $this;
    }

    /**
     * @return mixed[]
     */
    public function getCapturedRequestCalls(): array
    {
        return $this->capturedRequestCalls;
    }

    /**
     * @inheritDoc
     *
     * @param mixed[] $options
     */
    public function requestAsync(string $method, $uri, array $options = []): PromiseInterface
    {
        throw new \RuntimeException(sprintf('%s is not implemented', __METHOD__));
    }

    public function getConfig(?string $option = null): never
    {
        throw new \RuntimeException(sprintf('%s is not implemented', __METHOD__));
    }
}
