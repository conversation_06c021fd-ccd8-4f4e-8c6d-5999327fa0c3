<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\Settings;

use App\Domain\Settings\DomainSettings;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class DomainSettingsStubBuilder
{
    public const array TEST_HOSTS = [
        'unit.test.visymo.com',
        'integration.test.visymo.com',
        'zoekdan.test.visymo.com',
        'zoekenzoekenniet\vinden.test.visymo.com',
        'zoekie-zoekie.test.visymo.com',
    ];

    private string $host;

    private bool $javaScriptRelatedTermsEnabled;

    public function __construct()
    {
        $this->host = TestStubRandomizer::pickItem(self::TEST_HOSTS);
        $this->javaScriptRelatedTermsEnabled = TestStubRandomizer::pickBoolean();
    }

    public function setHost(string $host): self
    {
        $this->host = $host;

        return $this;
    }

    public function setJavaScriptRelatedTermsEnabled(bool $javaScriptRelatedTermsEnabled): self
    {
        $this->javaScriptRelatedTermsEnabled = $javaScriptRelatedTermsEnabled;

        return $this;
    }

    public function create(): DomainSettings
    {
        return new DomainSettings(
            host                         : $this->host,
            javaScriptRelatedTermsEnabled: $this->javaScriptRelatedTermsEnabled,
        );
    }
}
