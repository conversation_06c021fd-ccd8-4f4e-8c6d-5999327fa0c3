<?php

declare(strict_types=1);

namespace Tests\Stub\Cheq\Settings;

use App\Cheq\Settings\CheqSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method CheqSettings create()
 */
final class CheqSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    protected function createSettings(): CheqSettings
    {
        return new CheqSettings(
            enabled: $this->enabled,
        );
    }
}
