<?php

declare(strict_types=1);

namespace Tests\Stub\RelatedTerms\Request;

use App\RelatedTerms\Request\RelatedTermsRequestInterface;
use App\RelatedTerms\Request\RelatedTermsZone;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class RelatedTermsRequestStub implements RelatedTermsRequestInterface
{
    /** @var string[]|null */
    private ?array $relatedTerms;

    private ?string $alternateRelatedQuery;

    private ?RelatedTermsZone $relatedTermsZone;

    private ?int $relatedTermsLinkIndex;

    public function __construct()
    {
        if (TestStubRandomizer::pickBoolean()) {
            $this->relatedTerms = TestStubRandomizer::pickItems(
                [
                    'pizza',
                    'hawaii',
                    'salami',
                    'ananas',
                    'cheese',
                ],
                TestStubRandomizer::createInt(1, 5),
            );

            $this->alternateRelatedQuery = TestStubRandomizer::createString('arq');
            $this->relatedTermsZone = TestStubRandomizer::pickNullableItem(RelatedTermsZone::cases());
            $this->relatedTermsLinkIndex = TestStubRandomizer::createNullableInt();
        } else {
            $this->relatedTerms = null;
            $this->alternateRelatedQuery = null;
            $this->relatedTermsZone = null;
            $this->relatedTermsLinkIndex = null;
        }
    }

    /**
     * @inheritDoc
     */
    public function getRelatedTerms(): ?array
    {
        return $this->relatedTerms;
    }

    /**
     * @param string[]|null $relatedTerms
     */
    public function setRelatedTerms(?array $relatedTerms): self
    {
        $this->relatedTerms = $relatedTerms;

        return $this;
    }

    public function setAlternateRelatedQuery(?string $query): self
    {
        $this->alternateRelatedQuery = $query;

        return $this;
    }

    public function getAlternateRelatedQuery(): ?string
    {
        return $this->alternateRelatedQuery;
    }

    public function getRelatedTermsZone(): ?RelatedTermsZone
    {
        return $this->relatedTermsZone;
    }

    public function setRelatedTermsZone(RelatedTermsZone|string|null $relatedTermsZone): self
    {
        if (is_string($relatedTermsZone)) {
            $relatedTermsZone = RelatedTermsZone::from($relatedTermsZone);
        }

        $this->relatedTermsZone = $relatedTermsZone;

        return $this;
    }

    public function getRelatedTermsLinkIndex(): ?int
    {
        return $this->relatedTermsLinkIndex;
    }

    public function setRelatedTermsLinkIndex(?int $relatedTermsLinkIndex): self
    {
        $this->relatedTermsLinkIndex = $relatedTermsLinkIndex;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getUrlParameters(): array
    {
        return array_filter(
            $this->toArray(),
            static fn ($value) => $value !== null,
        );
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_TERMS                    => $this->getRelatedTerms(),
            self::PARAMETER_ALTERNATE_RELATED_QUERY  => $this->getAlternateRelatedQuery(),
            self::PARAMETER_RELATED_TERMS_ZONE       => $this->getRelatedTermsZone()?->value,
            self::PARAMETER_RELATED_TERMS_LINK_INDEX => $this->getRelatedTermsLinkIndex(),
        ];
    }
}
