<?php

declare(strict_types=1);

namespace Tests\Stub\ConversionTracking\Endpoint\GoogleAdSense;

use App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class GoogleAdSenseRequestStub implements GoogleAdSenseRequestInterface
{
    private ?int $block;

    private ?int $ad;

    public function __construct()
    {
        $this->block = TestStubRandomizer::createNullableInt();
        $this->ad = TestStubRandomizer::createNullableInt();
    }

    public function getBlock(): ?int
    {
        return $this->block;
    }

    public function setBlock(?int $block): self
    {
        $this->block = $block;

        return $this;
    }

    public function getAd(): ?int
    {
        return $this->ad;
    }

    public function setAd(?int $ad): self
    {
        $this->ad = $ad;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_BLOCK => $this->getBlock(),
            self::PARAMETER_AD    => $this->getAd(),
        ];
    }
}
