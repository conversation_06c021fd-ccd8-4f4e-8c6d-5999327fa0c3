<?php

declare(strict_types=1);

namespace Tests\Stub\ConversionTracking\Endpoint\GoogleRelatedTerms;

use App\ConversionTracking\Endpoint\GoogleRelatedTerms\GoogleRelatedTermsConversionUrlGeneratorInterface;

final class GoogleRelatedTermsConversionUrlGeneratorStub implements GoogleRelatedTermsConversionUrlGeneratorInterface
{
    private string $url;

    public function generate(string $adClientId): string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }
}
