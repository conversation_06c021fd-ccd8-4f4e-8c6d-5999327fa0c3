<?php

declare(strict_types=1);

namespace Tests\Stub\ConversionTracking\Endpoint\GoogleRelatedTerms;

use App\ConversionTracking\Endpoint\GoogleRelatedTerms\GoogleRelatedTermsRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class GoogleRelatedTermsRequestStub implements GoogleRelatedTermsRequestInterface
{
    private ?bool $supportsTracking;

    public function __construct()
    {
        $this->supportsTracking = TestStubRandomizer::pickBoolean();
    }

    public function supportsTracking(): ?bool
    {
        return $this->supportsTracking;
    }

    public function setSupportsTracking(?bool $supportsTracking): self
    {
        $this->supportsTracking = $supportsTracking;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_SUPPORTS_TRACKING => $this->supportsTracking(),
        ];
    }
}
