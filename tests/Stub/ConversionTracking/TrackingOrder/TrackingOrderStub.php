<?php

declare(strict_types=1);

namespace Tests\Stub\ConversionTracking\TrackingOrder;

use App\ConversionTracking\TrackingOrder\AbstractTrackingOrder;

class TrackingOrderStub extends AbstractTrackingOrder
{
    public function __construct(
        private readonly string $id,
        private readonly string $type,
        private readonly bool $supportsOnlineConversion,
        private readonly ?string $clickCountPrefix
    )
    {
        parent::__construct($this->id);
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function supportsOnlineConversion(int $clickCount): bool
    {
        return $this->supportsOnlineConversion;
    }

    public function getClickCountPrefix(): ?string
    {
        return $this->clickCountPrefix;
    }
}
