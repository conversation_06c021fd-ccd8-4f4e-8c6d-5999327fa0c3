<?php

declare(strict_types=1);

namespace Tests\Stub\ConversionTracking\Settings;

use App\ConversionTracking\Settings\ConversionTrackingSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method ConversionTrackingSettings create()
 */
final class ConversionTrackingSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $debugMode = false;

    public function setDebugMode(bool $debugMode): self
    {
        $this->debugMode = $debugMode;

        return $this;
    }

    protected function createSettings(): ConversionTrackingSettings
    {
        return new ConversionTrackingSettings(
            debugMode: $this->debugMode,
        );
    }
}
