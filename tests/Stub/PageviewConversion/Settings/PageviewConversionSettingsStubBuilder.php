<?php

declare(strict_types=1);

namespace Tests\Stub\PageviewConversion\Settings;

use App\PageviewConversion\Settings\PageviewConversionSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method PageviewConversionSettings create()
 */
final class PageviewConversionSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    private bool $enabledForRequest = false;

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setEnabledForRequest(bool $enabledForRequest): self
    {
        $this->enabledForRequest = $enabledForRequest;

        return $this;
    }

    protected function createSettings(): PageviewConversionSettings
    {
        return new PageviewConversionSettings(
            enabled          : $this->enabled,
            enabledForRequest: $this->enabledForRequest,
        );
    }
}
