<?php

declare(strict_types=1);

namespace Tests\Stub\SpamClickDetect\Settings;

use App\SpamClickDetect\Settings\SpamClickDetectSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method SpamClickDetectSettings create()
 */
final class SpamClickDetectSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    private bool $enabledForRequest = false;

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setEnabledForRequest(bool $enabledForRequest): self
    {
        $this->enabledForRequest = $enabledForRequest;

        return $this;
    }

    protected function createSettings(): SpamClickDetectSettings
    {
        return new SpamClickDetectSettings(
            enabled          : $this->enabled,
            enabledForRequest: $this->enabledForRequest,
        );
    }
}
