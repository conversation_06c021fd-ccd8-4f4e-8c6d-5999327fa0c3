<?php

declare(strict_types=1);

namespace Tests\Stub\WebsiteSettings\Settings\MicrosoftAdsConversionTracking;

use App\WebsiteSettings\Settings\MicrosoftAdsConversionTracking\MicrosoftAdsConversionTrackingSettings;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

class MicrosoftAdsConversionTrackingSettingsStub extends MicrosoftAdsConversionTrackingSettings
{
    public function __construct()
    {
        parent::__construct(
            TestStubRandomizer::pickBoolean(),
            random_int(1, 654),
            TestStubRandomizer::pickItem([null, sprintf('random-conversion-tracking-label-%s', mt_rand())]),
        );
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setConversionTrackingId(int $conversionTrackingId): self
    {
        $this->conversionTrackingId = $conversionTrackingId;

        return $this;
    }

    public function setConversionTrackingLabel(?string $conversionTrackingLabel): self
    {
        $this->conversionTrackingLabel = $conversionTrackingLabel;

        return $this;
    }
}
