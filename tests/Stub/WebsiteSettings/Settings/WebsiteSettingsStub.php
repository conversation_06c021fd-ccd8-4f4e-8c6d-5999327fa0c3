<?php

declare(strict_types=1);

namespace Tests\Stub\WebsiteSettings\Settings;

use App\WebsiteSettings\Settings\BingAds\BingAdsSettings;
use App\WebsiteSettings\Settings\ConversionLog\ConversionLogSettings;
use App\WebsiteSettings\Settings\GoogleAdsConversionTracking\GoogleAdsConversionTrackingSettings;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettings;
use App\WebsiteSettings\Settings\MicrosoftAdsConversionTracking\MicrosoftAdsConversionTrackingSettings;
use App\WebsiteSettings\Settings\WebsiteSettings;
use Tests\Stub\WebsiteSettings\Settings\BingAds\BingAdsSettingsStub;
use Tests\Stub\WebsiteSettings\Settings\ConversionLog\ConversionLogSettingsStub;
use Tests\Stub\WebsiteSettings\Settings\ConversionTracking\ConversionTrackingSettingsStub;
use Tests\Stub\WebsiteSettings\Settings\GoogleAdsConversionTracking\GoogleAdsConversionTrackingSettingsStub;
use Tests\Stub\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettingsStub;
use Tests\Stub\WebsiteSettings\Settings\MicrosoftAdsConversionTracking\MicrosoftAdsConversionTrackingSettingsStub;

/**
 * @method ConversionLogSettingsStub getConversionLog()
 * @method ConversionTrackingSettingsStub getConversionTracking()
 * @method BingAdsSettingsStub getBingAds()
 * @method GoogleAdSenseSettingsStub getGoogleAdSense()
 * @method GoogleAdsConversionTrackingSettingsStub getGoogleAdsConversionTracking()
 * @method MicrosoftAdsConversionTrackingSettingsStub getMicrosoftAdsConversionTracking()
 */
class WebsiteSettingsStub extends WebsiteSettings
{
    public function __construct()
    {
        parent::__construct(
            new ConversionLogSettingsStub(),
            new ConversionTrackingSettingsStub(),
            new BingAdsSettingsStub(),
            new GoogleAdSenseSettingsStub(),
            new GoogleAdsConversionTrackingSettingsStub(),
            new MicrosoftAdsConversionTrackingSettingsStub(),
        );
    }

    public function setConversionLog(ConversionLogSettings $conversionLog): self
    {
        $this->conversionLog = $conversionLog;

        return $this;
    }

    public function setBingAds(BingAdsSettings $bingAds): self
    {
        $this->bingAds = $bingAds;

        return $this;
    }

    public function setGoogleAdSense(GoogleAdSenseSettings $googleAdSense): self
    {
        $this->googleAdSense = $googleAdSense;

        return $this;
    }

    public function setGoogleAdsConversionTracking(
        GoogleAdsConversionTrackingSettings $googleAdsConversionTracking
    ): self
    {
        $this->googleAdsConversionTracking = $googleAdsConversionTracking;

        return $this;
    }

    public function setMicrosoftAdsConversionTracking(
        MicrosoftAdsConversionTrackingSettings $microsoftAdsConversionTracking
    ): self
    {
        $this->microsoftAdsConversionTracking = $microsoftAdsConversionTracking;

        return $this;
    }
}
