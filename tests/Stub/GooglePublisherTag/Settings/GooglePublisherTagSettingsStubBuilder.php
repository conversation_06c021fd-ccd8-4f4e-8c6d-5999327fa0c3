<?php

declare(strict_types=1);

namespace Tests\Stub\GooglePublisherTag\Settings;

use App\GooglePublisherTag\Settings\GooglePublisherTagSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method GooglePublisherTagSettings create()
 */
final class GooglePublisherTagSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    private ?string $adUnitPath = null;

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setAdUnitPath(?string $adUnitPath): self
    {
        $this->adUnitPath = $adUnitPath;

        return $this;
    }

    protected function createSettings(): GooglePublisherTagSettings
    {
        return new GooglePublisherTagSettings(
            enabled   : $this->enabled,
            adUnitPath: $this->adUnitPath,
        );
    }
}
