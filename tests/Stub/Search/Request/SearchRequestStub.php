<?php

declare(strict_types=1);

namespace Tests\Stub\Search\Request;

use App\Search\Request\SearchRequestFlag;
use App\Search\Request\SearchRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class SearchRequestStub implements SearchRequestInterface
{
    private ?string $query;

    private bool $ignoreQueryForSearch;

    private int $page;

    private int $searchRequestRate;

    private bool $landingPage;

    private bool $seoPage;

    private string $route;

    public function __construct()
    {
        $this->query = TestStubRandomizer::createNullableString();
        $this->ignoreQueryForSearch = TestStubRandomizer::pickBoolean();
        $this->page = TestStubRandomizer::createInt();
        $this->searchRequestRate = TestStubRandomizer::createInt();
        $this->setIsLandingPage(TestStubRandomizer::pickBoolean());
        $this->setIsSeoPage(TestStubRandomizer::pickBoolean());
    }

    public function reset(): self
    {
        $this->query = null;
        $this->ignoreQueryForSearch = false;
        $this->page = 1;
        $this->searchRequestRate = 0;
        $this->setIsLandingPage(false);

        return $this;
    }

    public function getQuery(): ?string
    {
        return $this->query;
    }

    public function setQuery(?string $query): self
    {
        $this->query = $query;

        return $this;
    }

    public function ignoreQueryForSearch(): bool
    {
        return $this->ignoreQueryForSearch;
    }

    public function setIgnoreQueryForSearch(bool $ignoreQueryForSearch = true): self
    {
        $this->ignoreQueryForSearch = $ignoreQueryForSearch;

        return $this;
    }

    public function isLandingPage(): bool
    {
        return $this->landingPage;
    }

    public function setIsLandingPage(bool $isLandingPage): self
    {
        $this->landingPage = $isLandingPage;
        $this->route = $this->landingPage ? 'route_web_search' : 'route_home';

        return $this;
    }

    public function isSeoPage(): bool
    {
        return $this->seoPage;
    }

    public function setIsSeoPage(bool $isSeoPage = true): self
    {
        $this->seoPage = $isSeoPage;

        return $this;
    }

    public function isArticle(): bool
    {
        return $this->route === 'route_article';
    }

    public function setArticle(): self
    {
        $this->route = 'route_article';
        $this->landingPage = true;

        return $this;
    }

    public function isDisplaySearch(): bool
    {
        return $this->route === 'route_display_search';
    }

    public function setDisplaySearch(): self
    {
        $this->route = 'route_display_search';
        $this->landingPage = true;

        return $this;
    }

    public function isDisplaySearchAdvertised(): bool
    {
        return $this->route === 'route_display_search_advertised';
    }

    public function setDisplaySearchAdvertised(): self
    {
        $this->route = 'route_display_search_advertised';
        $this->landingPage = true;

        return $this;
    }

    public function isDisplaySearchRelated(): bool
    {
        return $this->route === 'route_display_search_related';
    }

    public function setDisplaySearchRelated(): self
    {
        $this->route = 'route_display_search_related';

        return $this;
    }

    public function isDisplaySearchRelatedWeb(): bool
    {
        return $this->route === 'route_display_search_related_web';
    }

    public function setDisplaySearchRelatedWeb(): self
    {
        $this->route = 'route_display_search_related_web';

        return $this;
    }

    public function isMicrosoftSearchRelatedWeb(): bool
    {
        return $this->route === 'route_microsoft_search_related_web';
    }

    public function setMicrosoftSearchRelatedWeb(): self
    {
        $this->route = 'route_microsoft_search_related_web';
        $this->landingPage = true;

        return $this;
    }

    public function isWebSearch(): bool
    {
        return $this->route === 'route_web_search';
    }

    public function setWebSearch(): self
    {
        $this->route = 'route_web_search';
        $this->landingPage = true;

        return $this;
    }

    public function isWebSearchAdvertised(): bool
    {
        return $this->route === 'route_web_search_advertised';
    }

    public function setWebSearchAdvertised(): self
    {
        $this->route = 'route_web_search_advertised';

        return $this;
    }

    public function getQueryAsString(): string
    {
        return (string)$this->getQuery();
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function setPage(?int $page): self
    {
        $this->page = $page ?? 1;

        return $this;
    }

    public function getSearchRequestRate(): int
    {
        return $this->searchRequestRate;
    }

    public function setSearchRequestRate(int $searchRequestRate): self
    {
        $this->searchRequestRate = $searchRequestRate;

        return $this;
    }

    public function isRequestedPageAllowed(): bool
    {
        $requestedPage = $this->getPage();

        if ($requestedPage > SearchRequestInterface::MAX_PAGE_SIZE) {
            // reset page, otherwise it will also be used on the error page
            $this->page = 1;

            return false;
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function getUrlParameters(): array
    {
        return array_filter(
            [
                self::PARAMETER_QUERY => $this->getQuery(),
                self::PARAMETER_PAGE  => $this->getPage(),
            ],
            static fn ($value) => $value !== null,
        );
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_QUERY                      => $this->getQuery(),
            SearchRequestFlag::IGNORE_QUERY_FOR_SEARCH => $this->ignoreQueryForSearch(),
            self::PARAMETER_PAGE                       => $this->getPage(),
            self::HEADER_SEARCH_REQUEST_RATE           => $this->getSearchRequestRate(),
        ];
    }
}
