<?php

declare(strict_types=1);

namespace Tests\Stub\Redis;

use App\Redis\RedisClient;

class RedisClientStub extends RedisClient
{
    /** @var mixed[] */
    private array $cache = [];

    public function __construct()
    {
        parent::__construct('dsn', true);
    }

    /**
     * @inheritDoc
     *
     * @phpstan-ignore-next-line
     */
    public function get(string $key)
    {
        return $this->cache[$key] ?? false;
    }

    /**
     * @inheritDoc
     *
     * @phpstan-ignore-next-line
     */
    public function set(string $key, $value): bool
    {
        $this->cache[$key] = $value;

        return true;
    }

    /**
     * @inheritDoc
     *
     * @phpstan-ignore-next-line
     */
    public function setex(string $key, int $ttl, $value): bool
    {
        $this->cache[$key] = $value;

        return true;
    }

    /**
     * @inheritDoc
     *
     * @phpstan-ignore-next-line
     */
    public function pttl(string $key): int
    {
        return $this->get($key) === false ? -1 : random_int(1, 2000);
    }

    /**
     * @inheritDoc
     *
     * @phpstan-ignore-next-line
     */
    public function incr(string $key)
    {
        $value = (int)$this->get($key) + 1;

        $this->set($key, $value);

        return $value;
    }

    /**
     * @inheritDoc
     *
     * @phpstan-ignore-next-line
     */
    public function expire(string $key, int $ttl)
    {
        // empty because expire is not supported by stub
    }

    /**
     * @inheritDoc
     */
    public function __call(string $name, array $arguments): bool|int|float|string|array
    {
        throw new \RuntimeException(sprintf('Method "%s" not supported', $name));
    }
}
