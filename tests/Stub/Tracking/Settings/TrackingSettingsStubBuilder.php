<?php

declare(strict_types=1);

namespace Tests\Stub\Tracking\Settings;

use App\Tracking\Settings\TrackingSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method TrackingSettings create()
 */
final class TrackingSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $campaignNameValidationEnabled = false;

    public function setCampaignNameValidationEnabled(bool $campaignNameValidationEnabled): self
    {
        $this->campaignNameValidationEnabled = $campaignNameValidationEnabled;

        return $this;
    }

    protected function createSettings(): TrackingSettings
    {
        return new TrackingSettings(
            campaignNameValidationEnabled: $this->campaignNameValidationEnabled,
        );
    }
}
