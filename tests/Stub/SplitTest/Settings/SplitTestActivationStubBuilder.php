<?php

declare(strict_types=1);

namespace Tests\Stub\SplitTest\Settings;

use App\Generic\Device\Device;
use App\SplitTest\Settings\SplitTestActivation;
use Tests\Stub\Domain\Settings\DomainSettingsStubBuilder;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

class SplitTestActivationStubBuilder
{
    private ?Device $device;

    private ?string $service;

    private \DateTime $dateStart;

    private ?\DateTime $dateEnd;

    /** @var string[] */
    private array $domains;

    /** @var string[] */
    private array $routes;

    private const array TEST_ROUTES = [
        'default_search_landing_page',
        'default_search_related',
        'display_search_related_landing_page',
        'display_search_related_related',
        'web_search_display',
    ];

    public function __construct()
    {
        $this->service = TestStubRandomizer::pickNullableItem(
            [
                SplitTestActivation::SERVICE_VALUE_GOOGLE,
                SplitTestActivation::SERVICE_VALUE_MICROSOFT,
            ],
        );
        $this->device = TestStubRandomizer::pickNullableItem(Device::cases());
        // phpcs:disable Visymo.DateTime.DateTime.MissingTimezoneArgument
        $this->dateStart = new \DateTime(
            datetime: date('Y-m-d H:i:s', time() - mt_rand()),
            timezone: TimezoneEnum::UTC->toDateTimeZone(),
        );
        $this->dateEnd = new \DateTime(
            datetime: date('Y-m-d H:i:s', time() - mt_rand()),
            timezone: TimezoneEnum::UTC->toDateTimeZone(),
        );
        $this->domains = TestStubRandomizer::pickItems(DomainSettingsStubBuilder::TEST_HOSTS, random_int(0, 3));
        $this->routes = TestStubRandomizer::pickItems(self::TEST_ROUTES, random_int(0, 3));
    }

    public function setDevice(?Device $device): self
    {
        $this->device = $device;

        return $this;
    }

    public function setService(?string $service): self
    {
        $this->service = $service;

        return $this;
    }

    public function setDateStart(\DateTime $dateStart): self
    {
        $this->dateStart = $dateStart;

        return $this;
    }

    public function setDateEnd(?\DateTime $dateEnd): self
    {
        $this->dateEnd = $dateEnd;

        return $this;
    }

    /**
     * @param string[] $domains
     *
     * @return $this
     */
    public function setDomains(array $domains): self
    {
        $this->domains = $domains;

        return $this;
    }

    /**
     * @param string[] $routes
     *
     * @return $this
     */
    public function setRoutes(array $routes): self
    {
        $this->routes = $routes;

        return $this;
    }

    public function create(): SplitTestActivation
    {
        return new SplitTestActivation(
            device   : $this->device,
            service  : $this->service,
            dateStart: $this->dateStart,
            dateEnd  : $this->dateEnd,
            domains  : $this->domains,
            routes   : $this->routes,
        );
    }
}
