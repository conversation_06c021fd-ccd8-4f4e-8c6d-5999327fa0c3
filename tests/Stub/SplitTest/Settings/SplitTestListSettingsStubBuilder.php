<?php

declare(strict_types=1);

namespace Tests\Stub\SplitTest\Settings;

use App\SplitTest\Settings\SplitTestListSettings;
use App\SplitTest\Settings\SplitTestSettings;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

/**
 * @method SplitTestSettingsStubBuilder[] getSplitTests()
 * @method SplitTestSettingsStubBuilder|null findById(int $id)
 **/
class SplitTestListSettingsStubBuilder
{
    /** @var SplitTestSettings[] */
    protected array $splitTests = [];

    public function __construct()
    {
        $this->splitTests = TestStubRandomizer::createCollection(
            static fn () => (new SplitTestSettingsStubBuilder())->create(),
            random_int(0, 3),
        );
    }

    public function clearSplitTests(): self
    {
        $this->splitTests = [];

        return $this;
    }

    public function addSplitTest(SplitTestSettings $splitTest): self
    {
        $this->splitTests[$splitTest->getId()] = $splitTest;

        return $this;
    }

    /**
     * @param SplitTestSettings[] $splitTests
     */
    public function setSplitTests(array $splitTests): self
    {
        $this->clearSplitTests();

        foreach ($splitTests as $splitTest) {
            $this->addSplitTest($splitTest);
        }

        return $this;
    }

    public function create(): SplitTestListSettings
    {
        return new SplitTestListSettings(
            splitTests: $this->splitTests,
        );
    }
}
