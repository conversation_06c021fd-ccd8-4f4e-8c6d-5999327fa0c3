<?php

declare(strict_types=1);

namespace Tests\Integration\Cheq;

use App\Cheq\EventSubscriber\CheqEventSubscriber;
use App\Cheq\Settings\CheqSettings;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;

class CheqEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            CheqEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testRenderTemplateHeadersAddsItemWhenEnabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $settings = new CheqSettings(enabled: true);
        $genericRequest = $this->stubs()
            ->request()
            ->getGenericRequest()
            ->setVisitId('visit-123');

        $twig->method('render')
            ->with(
                '@theme/cheq/cheq_scripts.html.twig',
                ['visit_id' => 'visit-123']
            )
            ->willReturn('cheq-content');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new CheqEventSubscriber($twig, $settings, $genericRequest);
        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('cheq-content', $event->getItems()[0]);
    }

    public function testRenderTemplateHeadersDoesNothingWhenDisabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $settings = new CheqSettings(enabled: false);
        $genericRequest = $this->stubs()
            ->request()
            ->getGenericRequest();

        $twig->expects($this->never())->method('render');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new CheqEventSubscriber($twig, $settings, $genericRequest);
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }

    public function testRenderTemplateHeadersOnlyRendersOnce(): void
    {
        $twig = $this->createMock(Environment::class);
        $settings = new CheqSettings(enabled: true);
        $genericRequest = $this->stubs()
            ->request()
            ->getGenericRequest()
            ->setVisitId('visit-123');

        $twig->expects($this->once())
            ->method('render')
            ->with(
                '@theme/cheq/cheq_scripts.html.twig',
                ['visit_id' => 'visit-123']
            )
            ->willReturn('cheq-content');

        $subscriber = new CheqEventSubscriber($twig, $settings, $genericRequest);

        // Call multiple times
        $event1 = new RenderTemplateHeadersEvent();
        $subscriber->renderTemplateHeaders($event1);

        $event2 = new RenderTemplateHeadersEvent();
        $subscriber->renderTemplateHeaders($event2);

        $event3 = new RenderTemplateHeadersEvent();
        $subscriber->renderTemplateHeaders($event3);

        // Should only have content in the first event
        self::assertCount(1, $event1->getItems());
        self::assertSame('cheq-content', $event1->getItems()[0]);
        self::assertEmpty($event2->getItems());
        self::assertEmpty($event3->getItems());
    }
}
