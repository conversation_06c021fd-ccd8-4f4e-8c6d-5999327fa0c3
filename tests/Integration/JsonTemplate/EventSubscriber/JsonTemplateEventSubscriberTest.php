<?php

declare(strict_types=1);

namespace Tests\Integration\JsonTemplate\EventSubscriber;

use App\JsonTemplate\EventSubscriber\JsonTemplateEventSubscriber;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\JsonTemplateViewFactory;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class JsonTemplateEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $request = Request::create('https://id.brand.com/test');
        $this->stubs()->request()->getMainRequest()->setRequest($request);

        self::websiteSettingsTestHelper()->injectFoodBrandWebsiteConfiguration();

        $brandSettingsStub = $this->stubs()->brandSettings();
        $brandSettingsStub->setSlug('food');
        $brandSettingsStub->setPartnerSlug('partner');

        $websiteSettingsStub = $this->stubs()->websiteSettings();
        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);
        self::brandSettingsTestHelper()->injectBrandSettings($brandSettingsStub);
    }

    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateViewCreatedEvent::NAME,
            JsonTemplateEventSubscriber::class,
            'onJsonTemplateViewCreated',
        );
    }

    public function testOnJsonTemplateViewCreated(): void
    {
        /** @var JsonTemplateViewFactory $jsonTemplateViewFactory */
        $jsonTemplateViewFactory = self::getContainer()->get(JsonTemplateViewFactory::class);
        /** @var string $projectDir */
        $projectDir = self::getContainer()->getParameter('kernel.project_dir');

        $view = $jsonTemplateViewFactory->create(
            jsonTemplateFile: '@shared/templates_json/article/article.json',
            response        : new Response(),
        );

        $jsonTemplateEventSubscriber = new JsonTemplateEventSubscriber(
            projectDir: $projectDir,
        );
        $jsonTemplateEventSubscriber->onJsonTemplateViewCreated(
            new JsonTemplateViewCreatedEvent($view),
        );

        $actualHeader = $view->getResponse()->headers->get(
            JsonTemplateEventSubscriber::HEADER_X_JSON_TEMPLATE_FILE
        );
        self::assertNotNull($actualHeader, 'Header should be set');
        self::assertStringContainsString('/templates_json/article/article.json', $actualHeader);
        self::assertStringStartsWith('/', $actualHeader);
    }
}
