{"composite_search_api_requests": [{"content_pages": {"enabled": true, "is_homepage": null, "public_ids": null, "excluded_public_ids": null, "relevant_for_public_id": null, "category_public_id": null, "has_image": null, "sort": "date_created_desc", "page_size": 10, "page": 1, "paragraph_amount": 0, "is_adult": null}, "related_terms": {"enabled": true, "amount": 12}}, {"organic": {"enabled": true, "page": 1, "page_size": 9}}], "container": {"id": "container-0", "type": "container", "layout": "default", "mode": null, "font": null, "components": [{"id": "search_header-1", "type": "search_header", "componentSpaceModifiers": [], "layout": "default", "autofocus": false, "logoDarkMode": false, "logoStyleFilter": null, "showBackgroundOnDesktop": false, "showBackgroundOnMobile": true, "showBackgroundOnTablet": true, "showHamburgerMenuOnDesktop": true, "showHamburgerMenuOnMobile": false, "showHamburgerMenuOnTablet": false, "showMainMenuMoreOnDesktop": false, "showMainMenuMoreOnMobile": true, "showMainMenuMoreOnTablet": false, "showMainMenuOnDesktop": false, "showMainMenuOnMobile": true, "showMainMenuOnTablet": true, "showSubMenuOnDesktop": false, "showSubMenuOnMobile": false, "showSubMenuOnTablet": true, "showSearchQuery": true}, {"id": "columns-15", "type": "columns", "layout": "default", "one": [{"id": "split_test_matches-16", "type": "split_test_matches", "oneOfVariants": ["innomrc"], "matchingSegment": [{"id": "pill_related_terms-17", "type": "pill_related_terms", "componentSpaceModifiers": [], "layout": "default", "amount": 8}], "nonMatchingSegment": []}, {"id": "split_test_matches-18", "type": "split_test_matches", "oneOfVariants": ["innosmrc"], "matchingSegment": [{"id": "pill_related_terms-19", "type": "pill_related_terms", "componentSpaceModifiers": [], "layout": "stacked", "amount": 8}], "nonMatchingSegment": []}, {"id": "is_ad_bot-20", "type": "is_ad_bot", "matchingSegment": [{"id": "web_search_stats_title-21", "type": "web_search_stats_title", "layout": "default", "showRandomStats": true}], "nonMatchingSegment": [{"id": "web_search_stats_title-2", "type": "web_search_stats_title", "layout": "default", "showRandomStats": false}]}, {"id": "dynamic_ads-22", "type": "dynamic_ads", "components": [{"id": "google_ads_top_unit-3", "type": "google_ads_top_unit", "amount": 4, "container": "csa-top"}]}, {"id": "split_test_matches-23", "type": "split_test_matches", "oneOfVariants": ["rtbl"], "matchingSegment": [{"id": "related_terms-24", "type": "related_terms", "componentSpaceModifiers": [], "layout": "pill", "amount": 12, "zone": "i", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}], "nonMatchingSegment": [{"id": "related_terms-4", "type": "related_terms", "componentSpaceModifiers": [], "layout": "default", "amount": 12, "zone": "i", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}]}, {"id": "is_ad_bot-25", "type": "is_ad_bot", "matchingSegment": [{"id": "dynamic_ads-26", "type": "dynamic_ads", "components": [{"id": "google_ads_bottom_unit-27", "type": "google_ads_bottom_unit", "amount": 5, "numRepeated": 4, "container": "csa-bottom"}]}, {"id": "related_terms-28", "type": "related_terms", "componentSpaceModifiers": [], "layout": "default", "amount": 12, "zone": "b", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}], "nonMatchingSegment": [{"id": "organic_results_title-5", "type": "organic_results_title", "componentSpaceModifiers": ["top"], "layout": "default", "showQuery": true}, {"id": "organic_results_with_fallback-29", "type": "organic_results_with_fallback", "resultsComponent": {"id": "content_page_results_as_organic_results-6", "type": "content_page_results_as_organic_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": true, "resultTitleLink": true, "showResultDisplayUrl": true, "maxDescriptionLength": null, "layout": "default", "linkToActiveBrand": false}, "fallbackComponent": {"id": "organic_results-7", "type": "organic_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": true, "resultTitleLink": true, "showResultDisplayUrl": true, "maxDescriptionLength": null, "layout": "default"}}, {"id": "organic_error_message-8", "type": "organic_error_message"}, {"id": "has_organic_results-30", "type": "has_organic_results", "matchingSegment": [{"id": "dynamic_ads-31", "type": "dynamic_ads", "components": [{"id": "google_ads_bottom_unit-9", "type": "google_ads_bottom_unit", "amount": 5, "numRepeated": 4, "container": "csa-bottom"}]}, {"id": "split_test_matches-32", "type": "split_test_matches", "oneOfVariants": ["innomrc", "innosmrc"], "matchingSegment": [], "nonMatchingSegment": [{"id": "split_test_matches-33", "type": "split_test_matches", "oneOfVariants": ["rtbl"], "matchingSegment": [{"id": "related_terms-34", "type": "related_terms", "componentSpaceModifiers": [], "layout": "pill", "amount": 12, "zone": "b", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}], "nonMatchingSegment": [{"id": "related_terms-10", "type": "related_terms", "componentSpaceModifiers": [], "layout": "default", "amount": 12, "zone": "b", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}]}]}, {"id": "search_bar-11", "type": "search_bar", "componentSpaceModifiers": [], "layout": "default", "showSearchQuery": true, "allowStartQuerySearch": true, "autofocus": false}], "nonMatchingSegment": []}, {"id": "organic_pagination-12", "type": "organic_pagination"}]}], "two": [], "three": [], "mainColumn": "one", "section": null, "sectionVisible": true, "sectionCssProperties": []}, {"id": "footer-35", "type": "footer", "layout": "default", "components": [{"id": "footer_logo-13", "type": "footer_logo", "layout": "default", "logoDarkMode": false, "logoStyleFilter": null, "hideOnDesktop": true}, {"id": "footer_navigation-14", "type": "footer_navigation", "componentSpaceModifiers": [], "showAbout": true, "showContact": true, "showCopyright": true, "showDisclaimer": true, "showPrivacy": true, "layout": "borderless", "logoDarkMode": false}]}]}}