var _vrtQuery = '';
var _vrtViewLogUrl = '';
var _vrtPageOptions = {};
var _vrtUnitOptions = {};
var _vrtGoogleLoaded = false;

function _vrtRun() {
    // Dummy code
}


_vrtIgnoredPageParams = _vrtGetIgnoredPageParams(["locale","q","tv","nc"]);
_vrtQuery="Check it out! A nice hotel in Amsterdam";
_vrtViewLogUrl="https://localhost\/p\/related-terms-content\/v1\/lv";
_vrtPageOptions={"pubId": "web_client", "adtest": "on", "hl": "nl", "ivt": false, "ignoredPageParams": window._vrtIgnoredPageParams || null, "referrerAdCreative": "Check it out! A nice hotel in Amsterdam", "relatedSearchTargeting": "content", "resultsPageBaseUrl": "https://localhost\/dsrw?ppid=pageview_id&asid=cmp_123_g&rac=Check%20it%20out!%20A%20nice%20hotel%20in%20Amsterdam&gclid=gclid123123", "resultsPageQueryParam": "q", "terms": "Mickey Mouse,Donald Duck,Disney,Star Wars"};
_vrtUnitOptions={"styleId": 5136846704, "relatedSearches": 10};

try {_vrtRun();} catch (error) {
    (new Image).src = 'https://localhost\/js-error?error=' + encodeURIComponent(error) + '&url=' + encodeURIComponent(window.location.href);

        window.console && window.console.error && window.console.error(error);
}
