<?php

declare(strict_types=1);

namespace Tests\Integration\Home\Controller;

use App\Home\Controller\HomeController;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class HomeControllerTest extends AbstractBrandWebsitesIntegrationTestCase
{
    /**
     * @return mixed[]
     */
    public static function urlDataProvider(): array
    {
        return [
            'home page'     => [
                'url'        => '/',
                'parameters' => [],
            ],
            'direct search' => [
                'url'        => '/',
                'parameters' => [
                    'q' => 'Pizza Hawai',
                ],
            ],
        ];
    }

    /**
     * @param mixed[] $parameters
     */
    #[DataProvider('urlDataProvider')]
    public function testDefaultSearchIndexController(string $url, array $parameters): void
    {
        $url = self::websiteSettingsTestHelper()->prependRandomDomainToUrl($url);

        $this->stubs()->moduleSettings()
            ->getSearch()
            ->setEnabled(true)
            ->setStyleIdDesktop(**********)
            ->setStyleIdMobile(**********)
            ->create();

        $request = Request::create($url, Request::METHOD_GET, $parameters);

        // inject request in dependency container
        $this->handleRequestRoute($request);
        $this->setRequest($request);

        /** @var HomeController $homeController */
        $homeController = self::getContainer()->get(HomeController::class);

        self::assertEquals(
            Response::HTTP_OK,
            $homeController->home()->getStatusCode(),
        );
    }
}
