<?php

declare(strict_types=1);

namespace Tests\Integration\CookieConsent\EventSubscriber;

use App\Component\Generic\CookieConsent\CookieConsentLayout;
use App\Component\Generic\CookieConsent\CookieConsentRenderer;
use App\CookieConsent\EventSubscriber\InjectCookieConsentScriptEventSubscriber;
use App\Template\Event\RenderTemplateFootersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class InjectCookieConsentScriptEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateFootersEvent::NAME,
            InjectCookieConsentScriptEventSubscriber::class,
            'injectCookieConsentScript',
        );
    }

    public function testInjectCookieConsentScriptAddsItemsWhenNotRendered(): void
    {
        $cookieConsentRenderer = $this->createMock(CookieConsentRenderer::class);

        $cookieConsentRenderer->method('hasRendered')->willReturn(false);
        $cookieConsentRenderer->method('getLayout')->willReturn(CookieConsentLayout::BAR);
        $cookieConsentRenderer->method('renderTemplate')
            ->with(CookieConsentLayout::BAR)
            ->willReturn('template-content');
        $cookieConsentRenderer->method('renderScripts')
            ->with(CookieConsentLayout::BAR)
            ->willReturn('scripts-content');

        $event = new RenderTemplateFootersEvent();
        $subscriber = new InjectCookieConsentScriptEventSubscriber($cookieConsentRenderer);
        $subscriber->injectCookieConsentScript($event);

        self::assertCount(2, $event->getItems());
        self::assertSame('template-content', $event->getItems()[0]);
        self::assertSame('scripts-content', $event->getItems()[1]);
    }

    public function testInjectCookieConsentScriptDoesNothingWhenAlreadyRendered(): void
    {
        $cookieConsentRenderer = $this->createMock(CookieConsentRenderer::class);

        $cookieConsentRenderer->method('hasRendered')->willReturn(true);
        $cookieConsentRenderer->expects($this->never())->method('getLayout');
        $cookieConsentRenderer->expects($this->never())->method('renderTemplate');
        $cookieConsentRenderer->expects($this->never())->method('renderScripts');

        $event = new RenderTemplateFootersEvent();
        $subscriber = new InjectCookieConsentScriptEventSubscriber($cookieConsentRenderer);
        $subscriber->injectCookieConsentScript($event);

        self::assertEmpty($event->getItems());
    }
}
