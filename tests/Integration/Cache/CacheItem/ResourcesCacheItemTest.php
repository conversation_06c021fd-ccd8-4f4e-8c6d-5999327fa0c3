<?php

declare(strict_types=1);

namespace Tests\Integration\Cache\CacheItem;

use App\Cache\CacheItem\ResourcesCacheItem;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Config\ConfigCache;
use Symfony\Component\Config\Resource\ResourceInterface;
use Visymo\Serializer\NativePhpIncludeFileSerializer;

class ResourcesCacheItemTest extends TestCase
{
    private ConfigCache & MockObject $configCacheMock;

    private NativePhpIncludeFileSerializer & MockObject $nativePhpIncludeFileSerializerMock;

    /** @var ResourceInterface[] */
    private array $resources;

    private ResourcesCacheItem $resourcesCacheItem;

    protected function setUp(): void
    {
        parent::setUp();

        $this->configCacheMock = $this->createMock(ConfigCache::class);
        $this->nativePhpIncludeFileSerializerMock = $this->createMock(NativePhpIncludeFileSerializer::class);
        $this->resources = [$this->createMock(ResourceInterface::class), $this->createMock(ResourceInterface::class)];

        $this->resourcesCacheItem = new ResourcesCacheItem(
            $this->configCacheMock,
            $this->nativePhpIncludeFileSerializerMock,
            $this->resources,
        );
    }

    /**
     * @return array<string, array<string, bool>>
     */
    public static function isFreshDataProvider(): array
    {
        return [
            'is_fresh'     => [
                'isFresh'         => true,
                'expectedIsFresh' => true,
            ],
            'is_not_fresh' => [
                'isFresh'         => false,
                'expectedIsFresh' => false,
            ],
        ];
    }

    #[DataProvider('isFreshDataProvider')]
    public function testResourcesCacheItemIsFresh(bool $isFresh, bool $expectedIsFresh): void
    {
        $this->configCacheMock->expects(self::once())->method('isFresh')->willReturn($isFresh);
        self::assertSame($expectedIsFresh, $this->resourcesCacheItem->isFresh());
    }

    public function testResourcesCacheItemWrite(): void
    {
        $data = ['test' => 'data'];
        $serializedData = '<?php return [\'test\' => \'data\']; ?>';

        $this->nativePhpIncludeFileSerializerMock->expects(self::once())->method('serialize')->with($data)->willReturn($serializedData);
        $this->configCacheMock->expects(self::once())->method('write')->with($serializedData, $this->resources);

        $this->resourcesCacheItem->write($data);
    }
}
