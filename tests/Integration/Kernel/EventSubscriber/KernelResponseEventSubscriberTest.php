<?php

declare(strict_types=1);

namespace Tests\Integration\Kernel\EventSubscriber;

use App\Debug\EventSubscriber\InjectDebugInfoEventSubscriber;
use App\Http\Response\EventSubscriber\GenericResponseHeadersEventSubscriber;
use App\Http\Response\EventSubscriber\ResponseCachingEventSubscriber;
use App\Http\Response\EventSubscriber\SearchTypeHeaderEventSubscriber;
use App\Http\Response\EventSubscriber\SplitTestHeadersEventSubscriber;
use App\Kernel\EventSubscriber\KernelResponseEventSubscriber;
use App\Kernel\KernelResponseEvent;
use App\Statistics\EventSubscriber\CreateStatisticsLogEventSubscriber;
use App\Tracking\EventSubscriber\SeaResponseHeadersEventSubscriber;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\HttpKernel\KernelEvents;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class KernelResponseEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private EventDispatcherInterface & MockObject $eventDispatcherMock;

    private KernelResponseEventSubscriber $subscriber;

    private const array NON_REDIRECT_RESPONSE_EVENT_LISTENERS = [
        SplitTestHeadersEventSubscriber::class,
        InjectDebugInfoEventSubscriber::class,
        GenericResponseHeadersEventSubscriber::class,
        ResponseCachingEventSubscriber::class,
        SearchTypeHeaderEventSubscriber::class,
        CreateStatisticsLogEventSubscriber::class,
        SeaResponseHeadersEventSubscriber::class,
    ];

    private const array REDIRECT_RESPONSE_EVENT_LISTENERS = [
        GenericResponseHeadersEventSubscriber::class,
        SeaResponseHeadersEventSubscriber::class,
    ];

    protected function setUp(): void
    {
        parent::setUp();

        $this->eventDispatcherMock = $this->createMock(EventDispatcherInterface::class);
        $this->subscriber = new KernelResponseEventSubscriber($this->eventDispatcherMock);
    }

    public function testResponseEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            KernelEvents::RESPONSE,
            KernelResponseEventSubscriber::class,
            'onKernelResponse',
        );

        /** @var EventDispatcher $dispatcher */
        $dispatcher = self::getContainer()->get('event_dispatcher');
        $eventListeners = $dispatcher->getListeners(KernelEvents::RESPONSE);
        /** @var string $projectDir */
        $projectDir = self::getContainer()->getParameter('kernel.project_dir');

        $ownEventListenersCount = 0;

        foreach ($eventListeners as $eventListener) {
            if (!is_array($eventListener)) {
                continue;
            }

            $eventListenerReflectionClass = new \ReflectionClass($eventListener[0]);
            $eventListenerFilePath = $eventListenerReflectionClass->getFileName();

            if ($eventListenerFilePath === false) {
                continue;
            }

            $eventListenerFilePath = str_replace($projectDir, '', $eventListenerFilePath);
            $eventListenerFilePath = ltrim($eventListenerFilePath, '/');

            if (!str_starts_with($eventListenerFilePath, 'vendor/')) {
                $ownEventListenersCount++;
            }
        }

        self::assertEquals(1, $ownEventListenersCount);
    }

    public function testResponseEventListeners(): void
    {
        $this->assertEventListeners(
            kernelResponseEvent   : KernelResponseEvent::NO_REDIRECT,
            expectedEventListeners: self::NON_REDIRECT_RESPONSE_EVENT_LISTENERS,
        );
    }

    public function testRedirectResponseEventListeners(): void
    {
        $this->assertEventListeners(
            kernelResponseEvent   : KernelResponseEvent::REDIRECT,
            expectedEventListeners: self::REDIRECT_RESPONSE_EVENT_LISTENERS,
        );
    }

    public function testOnKernelResponseWithHeadRequest(): void
    {
        $request = new Request();
        $request->setMethod('HEAD');

        $response = new Response();

        $responseEvent = $this->createResponseEvent($request, $response);

        $this->eventDispatcherMock->expects(self::never())
            ->method('dispatch');

        $this->subscriber->onKernelResponse($responseEvent);

        self::assertFalse($response->headers->has('X-Log-Memory_Usage'));
    }

    public function testOnKernelResponseWithNonRedirectResponse(): void
    {
        $request = new Request();
        $request->setMethod('GET');

        $response = new Response('content', Response::HTTP_OK);

        $responseEvent = $this->createResponseEvent($request, $response);

        $this->eventDispatcherMock->expects(self::once())
            ->method('dispatch')
            ->with(
                self::identicalTo($responseEvent),
                self::identicalTo(KernelResponseEvent::NO_REDIRECT->value),
            );

        $this->subscriber->onKernelResponse($responseEvent);

        self::assertTrue($response->headers->has('X-Log-Memory_Usage'));
        self::assertIsNumeric($response->headers->get('X-Log-Memory_Usage'));
    }

    public function testOnKernelResponseWithRedirectResponse(): void
    {
        $request = new Request();
        $request->setMethod('GET');

        $response = new Response('', Response::HTTP_FOUND);
        $response->headers->set('Location', 'https://example.com');

        $responseEvent = $this->createResponseEvent($request, $response);

        $this->eventDispatcherMock->expects(self::once())
            ->method('dispatch')
            ->with(
                self::identicalTo($responseEvent),
                self::identicalTo(KernelResponseEvent::REDIRECT->value),
            );

        $this->subscriber->onKernelResponse($responseEvent);

        self::assertTrue($response->headers->has('X-Log-Memory_Usage'));
    }

    /**
     * @param array<class-string<EventSubscriberInterface>> $expectedEventListeners
     */
    private function assertEventListeners(
        KernelResponseEvent $kernelResponseEvent,
        array $expectedEventListeners
    ): void
    {
        /** @var EventDispatcher $dispatcher */
        $dispatcher = self::getContainer()->get('event_dispatcher');
        $eventListeners = $dispatcher->getListeners($kernelResponseEvent->value);
        $eventListenerClasses = [];

        foreach ($eventListeners as $eventListener) {
            if (!is_array($eventListener)) {
                continue;
            }

            $eventListenerClass = $eventListener[0]::class;

            if (!in_array($eventListenerClass, $expectedEventListeners, true)) {
                self::fail(
                    sprintf(
                        'Event listener "%s" is not expected for event "%s"',
                        $eventListenerClass,
                        $kernelResponseEvent->value,
                    ),
                );
            }

            $eventListenerClasses[$eventListenerClass] = true;
        }

        self::assertCount(count($eventListenerClasses), $expectedEventListeners);
    }

    private function createResponseEvent(Request $request, Response $response): ResponseEvent
    {
        $kernel = $this->createMock(HttpKernelInterface::class);

        return new ResponseEvent(
            $kernel,
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );
    }
}
