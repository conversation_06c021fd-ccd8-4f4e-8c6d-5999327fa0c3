<?php

declare(strict_types=1);

namespace Tests\Integration\Debug\EventSubscriber;

use App\Debug\EventSubscriber\DebugEventSubscriber;
use App\Debug\Helper\DebugHelper;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\JsonTemplate\View\ViewInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\Office\Request\OfficeRequestStub;

class DebugEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            DebugEventSubscriber::class,
            'onJsonTemplateHandled',
        );
    }

    public function testOnJsonTemplateHandledSetsViewWhenDebugInfoEnabled(): void
    {
        $debugRequest = new DebugRequestStub();
        $debugRequest->setDebugInfo(true);
        $officeRequest = new OfficeRequestStub();
        $debugHelper = new DebugHelper($debugRequest, $officeRequest);

        $view = $this->createMock(ViewInterface::class);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new DebugEventSubscriber($debugHelper, $debugRequest);
        $subscriber->onJsonTemplateHandled($event);

        self::assertSame($view, $debugHelper->getView());
    }

    public function testOnJsonTemplateHandledDoesNothingWhenDebugInfoDisabled(): void
    {
        $debugRequest = new DebugRequestStub();
        $debugRequest->setDebugInfo(false);
        $officeRequest = new OfficeRequestStub();
        $debugHelper = new DebugHelper($debugRequest, $officeRequest);

        $view = $this->createMock(ViewInterface::class);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new DebugEventSubscriber($debugHelper, $debugRequest);
        $subscriber->onJsonTemplateHandled($event);

        self::assertNull($debugHelper->getView());
    }
}
