<?php

declare(strict_types=1);

namespace Tests\Integration\Generic\Routing\Console;

use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Tester\CommandTester;
use Visymo\Filesystem\SerializedFile\SerializedFile;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;

final class GenerateRouteToRouteCheckerMappingConsoleTest extends AbstractSymfonyIntegrationTest
{
    public function testExecute(): void
    {
        self::bootKernel();

        if (self::$kernel === null) {
            self::fail('Kernel is not booted');
        }

        /** @var SerializedFile $mappingFile */
        $mappingFile = self::getContainer()->get('brand_website.generic.route.route_to_checker_mapping_file');
        $expectedConfig = $mappingFile->getContents();

        $application = new Application(self::$kernel);
        $command = $application->find('serp:config:generate-route-to-checker-mapping');

        $commandTester = new CommandTester($command);
        $commandTester->execute([]);
        $commandTester->assertCommandIsSuccessful();

        self::assertSame($expectedConfig, $mappingFile->getContents());
    }
}
