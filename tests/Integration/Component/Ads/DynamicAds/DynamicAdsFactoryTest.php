<?php

declare(strict_types=1);

namespace Tests\Integration\Component\Ads\DynamicAds;

use App\Ads\AdProvider;
use App\Component\Ads\BingAdsSidebarAdUnit\BingAdsSidebarAdUnitComponent;
use App\Component\Ads\BingAdsTopAdUnit\BingAdsTopAdUnitComponent;
use App\Component\Ads\DynamicAds\DynamicAdsFactory;
use App\Component\Ads\DynamicAds\DynamicAdsResolver;
use App\Component\Ads\DynamicAds\DynamicAdsUnit;
use App\Component\Ads\DynamicAds\Factory\AbstractDynamicAdsFactory;
use App\Component\Ads\DynamicAds\Factory\DynamicAdsFactoryInterface;
use App\Component\Ads\DynamicAds\Factory\DynamicBingAdsFactory;
use App\Component\Ads\DynamicAds\Factory\DynamicGoogleAdsFactory;
use App\Component\Ads\GoogleAdsBottomUnit\GoogleAdsBottomUnitComponent;
use App\Component\Ads\GoogleAdsTopUnit\GoogleAdsTopUnitComponent;
use App\Component\Generic\AbstractSearchApiCondition\SearchApiConditionComponentInterface;
use App\GoogleCsa\Container\GoogleCsaContainerAffixHelperInterface;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\GoogleCsa\Container\GoogleCsaContainerAffixHelperStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsHelperStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;

class DynamicAdsFactoryTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private DebugRequestStub $debugRequestStub;

    private WebsiteSettingsStub $websiteSettingsStub;

    private WebsiteSettingsHelperStub $websiteSettingsHelperStub;

    private ComponentFactory $componentFactory;

    /** @var array<string, DynamicAdsFactoryInterface> */
    private array $dynamicAdsFactories;

    private DynamicAdsFactory $dynamicAdsFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->debugRequestStub = $this->stubs()->request()->getDebugRequest();
        $this->debugRequestStub->setForcePrimaryAdsType(null);

        $this->websiteSettingsStub = $this->stubs()->websiteSettings();
        $this->websiteSettingsStub->getBingAds()->setEnabled(false);
        $this->websiteSettingsStub->getGoogleAdSense()->setEnabled(false);

        /** @var WebsiteSettingsHelperStub $websiteSettingsHelperStub */
        $websiteSettingsHelperStub = self::getContainer()->get(WebsiteSettingsHelperStub::class);
        $this->websiteSettingsHelperStub = $websiteSettingsHelperStub;
        $this->websiteSettingsHelperStub->setWebsiteSettings($this->websiteSettingsStub);

        /** @var ComponentFactory $componentFactory */
        $componentFactory = self::getContainer()->get(ComponentFactory::class);
        $this->componentFactory = $componentFactory;

        $googleCsaContainerAffixHelperStub = new GoogleCsaContainerAffixHelperStub();
        self::getContainer()->set(
            GoogleCsaContainerAffixHelperInterface::class,
            $googleCsaContainerAffixHelperStub,
        );

        $this->addAdsFactory(
            new DynamicBingAdsFactory(
                $this->debugRequestStub,
                $this->websiteSettingsHelperStub,
            ),
        );
        $this->addAdsFactory(
            new DynamicGoogleAdsFactory(
                $this->debugRequestStub,
                $this->websiteSettingsHelperStub,
            ),
        );

        $this->dynamicAdsFactory = new DynamicAdsFactory(new \ArrayObject($this->dynamicAdsFactories));
    }

    /**
     * @return mixed[]
     */
    public static function createDynamicAdComponentDataProvider(): array
    {
        return [
            'force bing, top unit'      => [
                'unit'                   => DynamicAdsUnit::TOP,
                'requestCallback'        => static function (DebugRequestStub $debugRequestStub): void {
                    $debugRequestStub->setForcePrimaryAdsType(AdProvider::TYPE_BING);
                },
                'settingsCallback'       => null,
                'expectedComponentTypes' => [
                    BingAdsTopAdUnitComponent::getType(),
                ],
            ],
            'force google, bottom unit' => [
                'unit'                   => DynamicAdsUnit::BOTTOM,
                'requestCallback'        => static function (DebugRequestStub $debugRequestStub): void {
                    $debugRequestStub->setForcePrimaryAdsType(AdProvider::TYPE_GOOGLE);
                },
                'settingsCallback'       => static function (WebsiteSettingsStub $websiteSettingsStub): void {
                    $websiteSettingsStub->getBingAds()
                        ->setDynamicAdsEnabled(false);
                },
                'expectedComponentTypes' => [
                    GoogleAdsBottomUnitComponent::getType(),
                ],
            ],
            'google, top unit'          => [
                'unit'                   => DynamicAdsUnit::TOP,
                'requestCallback'        => null,
                'settingsCallback'       => static function (WebsiteSettingsStub $websiteSettingsStub): void {
                    $websiteSettingsStub->getBingAds()
                        ->setDynamicAdsEnabled(false);
                },
                'expectedComponentTypes' => [
                    GoogleAdsTopUnitComponent::getType(),
                ],
            ],
            'bing, sidebar unit'        => [
                'unit'                   => DynamicAdsUnit::SIDEBAR,
                'requestCallback'        => null,
                'settingsCallback'       => null,
                'expectedComponentTypes' => [
                    BingAdsSidebarAdUnitComponent::getType(),
                ],
            ],
        ];
    }

    /**
     * @param string[] $expectedComponentTypes
     */
    #[DataProvider('createDynamicAdComponentDataProvider')]
    public function testCreateDynamicAdComponent(
        DynamicAdsUnit $unit,
        ?callable $requestCallback,
        ?callable $settingsCallback,
        array $expectedComponentTypes
    ): void
    {
        // Enable all dynamic ad providers
        $this->websiteSettingsStub->getBingAds()->setDynamicAdsEnabled(true);
        $this->websiteSettingsStub->getGoogleAdSense()->setDynamicAdsEnabled(true);

        if ($requestCallback !== null) {
            $requestCallback($this->debugRequestStub);
        }

        if ($settingsCallback !== null) {
            $settingsCallback($this->websiteSettingsStub);
        }

        // Options, except unit are irrelevant for the test
        $options = [
            DynamicAdsResolver::KEY_UNIT             => $unit->value,
            DynamicAdsResolver::KEY_CONTAINER_SUFFIX => null,
            DynamicAdsResolver::KEY_AMOUNT           => 3,
            DynamicAdsResolver::KEY_REPEATED         => 2,
        ];

        $dynamicAdComponent = $this->dynamicAdsFactory->create($options, $this->componentFactory);
        $actualComponentTypes = $this->getSegmentComponentTypes($dynamicAdComponent->components);

        self::assertSame($expectedComponentTypes, $actualComponentTypes);
    }

    private function addAdsFactory(AbstractDynamicAdsFactory $adsFactory): void
    {
        $this->dynamicAdsFactories[$adsFactory->getType()] = $adsFactory;
    }

    /**
     * @param ComponentInterface[] $components
     *
     * @return string[]
     */
    private function getSegmentComponentTypes(array $components): array
    {
        $componentTypes = [];

        foreach ($components as $component) {
            $componentTypes[] = $component::getType();

            if (!$component instanceof SearchApiConditionComponentInterface) {
                continue;
            }

            foreach ($component->getAllSegments() as $subSegment) {
                foreach ($this->getSegmentComponentTypes($subSegment->getComponents()) as $componentType) {
                    $componentTypes[] = $componentType;
                }
            }
        }

        return $componentTypes;
    }
}
