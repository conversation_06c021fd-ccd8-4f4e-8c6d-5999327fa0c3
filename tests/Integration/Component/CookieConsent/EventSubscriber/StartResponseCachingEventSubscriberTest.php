<?php

declare(strict_types=1);

namespace Tests\Integration\Component\CookieConsent\EventSubscriber;

use App\Component\Generic\CookieConsent\CookieConsentRenderer;
use App\Component\Generic\CookieConsent\EventSubscriber\StartResponseCachingEventSubscriber;
use App\Http\Response\Event\ResponseCachingStartedEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class StartResponseCachingEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            ResponseCachingStartedEvent::NAME,
            StartResponseCachingEventSubscriber::class,
            'forceCookieConsentRendering',
        );
    }

    public function testForceCookieConsentRenderingCallsRendererForceRender(): void
    {
        $cookieConsentRenderer = $this->createMock(CookieConsentRenderer::class);

        $cookieConsentRenderer->expects($this->once())
            ->method('forceRender');

        $subscriber = new StartResponseCachingEventSubscriber($cookieConsentRenderer);
        $subscriber->forceCookieConsentRendering();
    }

    public function testForceCookieConsentRenderingWithRealRenderer(): void
    {
        /** @var CookieConsentRenderer $cookieConsentRenderer */
        $cookieConsentRenderer = self::getContainer()->get(CookieConsentRenderer::class);

        $subscriber = new StartResponseCachingEventSubscriber($cookieConsentRenderer);
        $subscriber->forceCookieConsentRendering();

        $this->expectNotToPerformAssertions();
    }
}
