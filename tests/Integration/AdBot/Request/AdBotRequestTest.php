<?php

declare(strict_types=1);

namespace Tests\Integration\AdBot\Request;

use App\AdBot\Bot\AdBot;
use App\AdBot\Request\AdBotRequestInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\AbstractRequestIntegrationTestCase;
use Tests\Stub\Debug\Request\DebugRequestStub;

class AdBotRequestTest extends AbstractRequestIntegrationTestCase
{
    private DebugRequestStub $debugRequestStub;

    private AdBotRequestInterface $adBotRequest;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->debugRequestStub = $this->stubs()->request()->getDebugRequest()->setIsAdBot(false);

        /** @var AdBotRequestInterface $adBotRequest */
        $adBotRequest = self::getContainer()->get(AdBotRequestInterface::class);
        $this->adBotRequest = $adBotRequest;
    }

    /**
     * @return mixed[]
     */
    public static function requestDataProvider(): array
    {
        $defaultExpectedValues = [
            AdBotRequestInterface::KEY_IS_AD_BOT => false,
            AdBotRequestInterface::KEY_AD_BOT    => null,
        ];

        $data = [
            'empty'                      => [
                'requestCallback'      => null,
                'debugRequestCallback' => null,
                'expectedValues'       => $defaultExpectedValues,
            ],
            'is no ad bot by header'     => [
                'requestCallback'      => static function (Request $request): void {
                    $request->headers->set(AdBotRequestInterface::HEADER_X_LOADBALANCER_IS_AD_BOT, '0');
                },
                'debugRequestCallback' => null,
                'expectedValues'       => $defaultExpectedValues,
            ],
            'is ad bot by debug request' => [
                'requestCallback'      => null,
                'debugRequestCallback' => static function (DebugRequestStub $debugRequestStub): void {
                    $debugRequestStub->setIsAdBot();
                },
                'expectedValues'       => [
                    ...$defaultExpectedValues,
                    AdBotRequestInterface::KEY_IS_AD_BOT => true,
                ],
            ],
        ];

        foreach (self::getAdBotUserAgents() as $adBot => $userAgents) {
            foreach ($userAgents as $userAgent) {
                $testName = sprintf('is %s ad bot by user agent %s', $adBot, $userAgent);

                $data[$testName] = [
                    'requestCallback'      => static function (Request $request) use ($userAgent): void {
                        $request->headers->set(AdBotRequestInterface::HEADER_X_LOADBALANCER_IS_AD_BOT, '1');
                        $request->headers->set('User-Agent', $userAgent);
                    },
                    'debugRequestCallback' => null,
                    'expectedValues'       => [
                        ...$defaultExpectedValues,
                        AdBotRequestInterface::KEY_IS_AD_BOT => true,
                        AdBotRequestInterface::KEY_AD_BOT    => $adBot,
                    ],
                ];
            }
        }

        return $data;
    }

    /**
     * @param array<string, mixed> $expectedValues
     */
    #[DataProvider('requestDataProvider')]
    public function testAdBotRequest(
        ?callable $requestCallback,
        ?callable $debugRequestCallback,
        array $expectedValues
    ): void
    {
        if ($debugRequestCallback !== null) {
            $debugRequestCallback($this->debugRequestStub);
        }

        $this->assertRequest('/', $requestCallback, $this->adBotRequest, $expectedValues);
    }

    /**
     * @return array<string, array<string, string>>
     */
    private static function getAdBotUserAgents(): array
    {
        return [
            AdBot::GOOGLE->value    => [
                'Mobile android' => 'Mozilla/5.0 (Linux; Android 5.0; SM-G920A) AppleWebKit (KHTML, like Gecko) Chrome Mobile '.
                                    'Safari (compatible; AdsBot-Google-Mobile; +http://www.google.com/mobile/adsbot.html)',
                'Mobile web'     => 'Mozilla/5.0 (iPhone; CPU iPhone OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like '.
                                    'Gecko) Version/9.0 Mobile/13B143 Safari/601.1 (compatible; AdsBot-Google-Mobile; '.
                                    '+http://www.google.com/mobile/adsbot.html)',
            ],
            AdBot::MICROSOFT->value => [
                'Web A'  => 'Mozilla/5.0 (compatible; adidxbot/2.0; +http://www.bing.com/bingbot.htm)',
                'Web B'  => 'Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; '.
                            'Lumia 530) like Gecko (compatible; adidxbot/2.0; +http://www.bing.com/bingbot.htm)',
                'Mobile' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like '.
                            'Gecko) Version/7.0 Mobile/11A465 Safari/9537.53 (compatible; adidxbot/2.0; '.
                            '+http://www.bing.com/bingbot.htm)',
            ],
        ];
    }
}
