<?php

declare(strict_types=1);

namespace Tests\Integration\GooglePublisherTag\EventSubscriber;

use App\ConversionTracking\Endpoint\GoogleAdManager\GoogleAdManagerConversionUrlGenerator;
use App\GooglePublisherTag\EventSubscriber\InjectGooglePublisherTagScriptsEventSubscriber;
use App\GooglePublisherTag\PubAdsService\PubAdsService;
use App\GooglePublisherTag\Tag\GoogleTagRenderHelper;
use App\Http\Response\HeaderLink\HeaderLinkRegistry;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;

class InjectGooglePublisherTagScriptsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            InjectGooglePublisherTagScriptsEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testRenderTemplateHeadersAddsItemWhenEnabledAndHasSlots(): void
    {
        $twig = $this->createMock(Environment::class);
        $googleTagRenderHelper = $this->createMock(GoogleTagRenderHelper::class);
        $conversionUrlGenerator = $this->createMock(GoogleAdManagerConversionUrlGenerator::class);
        $headerLinkRegistry = new HeaderLinkRegistry();

        $pubAdsService = $this->createMock(PubAdsService::class);
        $googleTagRenderHelper->method('isEnabled')->willReturn(true);
        $googleTagRenderHelper->method('hasSlots')->willReturn(true);
        $googleTagRenderHelper->method('getPubAdsService')->willReturn($pubAdsService);
        $googleTagRenderHelper->method('getSlotsCount')->willReturn(3);
        $conversionUrlGenerator->method('generateDisplay')->willReturn('conversion-url');
        $twig->method('render')
            ->with(
                '@theme/google_gpt/google_gpt_script.html.twig',
                [
                    'pub_ads_service'        => $pubAdsService,
                    'display_conversion_url' => 'conversion-url',
                    'requested_slots'        => 3,
                ],
            )
            ->willReturn('gpt-content');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGooglePublisherTagScriptsEventSubscriber(
            $twig,
            $googleTagRenderHelper,
            $conversionUrlGenerator,
            $headerLinkRegistry,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('gpt-content', $event->getItems()[0]);
    }

    public function testRenderTemplateHeadersDoesNothingWhenNotEnabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $googleTagRenderHelper = $this->createMock(GoogleTagRenderHelper::class);
        $conversionUrlGenerator = $this->createMock(GoogleAdManagerConversionUrlGenerator::class);
        $headerLinkRegistry = new HeaderLinkRegistry();

        $googleTagRenderHelper->method('isEnabled')->willReturn(false);
        $twig->expects($this->never())->method('render');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGooglePublisherTagScriptsEventSubscriber(
            $twig,
            $googleTagRenderHelper,
            $conversionUrlGenerator,
            $headerLinkRegistry,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }

    public function testRenderTemplateHeadersDoesNothingWhenNoSlots(): void
    {
        $twig = $this->createMock(Environment::class);
        $googleTagRenderHelper = $this->createMock(GoogleTagRenderHelper::class);
        $conversionUrlGenerator = $this->createMock(GoogleAdManagerConversionUrlGenerator::class);
        $headerLinkRegistry = new HeaderLinkRegistry();

        $googleTagRenderHelper->method('isEnabled')->willReturn(true);
        $googleTagRenderHelper->method('hasSlots')->willReturn(false);
        $twig->expects($this->never())->method('render');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGooglePublisherTagScriptsEventSubscriber(
            $twig,
            $googleTagRenderHelper,
            $conversionUrlGenerator,
            $headerLinkRegistry,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }
}
