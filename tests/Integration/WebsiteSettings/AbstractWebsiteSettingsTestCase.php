<?php

declare(strict_types=1);

namespace Tests\Integration\WebsiteSettings;

use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Integration\Helper\BrandConfigTestHelper;
use Visymo\PhpunitExtensions\PhpUnit\PhpUnitEnv;

abstract class AbstractWebsiteSettingsTestCase extends AbstractBrandWebsitesIntegrationTestCase
{
    protected BrandConfigTestHelper $brandConfigTestHelper;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->stubs()->request()->getConsentManagementPlatformRequest()->setConsentManagementPlatform(null);

        /** @var BrandConfigTestHelper $brandConfigTestHelper */
        $brandConfigTestHelper = self::getContainer()->get(BrandConfigTestHelper::class);
        $this->brandConfigTestHelper = $brandConfigTestHelper;
    }

    /**
     * @param mixed[] $outputData
     */
    protected function validateActualOutputData(array $outputData, string $expectedResponseFilePath): void
    {
        $actualResponse = json_encode(
            $outputData,
            JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES,
        );

        if (PhpUnitEnv::isUpdateAssertions()) {
            file_put_contents($expectedResponseFilePath, $actualResponse);

            self::markTestIncomplete('Updated expected responses');
        } else {
            self::assertStringEqualsFile($expectedResponseFilePath, $actualResponse);
        }
    }
}
