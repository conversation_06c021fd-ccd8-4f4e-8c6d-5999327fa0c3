<?php

declare(strict_types=1);

namespace Tests\Integration\Locale\Url;

use App\Http\Url\PersistentUrlParametersPageType;
use App\Locale\Request\LocaleRequestInterface;
use App\Locale\Url\LocaleUrlParametersProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class LocaleRequestUrlParametersProviderTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private LocaleUrlParametersProvider $localeRequestUrlParametersProvider;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var LocaleUrlParametersProvider $localeRequestUrlParametersProvider */
        $localeRequestUrlParametersProvider = self::getContainer()->get(LocaleUrlParametersProvider::class);
        $this->localeRequestUrlParametersProvider = $localeRequestUrlParametersProvider;
    }

    /**
     * @return mixed[]
     */
    public static function getPersistentUrlParametersDataProvider(): array
    {
        return [
            'empty'                                    => [
                'url'                   => '/',
                'requestLocaleProperty' => 'nl_NL',
                'expectedUrlParameters' => [],
            ],
            'valid (equal to request attribute)'       => [
                'url'                   => '/?locale=nl_NL',
                'requestLocaleProperty' => 'nl_NL',
                'expectedUrlParameters' => [
                    LocaleRequestInterface::PARAMETER_LOCALE => 'nl_NL',
                ],
            ],
            'invalid (not equal to request attribute)' => [
                'url'                   => '/?locale=nl_NL',
                'requestLocaleProperty' => 'en_US',
                'expectedUrlParameters' => [],
            ],
        ];
    }

    /**
     * @param mixed[] $expectedUrlParameters
     */
    #[DataProvider('getPersistentUrlParametersDataProvider')]
    public function testGetPersistentUrlParameters(
        string $url,
        string $requestLocaleProperty,
        array $expectedUrlParameters
    ): void
    {
        $request = Request::create($url);
        $request->setLocale($requestLocaleProperty);

        $this->setRequest($request);

        // Assert the output stays the same each time and the request is accessed once for information
        self::assertSame(
            $expectedUrlParameters,
            $this->localeRequestUrlParametersProvider->getPersistentUrlParameters(
                PersistentUrlParametersPageType::DEFAULT,
            ),
        );
        self::assertSame(
            $expectedUrlParameters,
            $this->localeRequestUrlParametersProvider->getPersistentUrlParameters(
                PersistentUrlParametersPageType::DEFAULT,
            ),
        );
    }
}
