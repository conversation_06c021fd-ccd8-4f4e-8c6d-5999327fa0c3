<?php

declare(strict_types=1);

namespace Tests\Integration\Helper;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;
use App\JsonTemplate\View\SegmentInterface;

final class ComponentTestHelper
{
    /**
     * @return mixed[]
     */
    public function componentToArray(ComponentInterface $component): array
    {
        $data = [
            'id'                         => $component->getId(),
            ComponentInterface::KEY_TYPE => $component::getType(),
            ...get_object_vars($component),
        ];

        if (!$component instanceof ParentComponentInterface) {
            return $data;
        }

        $reflectionObject = new \ReflectionObject($component);

        //phpcs:disable SlevomatCodingStandard.ControlStructures.AssignmentInCondition.AssignmentInCondition
        do {
            foreach ($reflectionObject->getProperties() as $reflectionProperty) {
                $reflectionProperty->setAccessible(true);

                $property = $reflectionProperty->getName();
                $propertyValue = $reflectionProperty->getValue($component);

                if ($propertyValue instanceof ComponentInterface) {
                    $data[$property] = $this->componentToArray($propertyValue);

                    continue;
                }

                if ($propertyValue instanceof SegmentInterface) {
                    $data[$property] = array_map(
                        fn (ComponentInterface $component) => $this->componentToArray($component),
                        $propertyValue->getComponents(),
                    );

                    continue;
                }

                if (is_array($propertyValue)) {
                    $firstValue = reset($propertyValue);

                    if ($firstValue instanceof ComponentInterface) {
                        $data[$property] = array_map(
                            fn (ComponentInterface $component) => $this->componentToArray($component),
                            $propertyValue,
                        );

                        continue;
                    }
                }

                $data[$property] = $propertyValue;
            }

        } while ($reflectionObject = $reflectionObject->getParentClass());

        return $data;
    }
}
