<?php

declare(strict_types=1);

namespace Tests\Integration\ConversionTracking\EventSubscriber;

use App\ConversionTracking\EventSubscriber\InjectTrackingPixelEventSubscriber;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Twig\Environment;

final class InjectTrackingPixelEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private ActiveTrackingEntryHelperInterface & MockObject $activeTrackingEntryHelper;

    private Environment & MockObject $twig;

    protected function setUp(): void
    {
        parent::setUp();

        $this->activeTrackingEntryHelper = $this->createMock(ActiveTrackingEntryHelperInterface::class);
        $this->twig = $this->createMock(Environment::class);
    }

    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateFootersEvent::NAME,
            InjectTrackingPixelEventSubscriber::class,
            'injectTrackingPixel',
        );
    }

    /**
     * @return mixed[]
     */
    public static function injectTrackingPixelDataProvider(): array
    {
        return [
            'tracking entry not empty' => [
                'trackingEntryEmpty' => false,
                'expectRender' => true,
                'expectedItemCount' => 1,
            ],
            'tracking entry is empty' => [
                'trackingEntryEmpty' => true,
                'expectRender' => false,
                'expectedItemCount' => 0,
            ],
        ];
    }

    #[DataProvider('injectTrackingPixelDataProvider')]
    public function testInjectTrackingPixel(
        bool $trackingEntryEmpty,
        bool $expectRender,
        int $expectedItemCount
    ): void {
        $builder = (new TrackingEntryStubBuilder())->clear()->setIsEmpty($trackingEntryEmpty);

        if (!$trackingEntryEmpty) {
            $builder->setQuery('test-query');
        }

        $trackingEntry = $builder->create();
        $this->activeTrackingEntryHelper->method('getActiveTrackingEntry')->willReturn($trackingEntry);

        if ($expectRender) {
            $this->twig->expects($this->once())
                ->method('render')
                ->with('@theme/tracking/tracking_pixel_scripts.html.twig')
                ->willReturn('tracking-pixel-content');
        } else {
            $this->twig->expects($this->never())->method('render');
        }

        $event = new RenderTemplateFootersEvent();
        $subscriber = new InjectTrackingPixelEventSubscriber($this->activeTrackingEntryHelper, $this->twig);
        $subscriber->injectTrackingPixel($event);

        self::assertCount($expectedItemCount, $event->getItems());

        if ($expectRender) {
            self::assertSame('tracking-pixel-content', $event->getItems()[0]);
        }
    }
}
