<?php

declare(strict_types=1);

namespace Tests\Integration\Translations\Model;

readonly class ParsedTranslationUnit
{
    public function __construct(
        private string $id,
        private string $target
    )
    {
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getTarget(): string
    {
        return $this->target;
    }

    public function getAmountOfPercentageSigns(): int
    {
        return substr_count($this->getTarget(), '%');
    }

    /**
     * @return string[]
     */
    public function getVariables(): array
    {
        $result = preg_match_all('/%[^%]+%/m', $this->getTarget(), $matches);

        if ($result === false || $result === 0) {
            return [];
        }

        $foundVariables = $matches[0];

        sort($foundVariables);

        return $foundVariables;
    }
}
