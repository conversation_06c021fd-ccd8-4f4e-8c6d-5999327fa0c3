<?php

declare(strict_types=1);

namespace Tests\Integration\GoogleCsa\EventSubscriber;

use App\ConversionTracking\Endpoint\GoogleAdSenseOnline\GoogleAdSenseOnlineConversionUrlGenerator;
use App\Generic\Url\UrlHelper;
use App\GoogleCsa\EventSubscriber\InjectGoogleCsaScriptsEventSubscriber;
use App\GoogleCsa\Helper\GoogleCsaClickTrackUrlHelper;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\GoogleCsa\StyleId\GoogleCsaStyleIdParameterInterface;
use App\Http\Response\HeaderLink\HeaderLinkRegistry;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\GoogleCsa\GoogleCsaStubBuilder;
use Tests\Stub\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettingsStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Twig\Environment;
use Visymo\GoogleCsa\Ads\Unit\AdUnit;
use Visymo\GoogleCsa\GoogleCsaRenderer;

class InjectGoogleCsaScriptsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            InjectGoogleCsaScriptsEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testRenderTemplateHeadersAddsItemsWhenGoogleCsaHasUnits(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $googleCsaClickTrackUrlHelper = new GoogleCsaClickTrackUrlHelper(new UrlHelper());
        $googleCsaStyleIdParameter = $this->createMock(GoogleCsaStyleIdParameterInterface::class);
        $googleCsaRenderer = new GoogleCsaRenderer();
        $twig = $this->createMock(Environment::class);
        $googleAdSenseOnlineConversionUrlGenerator = $this->createMock(
            GoogleAdSenseOnlineConversionUrlGenerator::class,
        );
        $headerLinkRegistry = new HeaderLinkRegistry();
        $websiteSettingsHelper = $this->createMock(WebsiteSettingsHelper::class);

        $googleCsa = (new GoogleCsaStubBuilder())
            ->withPublisherId('test-publisher')
            ->withQuery('test query')
            ->create();
        $googleCsa->ads()->addUnit(new AdUnit('test-container', null, 3, true));

        $googleAdSenseSettings = new GoogleAdSenseSettingsStub();
        $googleAdSenseSettings->setContractType(ContractType::ONLINE);
        $websiteSettings = new WebsiteSettingsStub();
        $websiteSettings->setGoogleAdSense($googleAdSenseSettings);

        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $websiteSettingsHelper->method('getSettings')->willReturn($websiteSettings);
        $googleCsaStyleIdParameter->method('getStyleId')->willReturn(123);
        $googleAdSenseOnlineConversionUrlGenerator->method('generate')->willReturn('conversion-url');

        $twig->method('render')
            ->willReturnCallback(
                static fn ($template, $params) => match ($template) {
                    '@theme/google_csa/google_csa_scripts.html.twig'           => 'csa-scripts',
                    '@theme/google_afs/google_afs_online_javascript.html.twig' => 'afs-online',
                    '@theme/google_csa/google_csa_javascript.html.twig'        => 'csa-javascript',
                    default                                                    => ''
                },
            );

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleCsaScriptsEventSubscriber(
            $googleCsaRegistry,
            $googleCsaClickTrackUrlHelper,
            $googleCsaStyleIdParameter,
            $googleCsaRenderer,
            $twig,
            $googleAdSenseOnlineConversionUrlGenerator,
            $headerLinkRegistry,
            $websiteSettingsHelper,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertCount(3, $event->getItems());
        self::assertSame('csa-scripts', $event->getItems()[0]);
        self::assertSame('afs-online', $event->getItems()[1]);
        self::assertSame('csa-javascript', $event->getItems()[2]);
    }

    public function testRenderTemplateHeadersDoesNothingWhenGoogleCsaIsNull(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $googleCsaClickTrackUrlHelper = new GoogleCsaClickTrackUrlHelper(new UrlHelper());
        $googleCsaStyleIdParameter = $this->createMock(GoogleCsaStyleIdParameterInterface::class);
        $googleCsaRenderer = new GoogleCsaRenderer();
        $twig = $this->createMock(Environment::class);
        $googleAdSenseOnlineConversionUrlGenerator = $this->createMock(
            GoogleAdSenseOnlineConversionUrlGenerator::class,
        );
        $headerLinkRegistry = new HeaderLinkRegistry();
        $websiteSettingsHelper = $this->createMock(WebsiteSettingsHelper::class);

        $twig->expects($this->never())->method('render');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleCsaScriptsEventSubscriber(
            $googleCsaRegistry,
            $googleCsaClickTrackUrlHelper,
            $googleCsaStyleIdParameter,
            $googleCsaRenderer,
            $twig,
            $googleAdSenseOnlineConversionUrlGenerator,
            $headerLinkRegistry,
            $websiteSettingsHelper,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }

    public function testRenderTemplateHeadersDoesNothingWhenGoogleCsaHasNoUnits(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $googleCsaClickTrackUrlHelper = new GoogleCsaClickTrackUrlHelper(new UrlHelper());
        $googleCsaStyleIdParameter = $this->createMock(GoogleCsaStyleIdParameterInterface::class);
        $googleCsaRenderer = new GoogleCsaRenderer();
        $twig = $this->createMock(Environment::class);
        $googleAdSenseOnlineConversionUrlGenerator = $this->createMock(
            GoogleAdSenseOnlineConversionUrlGenerator::class,
        );
        $headerLinkRegistry = new HeaderLinkRegistry();
        $websiteSettingsHelper = $this->createMock(WebsiteSettingsHelper::class);

        $googleCsa = (new GoogleCsaStubBuilder())
            ->withPublisherId('test-publisher')
            ->create(); // No units added

        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $twig->expects($this->never())->method('render');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleCsaScriptsEventSubscriber(
            $googleCsaRegistry,
            $googleCsaClickTrackUrlHelper,
            $googleCsaStyleIdParameter,
            $googleCsaRenderer,
            $twig,
            $googleAdSenseOnlineConversionUrlGenerator,
            $headerLinkRegistry,
            $websiteSettingsHelper,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }

    public function testRenderTemplateHeadersOnlyRendersOnce(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $googleCsaClickTrackUrlHelper = new GoogleCsaClickTrackUrlHelper(new UrlHelper());
        $googleCsaStyleIdParameter = $this->createMock(GoogleCsaStyleIdParameterInterface::class);
        $googleCsaRenderer = new GoogleCsaRenderer();
        $twig = $this->createMock(Environment::class);
        $googleAdSenseOnlineConversionUrlGenerator = $this->createMock(
            GoogleAdSenseOnlineConversionUrlGenerator::class,
        );
        $headerLinkRegistry = new HeaderLinkRegistry();
        $websiteSettingsHelper = $this->createMock(WebsiteSettingsHelper::class);

        $googleCsa = (new GoogleCsaStubBuilder())
            ->withPublisherId('test-publisher')
            ->withQuery('test query')
            ->create();
        $googleCsa->ads()->addUnit(new AdUnit('test-container', null, 3, true));

        $googleAdSenseSettings = new GoogleAdSenseSettingsStub();
        $googleAdSenseSettings->setContractType(ContractType::DIRECT);
        $websiteSettings = new WebsiteSettingsStub();
        $websiteSettings->setGoogleAdSense($googleAdSenseSettings);

        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $websiteSettingsHelper->method('getSettings')->willReturn($websiteSettings);

        $twig->expects($this->exactly(2))
            ->method('render')
            ->willReturnCallback(
                static fn ($template, $params) => match ($template) {
                    '@theme/google_csa/google_csa_scripts.html.twig'    => 'csa-scripts',
                    '@theme/google_csa/google_csa_javascript.html.twig' => 'csa-javascript',
                    default                                             => ''
                },
            );

        $subscriber = new InjectGoogleCsaScriptsEventSubscriber(
            $googleCsaRegistry,
            $googleCsaClickTrackUrlHelper,
            $googleCsaStyleIdParameter,
            $googleCsaRenderer,
            $twig,
            $googleAdSenseOnlineConversionUrlGenerator,
            $headerLinkRegistry,
            $websiteSettingsHelper,
        );

        // First call should render
        $event1 = new RenderTemplateHeadersEvent();
        $subscriber->renderTemplateHeaders($event1);

        // Second call should not render
        $event2 = new RenderTemplateHeadersEvent();
        $subscriber->renderTemplateHeaders($event2);

        self::assertCount(2, $event1->getItems());
        self::assertEmpty($event2->getItems());
    }

    public function testRenderTemplateHeadersWithoutGoogleAfsOnline(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $googleCsaClickTrackUrlHelper = new GoogleCsaClickTrackUrlHelper(new UrlHelper());
        $googleCsaStyleIdParameter = $this->createMock(GoogleCsaStyleIdParameterInterface::class);
        $googleCsaRenderer = new GoogleCsaRenderer();
        $twig = $this->createMock(Environment::class);
        $googleAdSenseOnlineConversionUrlGenerator = $this->createMock(
            GoogleAdSenseOnlineConversionUrlGenerator::class,
        );
        $headerLinkRegistry = new HeaderLinkRegistry();
        $websiteSettingsHelper = $this->createMock(WebsiteSettingsHelper::class);

        $googleCsa = (new GoogleCsaStubBuilder())
            ->withPublisherId('test-publisher')
            ->withQuery('test query')
            ->create();
        $googleCsa->ads()->addUnit(new AdUnit('test-container', null, 3, true));

        $googleAdSenseSettings = new GoogleAdSenseSettingsStub();
        $googleAdSenseSettings->setContractType(ContractType::DIRECT);
        $websiteSettings = new WebsiteSettingsStub();
        $websiteSettings->setGoogleAdSense($googleAdSenseSettings);

        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $websiteSettingsHelper->method('getSettings')->willReturn($websiteSettings);

        $twig->method('render')
            ->willReturnCallback(
                static fn ($template, $params) => match ($template) {
                    '@theme/google_csa/google_csa_scripts.html.twig'    => 'csa-scripts',
                    '@theme/google_csa/google_csa_javascript.html.twig' => 'csa-javascript',
                    default                                             => ''
                },
            );

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleCsaScriptsEventSubscriber(
            $googleCsaRegistry,
            $googleCsaClickTrackUrlHelper,
            $googleCsaStyleIdParameter,
            $googleCsaRenderer,
            $twig,
            $googleAdSenseOnlineConversionUrlGenerator,
            $headerLinkRegistry,
            $websiteSettingsHelper,
        );
        $subscriber->renderTemplateHeaders($event);

        // Should only have 2 items (no AFS online)
        self::assertCount(2, $event->getItems());
        self::assertSame('csa-scripts', $event->getItems()[0]);
        self::assertSame('csa-javascript', $event->getItems()[1]);
    }
}
