<?php

declare(strict_types=1);

namespace Tests\Integration\GoogleCsa\EventSubscriber;

use App\Brand\Settings\BrandSettingsHelper;
use App\GoogleCsa\EventSubscriber\InjectGoogleTagScriptEventSubscriber;
use App\Http\Request\GenericRequestInterface;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\Brand\Settings\BrandSettingsStub;
use Tests\Stub\WebsiteSettings\Settings\GoogleAdsConversionTracking\GoogleAdsConversionTrackingSettingsStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Twig\Environment;

class InjectGoogleTagScriptEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private WebsiteSettingsHelper & MockObject $websiteSettingsHelper;

    private Environment & MockObject $twig;

    private GenericRequestInterface & MockObject $genericRequest;

    private BrandSettingsHelper & MockObject $brandSettingsHelper;

    protected function setUp(): void
    {
        parent::setUp();

        $this->websiteSettingsHelper = $this->createMock(WebsiteSettingsHelper::class);
        $this->twig = $this->createMock(Environment::class);
        $this->genericRequest = $this->createMock(GenericRequestInterface::class);
        $this->brandSettingsHelper = $this->createMock(BrandSettingsHelper::class);
    }

    /**
     * @return mixed[]
     */
    public static function injectGoogleTagDataProvider(): array
    {
        return [
            'proadvisr with valid settings'                 => [
                'brandSlug'                 => 'proadvisr',
                'conversionTrackingEnabled' => true,
                'conversionTrackingLabel'   => 'test-label',
                'expectRender'              => true,
                'expectedItemCount'         => 2,
            ],
            'non-proadvisr brand'                           => [
                'brandSlug'                 => 'other-brand',
                'conversionTrackingEnabled' => true,
                'conversionTrackingLabel'   => 'test-label',
                'expectRender'              => false,
                'expectedItemCount'         => 0,
            ],
            'proadvisr with disabled conversion tracking'   => [
                'brandSlug'                 => 'proadvisr',
                'conversionTrackingEnabled' => false,
                'conversionTrackingLabel'   => 'test-label',
                'expectRender'              => false,
                'expectedItemCount'         => 0,
            ],
            'proadvisr with null conversion tracking label' => [
                'brandSlug'                 => 'proadvisr',
                'conversionTrackingEnabled' => true,
                'conversionTrackingLabel'   => null,
                'expectRender'              => false,
                'expectedItemCount'         => 0,
            ],
        ];
    }

    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            InjectGoogleTagScriptEventSubscriber::class,
            'injectGoogleTag',
        );
    }

    #[DataProvider('injectGoogleTagDataProvider')]
    public function testInjectGoogleTag(
        string $brandSlug,
        bool $conversionTrackingEnabled,
        ?string $conversionTrackingLabel,
        bool $expectRender,
        int $expectedItemCount
    ): void
    {
        $conversionTrackingSettings = new GoogleAdsConversionTrackingSettingsStub();
        $conversionTrackingSettings->setEnabled($conversionTrackingEnabled);
        $conversionTrackingSettings->setConversionTrackingId(123456);
        $conversionTrackingSettings->setConversionTrackingLabel($conversionTrackingLabel);

        $websiteSettings = new WebsiteSettingsStub();
        $websiteSettings->setGoogleAdsConversionTracking($conversionTrackingSettings);

        $brandSettings = new BrandSettingsStub();
        $brandSettings->setSlug($brandSlug);

        $this->websiteSettingsHelper->method('getSettings')->willReturn($websiteSettings);
        $this->brandSettingsHelper->method('getSettings')->willReturn($brandSettings);
        $this->genericRequest->method('getPageviewId')->willReturn('pageview-123');

        if ($expectRender) {
            $this->twig->expects($this->exactly(2))
                ->method('render')
                ->willReturnCallback(
                    static fn ($template, $params) => match ($template) {
                        '@theme/google_tag/google_tag_script.html.twig'     => 'google-tag-script',
                        '@theme/google_tag/google_tag_conversion.html.twig' => 'google-tag-conversion',
                        default                                             => '',
                    },
                );
        } else {
            $this->twig->expects($this->never())->method('render');
        }

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleTagScriptEventSubscriber(
            $this->websiteSettingsHelper,
            $this->twig,
            $this->genericRequest,
            $this->brandSettingsHelper,
        );
        $subscriber->injectGoogleTag($event);

        self::assertCount($expectedItemCount, $event->getItems());

        if ($expectRender) {
            self::assertSame('google-tag-script', $event->getItems()[0]);
            self::assertSame('google-tag-conversion', $event->getItems()[1]);
        }
    }
}
