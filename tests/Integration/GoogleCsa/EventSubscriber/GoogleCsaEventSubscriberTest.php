<?php

declare(strict_types=1);

namespace Tests\Integration\GoogleCsa\EventSubscriber;

use App\Debug\Request\DebugRequestInterface;
use App\GoogleCsa\EventSubscriber\GoogleCsaEventSubscriber;
use App\GoogleCsa\Factory\GoogleCsaFactory;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\GoogleCsa\Request\GoogleCsaRequestFactory;
use App\Http\Request\Info\RequestInfoInterface;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class GoogleCsaEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            GoogleCsaEventSubscriber::class,
            'onJsonTemplateHandled',
        );
    }

    public function testOnJsonTemplateHandledBasicFlow(): void
    {
        $debugRequest = $this->createMock(DebugRequestInterface::class);
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $trademarkInfringementResultBlocker = $this->createMock(TrademarkInfringementResultBlocker::class);
        $googleCsaRegistry = new GoogleCsaRegistry();

        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn(new ViewDataRequest());

        $debugRequest->method('disableGoogleAds')->willReturn(false);
        $requestInfo->method('getUserAgent')->willReturn('Mozilla/5.0');
        $trademarkInfringementResultBlocker->method('blockResults')->willReturn(false);

        /** @var GoogleCsaRequestFactory $googleCsaRequestFactory */
        $googleCsaRequestFactory = self::getContainer()->get(GoogleCsaRequestFactory::class);
        /** @var GoogleCsaFactory $googleCsaFactory */
        $googleCsaFactory = self::getContainer()->get(GoogleCsaFactory::class);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new GoogleCsaEventSubscriber(
            $googleCsaRequestFactory,
            $googleCsaFactory,
            $googleCsaRegistry,
            $debugRequest,
            $requestInfo,
            $trademarkInfringementResultBlocker
        );

        try {
            $subscriber->onJsonTemplateHandled($event);
        } catch (\Exception) {
            // Ignore exceptions since we're not testing the factories
        } finally {
            $this->expectNotToPerformAssertions();
        }
    }

    public function testOnJsonTemplateHandledDoesNothingWhenGoogleAdsDisabled(): void
    {
        $debugRequest = $this->createMock(DebugRequestInterface::class);
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $trademarkInfringementResultBlocker = $this->createMock(TrademarkInfringementResultBlocker::class);
        $googleCsaRegistry = new GoogleCsaRegistry();

        $view = $this->createMock(ViewInterface::class);

        $debugRequest->method('disableGoogleAds')->willReturn(true);
        $requestInfo->method('getUserAgent')->willReturn('Mozilla/5.0');
        $trademarkInfringementResultBlocker->method('blockResults')->willReturn(false);

        /** @var GoogleCsaRequestFactory $googleCsaRequestFactory */
        $googleCsaRequestFactory = self::getContainer()->get(GoogleCsaRequestFactory::class);
        /** @var GoogleCsaFactory $googleCsaFactory */
        $googleCsaFactory = self::getContainer()->get(GoogleCsaFactory::class);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new GoogleCsaEventSubscriber(
            $googleCsaRequestFactory,
            $googleCsaFactory,
            $googleCsaRegistry,
            $debugRequest,
            $requestInfo,
            $trademarkInfringementResultBlocker
        );
        $subscriber->onJsonTemplateHandled($event);

        self::assertNull($googleCsaRegistry->getGoogleCsa());
    }

    public function testOnJsonTemplateHandledDoesNothingWhenPageburstBot(): void
    {
        $debugRequest = $this->createMock(DebugRequestInterface::class);
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $trademarkInfringementResultBlocker = $this->createMock(TrademarkInfringementResultBlocker::class);
        $googleCsaRegistry = new GoogleCsaRegistry();

        $view = $this->createMock(ViewInterface::class);

        $debugRequest->method('disableGoogleAds')->willReturn(false);
        $requestInfo->method('getUserAgent')->willReturn('Mozilla/5.0 (compatible; PageBurst Bot)');
        $trademarkInfringementResultBlocker->method('blockResults')->willReturn(false);

        /** @var GoogleCsaRequestFactory $googleCsaRequestFactory */
        $googleCsaRequestFactory = self::getContainer()->get(GoogleCsaRequestFactory::class);
        /** @var GoogleCsaFactory $googleCsaFactory */
        $googleCsaFactory = self::getContainer()->get(GoogleCsaFactory::class);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new GoogleCsaEventSubscriber(
            $googleCsaRequestFactory,
            $googleCsaFactory,
            $googleCsaRegistry,
            $debugRequest,
            $requestInfo,
            $trademarkInfringementResultBlocker
        );
        $subscriber->onJsonTemplateHandled($event);

        self::assertNull($googleCsaRegistry->getGoogleCsa());
    }

    public function testOnJsonTemplateHandledDoesNothingWhenTrademarkInfringementBlocked(): void
    {
        $debugRequest = $this->createMock(DebugRequestInterface::class);
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $trademarkInfringementResultBlocker = $this->createMock(TrademarkInfringementResultBlocker::class);
        $googleCsaRegistry = new GoogleCsaRegistry();

        $view = $this->createMock(ViewInterface::class);

        $debugRequest->method('disableGoogleAds')->willReturn(false);
        $requestInfo->method('getUserAgent')->willReturn('Mozilla/5.0');
        $trademarkInfringementResultBlocker->method('blockResults')->willReturn(true);

        /** @var GoogleCsaRequestFactory $googleCsaRequestFactory */
        $googleCsaRequestFactory = self::getContainer()->get(GoogleCsaRequestFactory::class);
        /** @var GoogleCsaFactory $googleCsaFactory */
        $googleCsaFactory = self::getContainer()->get(GoogleCsaFactory::class);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new GoogleCsaEventSubscriber(
            $googleCsaRequestFactory,
            $googleCsaFactory,
            $googleCsaRegistry,
            $debugRequest,
            $requestInfo,
            $trademarkInfringementResultBlocker
        );
        $subscriber->onJsonTemplateHandled($event);

        self::assertNull($googleCsaRegistry->getGoogleCsa());
    }
}
