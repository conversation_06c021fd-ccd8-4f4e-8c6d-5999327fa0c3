<?php

declare(strict_types=1);

namespace Tests\Integration\PageHeadTags\EventSubscriber;

use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\EventSubscriber\PageHeadTagsEventSubscriber;
use App\PageHeadTags\Tags\PageHeadTags;
use App\PageHeadTags\Tags\PageHeadTagsHelper;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;

class PageHeadTagsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            PageHeadTagsEventSubscriber::class,
            'onJsonTemplateHandled',
        );

        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            PageHeadTagsEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testOnJsonTemplateHandledCallsInitFromView(): void
    {
        $twig = $this->createMock(Environment::class);
        $pageHeadTagsHelper = $this->createMock(PageHeadTagsHelper::class);
        $subscriber = new PageHeadTagsEventSubscriber($twig, $pageHeadTagsHelper);

        $view = $this->createMock(ViewInterface::class);
        $event = new JsonTemplateHandledEvent($view);

        $pageHeadTagsHelper->expects($this->once())
            ->method('initFromView')
            ->with($view);

        $subscriber->onJsonTemplateHandled($event);
    }

    public function testRenderTemplateHeadersAddsRenderedItem(): void
    {
        $twig = $this->createMock(Environment::class);
        $pageHeadTagsHelper = $this->createMock(PageHeadTagsHelper::class);
        $subscriber = new PageHeadTagsEventSubscriber($twig, $pageHeadTagsHelper);

        $pageHeadTags = new PageHeadTags(title: 'Test Title');
        $event = new RenderTemplateHeadersEvent();

        $pageHeadTagsHelper->method('getPageHeadTags')->willReturn($pageHeadTags);
        $twig->method('render')
            ->with('@theme/page_head_tags/page_head_tags.html.twig', ['page_head_tags' => $pageHeadTags])
            ->willReturn('rendered_content');

        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('rendered_content', $event->getItems()[0]);
    }
}
