<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Request\Manager;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\ParameterBag\RequestParameterBagInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class RequestManagerTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private RequestManagerInterface $requestManager;

    private RequestStack $requestStack;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var RequestManagerInterface $requestManager */
        $requestManager = self::getContainer()->get(RequestManagerInterface::class);
        $this->requestManager = $requestManager;

        /** @var RequestStack $requestStack */
        $requestStack = self::getContainer()->get(RequestStack::class);
        $this->requestStack = $requestStack;
    }

    /**
     * @return mixed[]
     */
    public static function parameterBagsDataProvider(): array
    {
        $defaultParameters = [
            'string'       => 'hello world',
            'empty_string' => '',
            'int'          => 5,
            'negative_int' => -5,
            'unsigned_int' => 10,
            'bool_yes'     => 'y',
            'bool_no'      => 'n',
            'bool_zero'    => 0,
            'bool_one'     => 1,
            'array'        => [1, 2],
            'empty_array'  => [],
        ];

        // Header parameter must start with HTTP_ and must have underscores instead of dashes
        $serverHttpParameters = array_combine(
            array_map(
                static fn (string $parameter) => sprintf('HTTP_test_header_%s', $parameter),
                array_keys($defaultParameters),
            ),
            $defaultParameters,
        );
        $serverHttpParameters = [
            ...$serverHttpParameters,
            'HTTP_user_agent'                => 'My User Agent',
            'HTTP_accept_language'           => 'fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, *;q=0.5',
            'HTTP_sec_CH_UA'                 => '"Chromium";v="104", " Not A;Brand";v="99", "Google Chrome";v="104"',
            'HTTP_sec_CH_UA_Mobile'          => '?0',
            'HTTP_sec_CH_UA_Platform'        => '"Windows"',
            'HTTP_Sec_Fetch_Dest'            => 'document',
            'HTTP_Sec_Fetch_Mode'            => 'navigate',
            'HTTP_Sec_Fetch_Site'            => 'none',
            'HTTP_Sec_Fetch_User'            => '?1',
            'HTTP_Upgrade_Insecure-Requests' => '1',
            'REQUEST_TIME'                   => 1234567890,
            'REQUEST_TIME_FLOAT'             => 1234567890.123456,
        ];

        // Reading headers in Symfony is case-insensitive and underscores are replaced with dashes
        $headerParameters = array_map(
            static function (string $parameter): string {
                $parameter = str_replace(['HTTP_', '_'], ['', '-'], $parameter);
                $parameter = mb_strtolower($parameter);

                return $parameter;
            },
            array_keys($serverHttpParameters),
        );

        $getRequest = Request::create(
            uri       : '/',
            method    : 'GET',
            parameters: $defaultParameters,
            cookies   : $defaultParameters,
            server    : $serverHttpParameters,
        );
        $getRequest->attributes->add($defaultParameters);
        $getRequest->attributes->set('_route', 'route_66');

        return [
            'get request with attributes' => [
                'request'          => $getRequest,
                'headerParameters' => $headerParameters,
            ],
            'post request'                => [
                'request'          => Request::create(
                    uri       : '/',
                    method    : 'POST',
                    parameters: $defaultParameters,
                    cookies   : $defaultParameters,
                    server    : $serverHttpParameters,
                ),
                'headerParameters' => $headerParameters,
            ],
        ];
    }

    /**
     * @param mixed[] $headerParameters
     */
    #[DataProvider('parameterBagsDataProvider')]
    public function testParameterBags(Request $request, array $headerParameters): void
    {
        $this->requestStack->push($request);

        $data = [
            'attributes_bag' => $this->parametersValueToArray(
                array_keys($request->attributes->all()),
                $this->requestManager->attributesBag(),
            ),
            'query_bag'      => $this->parametersValueToArray(
                array_keys($request->query->all()),
                $this->requestManager->queryBag(),
            ),
            'request_bag'    => $this->parametersValueToArray(
                array_keys($request->request->all()),
                $this->requestManager->requestBag(),
            ),
            'cookies_bag'    => $this->parametersValueToArray(
                array_keys($request->cookies->all()),
                $this->requestManager->cookiesBag(),
            ),
            'headers_bag'    => $this->parametersValueToArray(
                $headerParameters,
                $this->requestManager->headersBag(),
            ),
            'server_bag'     => $this->parametersValueToArray(
                array_keys($request->server->all()),
                $this->requestManager->serverBag(),
            ),
        ];

        $assertionFile = $this->initJsonAssertionFile($data);
        $assertionFile->assertSame();
    }

    /**
     * @param mixed[] $parameters
     *
     * @return mixed[]
     */
    private function parametersValueToArray(array $parameters, RequestParameterBagInterface $parameterBag): array
    {
        $data = [
            'isEmpty'       => $parameterBag->isEmpty(),
            'getParameters' => $parameterBag->getParameters(),
        ];

        foreach ($parameters as $parameter) {
            $data[$parameter] = $this->parameterValueToArray($parameter, $parameterBag);
        }

        return $data;
    }

    /**
     * @return mixed[]
     */
    private function parameterValueToArray(string $parameter, RequestParameterBagInterface $parameterBag): array
    {
        $data = [
            'getString'              => $parameterBag->getString($parameter),
            'getNullableString'      => $parameterBag->getNullableString($parameter),
            'getInt'                 => $parameterBag->getInt($parameter),
            'getNullableInt'         => $parameterBag->getNullableInt($parameter),
            'getUnsignedInt'         => $parameterBag->getUnsignedInt($parameter),
            'getNullableUnsignedInt' => $parameterBag->getNullableUnsignedInt($parameter),
            'getBool'                => $parameterBag->getBool($parameter),
            'getNullableBool'        => $parameterBag->getNullableBool($parameter),
            'getNullableArray'       => $parameterBag->getNullableArray($parameter),
        ];

        $data = array_filter(
            $data,
            static fn (mixed $value) => $value !== null,
        );

        return $data;
    }
}
