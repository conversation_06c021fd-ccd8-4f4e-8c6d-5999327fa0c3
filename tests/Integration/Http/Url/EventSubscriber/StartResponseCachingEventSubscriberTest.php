<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Url\EventSubscriber;

use App\Http\Response\Event\ResponseCachingStartedEvent;
use App\Http\Url\EventSubscriber\StartResponseCachingEventSubscriber;
use App\Http\Url\PersistentUrlParametersHelper;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersProviderInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class StartResponseCachingEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            ResponseCachingStartedEvent::NAME,
            StartResponseCachingEventSubscriber::class,
            'disablePersistentParameters',
        );
    }

    public function testDisablePersistentParametersCallsHelperDisable(): void
    {
        $persistentUrlParametersHelper = $this->createMock(PersistentUrlParametersHelper::class);

        $persistentUrlParametersHelper->expects($this->once())
            ->method('disable');

        $subscriber = new StartResponseCachingEventSubscriber($persistentUrlParametersHelper);
        $subscriber->disablePersistentParameters();
    }

    public function testDisablePersistentParametersWithRealHelper(): void
    {
        $persistentUrlParametersHelper = new PersistentUrlParametersHelper([]);

        $subscriber = new StartResponseCachingEventSubscriber($persistentUrlParametersHelper);

        // Before disabling, helper should return an empty array (no providers)
        self::assertSame([], $persistentUrlParametersHelper->getPersistentParameters(
            PersistentUrlParametersPageType::DEFAULT
        ));

        $subscriber->disablePersistentParameters();

        // After disabling, helper should still return an empty array
        self::assertSame([], $persistentUrlParametersHelper->getPersistentParameters(
            PersistentUrlParametersPageType::DEFAULT
        ));
    }

    public function testDisablePersistentParametersWithProviders(): void
    {
        // Create a mock provider that would normally return parameters
        $provider = $this->createMock(PersistentUrlParametersProviderInterface::class);
        $provider->method('getPersistentUrlParameters')
            ->willReturn(['test_param' => 'test_value']);

        $persistentUrlParametersHelper = new PersistentUrlParametersHelper([$provider]);

        // Before disabling, helper should return parameters from the provider
        self::assertSame(['test_param' => 'test_value'], $persistentUrlParametersHelper->getPersistentParameters(
            PersistentUrlParametersPageType::DEFAULT
        ));

        $subscriber = new StartResponseCachingEventSubscriber($persistentUrlParametersHelper);

        $subscriber->disablePersistentParameters();

        // After disabling, helper should return an empty array even with providers
        self::assertSame([], $persistentUrlParametersHelper->getPersistentParameters(
            PersistentUrlParametersPageType::DEFAULT
        ));
    }

    public function testDisablePersistentParametersAcrossDifferentPageTypes(): void
    {
        $provider = $this->createMock(PersistentUrlParametersProviderInterface::class);
        $provider->method('getPersistentUrlParameters')
            ->willReturn(['param' => 'value']);

        $persistentUrlParametersHelper = new PersistentUrlParametersHelper([$provider]);
        $subscriber = new StartResponseCachingEventSubscriber($persistentUrlParametersHelper);

        $subscriber->disablePersistentParameters();

        // Test all page types return empty arrays after disabling
        $pageTypes = [
            PersistentUrlParametersPageType::DEFAULT,
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            PersistentUrlParametersPageType::NEW_SEARCH,
            PersistentUrlParametersPageType::RELATED_TERMS,
        ];

        foreach ($pageTypes as $pageType) {
            self::assertSame([], $persistentUrlParametersHelper->getPersistentParameters($pageType));
        }
    }
}
