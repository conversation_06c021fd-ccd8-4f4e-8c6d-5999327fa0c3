<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Response\EventSubscriber;

use App\Http\Response\EventSubscriber\ResponseCachingEventSubscriber;
use App\Http\Response\ResponseCachingHelper;
use App\Kernel\KernelResponseEvent;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

class ResponseCachingEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            KernelResponseEvent::NO_REDIRECT->value,
            ResponseCachingEventSubscriber::class,
            'enableResponseCaching',
        );
    }

    public function testEnableResponseCachingWhenCachingStarted(): void
    {
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $dateTimeFactory = new DateTimeFactory();
        $responseCachingHelper = new ResponseCachingHelper($eventDispatcher, $dateTimeFactory);

        $subscriber = new ResponseCachingEventSubscriber($responseCachingHelper);

        // Start response caching
        $responseCachingHelper->startResponseCaching(60);

        $response = new Response();
        $response->headers->set('X-Log-Test', 'test');
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->enableResponseCaching($responseEvent);

        self::assertNotNull($response->getExpires());
        self::assertSame(60, $response->getMaxAge());
        self::assertTrue($response->headers->getCacheControlDirective('public'));

        // Verify that X-Log headers were removed
        self::assertFalse($response->headers->has('X-Log-Test'));
    }

    public function testEnableResponseCachingWhenCachingNotStarted(): void
    {
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $dateTimeFactory = new DateTimeFactory();
        $responseCachingHelper = new ResponseCachingHelper($eventDispatcher, $dateTimeFactory);

        $subscriber = new ResponseCachingEventSubscriber($responseCachingHelper);

        $response = new Response();
        $response->headers->set('X-Log-Test', 'test');
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->enableResponseCaching($responseEvent);

        self::assertNull($response->getExpires());
        self::assertNull($response->getMaxAge());
        self::assertFalse($response->headers->hasCacheControlDirective('public'));

        // X-Log headers should remain when caching is not started
        self::assertTrue($response->headers->has('X-Log-Test'));
    }

    public function testEnableResponseCachingWithDifferentTtl(): void
    {
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $dateTimeFactory = new DateTimeFactory();
        $responseCachingHelper = new ResponseCachingHelper($eventDispatcher, $dateTimeFactory);

        $subscriber = new ResponseCachingEventSubscriber($responseCachingHelper);

        $testTtls = [30, 120, 300, 3600];

        foreach ($testTtls as $ttl) {
            $responseCachingHelper->startResponseCaching($ttl);

            $response = new Response();
            $responseEvent = new ResponseEvent(
                $this->createMock(HttpKernelInterface::class),
                new Request(),
                HttpKernelInterface::MAIN_REQUEST,
                $response
            );

            $subscriber->enableResponseCaching($responseEvent);

            self::assertSame($ttl, $response->getMaxAge());
            self::assertNotNull($response->getExpires());
        }
    }
}
