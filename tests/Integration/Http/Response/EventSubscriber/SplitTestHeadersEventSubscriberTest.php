<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Response\EventSubscriber;

use App\Http\Response\EventSubscriber\SplitTestHeadersEventSubscriber;
use App\Kernel\KernelResponseEvent;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class SplitTestHeadersEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            KernelResponseEvent::NO_REDIRECT->value,
            SplitTestHeadersEventSubscriber::class,
            'setSplitTestHeadersResponse',
        );
    }

    public function testSetSplitTestHeadersResponseWithIdAndVariant(): void
    {
        $splitTestExtendedReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $splitTestExtendedReader->method('getId')->willReturn(123);
        $splitTestExtendedReader->method('getVariant')->willReturn('test-variant');

        $subscriber = new SplitTestHeadersEventSubscriber($splitTestExtendedReader);

        $response = new Response();
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->setSplitTestHeadersResponse($responseEvent);

        self::assertSame('123', $response->headers->get('X-Log-Split_Test_Id'));
        self::assertSame('test-variant', $response->headers->get('X-Log-Split_Test_Variant'));
    }

    public function testSetSplitTestHeadersResponseWithIdOnly(): void
    {
        $splitTestExtendedReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $splitTestExtendedReader->method('getId')->willReturn(456);
        $splitTestExtendedReader->method('getVariant')->willReturn(null);

        $subscriber = new SplitTestHeadersEventSubscriber($splitTestExtendedReader);

        $response = new Response();
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->setSplitTestHeadersResponse($responseEvent);

        self::assertSame('456', $response->headers->get('X-Log-Split_Test_Id'));
        self::assertFalse($response->headers->has('X-Log-Split_Test_Variant'));
    }

    public function testSetSplitTestHeadersResponseWithVariantOnly(): void
    {
        $splitTestExtendedReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $splitTestExtendedReader->method('getId')->willReturn(null);
        $splitTestExtendedReader->method('getVariant')->willReturn('variant-only');

        $subscriber = new SplitTestHeadersEventSubscriber($splitTestExtendedReader);

        $response = new Response();
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->setSplitTestHeadersResponse($responseEvent);

        self::assertFalse($response->headers->has('X-Log-Split_Test_Id'));
        self::assertSame('variant-only', $response->headers->get('X-Log-Split_Test_Variant'));
    }

    public function testSetSplitTestHeadersResponseWithNeitherIdNorVariant(): void
    {
        $splitTestExtendedReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $splitTestExtendedReader->method('getId')->willReturn(null);
        $splitTestExtendedReader->method('getVariant')->willReturn(null);

        $subscriber = new SplitTestHeadersEventSubscriber($splitTestExtendedReader);

        $response = new Response();
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->setSplitTestHeadersResponse($responseEvent);

        self::assertFalse($response->headers->has('X-Log-Split_Test_Id'));
        self::assertFalse($response->headers->has('X-Log-Split_Test_Variant'));
    }
}
