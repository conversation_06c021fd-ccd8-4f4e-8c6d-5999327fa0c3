<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Response\EventSubscriber;

use App\Http\Request\Main\MainRequestInterface;
use App\Http\Response\EventSubscriber\SearchTypeHeaderEventSubscriber;
use App\JsonTemplate\Event\JsonTemplateSearchSubmittedEvent;
use App\Kernel\KernelResponseEvent;
use App\Search\Request\SearchRequestFlag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class DefaultSearchTypeHeaderEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateSearchSubmittedEvent::NAME,
            SearchTypeHeaderEventSubscriber::class,
            'setSearchTypeValue',
        );
        $this->assertEventListenerInstanceIsRegistered(
            KernelResponseEvent::NO_REDIRECT->value,
            SearchTypeHeaderEventSubscriber::class,
            'setSearchTypeHeaderResponse',
        );
    }

    public function testSetSearchTypeValueSetsSearchTypeFromRequest(): void
    {
        $mainRequest = $this->createMock(MainRequestInterface::class);
        $request = new Request();
        $request->attributes->set(SearchRequestFlag::TYPE, 'organic');

        $mainRequest->method('getRequest')->willReturn($request);

        $subscriber = new SearchTypeHeaderEventSubscriber($mainRequest);
        $subscriber->setSearchTypeValue();

        $response = new Response();
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->setSearchTypeHeaderResponse($responseEvent);

        self::assertSame('organic', $response->headers->get('X-Log-Search_Type'));
    }

    public function testSetSearchTypeHeaderResponseDoesNothingWhenNoSearchType(): void
    {
        $mainRequest = $this->createMock(MainRequestInterface::class);
        $request = new Request();

        $mainRequest->method('getRequest')->willReturn($request);

        $subscriber = new SearchTypeHeaderEventSubscriber($mainRequest);

        $response = new Response();
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->setSearchTypeHeaderResponse($responseEvent);

        self::assertFalse($response->headers->has('X-Log-Search_Type'));
    }

    public function testSetSearchTypeHeaderResponseWithDifferentSearchTypes(): void
    {
        $testCases = ['search', 'organic', 'news', 'images'];

        foreach ($testCases as $searchType) {
            $mainRequest = $this->createMock(MainRequestInterface::class);
            $request = new Request();
            $request->attributes->set(SearchRequestFlag::TYPE, $searchType);

            $mainRequest->method('getRequest')->willReturn($request);

            $subscriber = new SearchTypeHeaderEventSubscriber($mainRequest);
            $subscriber->setSearchTypeValue();

            $response = new Response();
            $responseEvent = new ResponseEvent(
                $this->createMock(HttpKernelInterface::class),
                new Request(),
                HttpKernelInterface::MAIN_REQUEST,
                $response
            );

            $subscriber->setSearchTypeHeaderResponse($responseEvent);

            self::assertSame($searchType, $response->headers->get('X-Log-Search_Type'));
        }
    }
}
