<?php

declare(strict_types=1);

namespace Tests\Integration\Highlight\Twig;

use App\Highlight\HighlightHelper;
use App\Highlight\Twig\HighlightExtension;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;
use Twig\Loader\ArrayLoader;

class HighlightExtensionTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private Environment $twig;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var HighlightHelper $highlightHelper */
        $highlightHelper = self::getContainer()->get(HighlightHelper::class);
        $highlightExtension = new HighlightExtension($highlightHelper);

        $this->twig = new Environment(
            new ArrayLoader(
                [
                    'test' => 'Test {{ input|highlight(query, applyHighlight) }} output',
                ],
            ),
        );

        $this->twig->addExtension($highlightExtension);
    }

    /**
     * @return mixed[]
     */
    public static function textDataProvider(): array
    {
        return [
            'simple with highlighting'           => [
                'The quick brown fox jumps over the lazy dog',
                'lazy',
                true,
                'The quick brown fox jumps over the <strong>lazy</strong> dog',
            ],
            'simple without highlighting'        => [
                'The quick brown fox jumps over the lazy dog',
                'lazy',
                false,
                'The quick brown fox jumps over the lazy dog',
            ],
            'special with highlighting'          => [
                '<script>alert("\'1\'")</script>',
                'alert',
                true,
                '&lt;script&gt;<strong>alert</strong>(&quot;&#039;1&#039;&quot;)&lt;/script&gt;',
            ],
            'special without highlighting'       => [
                '<script>alert("\'1\'")</script>',
                'alert',
                false,
                '&lt;script&gt;alert(&quot;&#039;1&#039;&quot;)&lt;/script&gt;',
            ],
            'diacritics'                         => [
                'Hội An (會安) translates as "peaceful meeting place".',
                'Hoi',
                true,
                '<strong>Hội</strong> An (會安) translates as &quot;peaceful meeting place&quot;.',
            ],
            'diacritics_from'                    => [
                'Hoi An (會安) translates as "peaceful meeting place".',
                'Hội',
                true,
                '<strong>Hoi</strong> An (會安) translates as &quot;peaceful meeting place&quot;.',
            ],
            'accents'                            => [
                'Wir Wiener Wäscheweiber wäschten weisse Wäsche, Wenn wir wüssten, wo warmes, weiches Wasser wär.',
                'wäsche',
                true,
                'Wir Wiener Wäscheweiber wäschten weisse <strong>Wäsche</strong>, Wenn wir wüssten, wo warmes, weiches Wasser wär.',
            ],
            'entities surround with white space' => [
                'HTML entities are & great < >.',
                'are & < >',
                true,
                // This is correct, HTML entities are not matched with word boundary
                'HTML entities <strong>are</strong> &amp; great &lt; &gt;.',
            ],
            'entities in words'                  => [
                'Eats&Drinks. We do our best to source the highest quality ingredients.',
                'Eats & Drinks',
                true,
                '<strong>Eats&amp;Drinks</strong>. We do our best to source the highest quality ingredients.',
            ],
            'exclusions'                         => [
                'De beste stuurlui staan aan wal.',
                '-stuurlui site:wal',
                true,
                'De beste stuurlui staan aan wal.',
            ],
        ];
    }

    #[DataProvider('textDataProvider')]
    public function testHighlight(string $input, string $query, bool $applyHighlight, string $expectedOutput): void
    {
        $output = $this->twig->render(
            'test',
            [
                'input'          => $input,
                'query'          => $query,
                'applyHighlight' => $applyHighlight,
            ],
        );
        $expectedOutput = sprintf('Test %s output', $expectedOutput);
        self::assertSame($expectedOutput, $output);
    }
}
