<?php

declare(strict_types=1);

namespace Tests\Integration\App\EventSubscriber;

use App\App\EventSubscriber\AppEventSubscriber;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

final class AppEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            AppEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testRenderTemplateHeaders(): void
    {
        $request = self::createRequest('/');
        $this->setRequest($request);

        $event = new RenderTemplateHeadersEvent();

        /** @var AppEventSubscriber $eventSubscriber */
        $eventSubscriber = self::getContainer()->get(AppEventSubscriber::class);

        // Call the method multiple times
        $eventSubscriber->renderTemplateHeaders($event);
        $eventSubscriber->renderTemplateHeaders($event);
        $eventSubscriber->renderTemplateHeaders($event);

        // Should only have one item despite multiple calls
        self::assertCount(1, $event->getItems());
    }

}
