<?php

declare(strict_types=1);

namespace Tests\Integration\Tracking\Request;

use App\Tracking\Model\Network;
use App\Tracking\Request\SeaRequestInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractRequestIntegrationTestCase;

class SeaRequestTest extends AbstractRequestIntegrationTestCase
{
    private SeaRequestInterface $seaRequest;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var SeaRequestInterface $seaRequest */
        $seaRequest = self::getContainer()->get(SeaRequestInterface::class);
        $this->seaRequest = $seaRequest;
    }

    /**
     * @return mixed[]
     */
    public static function urlParsingDataProvider(): array
    {
        $defaultExpectedValues = [
            SeaRequestInterface::PARAMETER_CAMPAIGN_ID                => null,
            SeaRequestInterface::PARAMETER_CAMPAIGN_NAME              => null,
            SeaRequestInterface::PARAMETER_DEVICE                     => null,
            SeaRequestInterface::PARAMETER_NETWORK                    => null,
            SeaRequestInterface::PARAMETER_ACCOUNT_ID                 => null,
            SeaRequestInterface::PARAMETER_AD_GROUP_ID                => null,
            SeaRequestInterface::PARAMETER_KEYWORD_ID                 => null,
            SeaRequestInterface::PARAMETER_GOOGLE_LOCATION_ID         => null,
            SeaRequestInterface::PARAMETER_REFERRER_AD_CREATIVE       => null,
            SeaRequestInterface::PARAMETER_ADDITIONAL_CHANNEL         => null,
            SeaRequestInterface::PARAMETER_PUBLISHER                  => null,
            SeaRequestInterface::PARAMETER_GENERIC_SECONDARY_CLICK_ID => null,
            SeaRequestInterface::HEADER_X_LOADBALANCER_STYLE_ID       => null,
            SeaRequestInterface::HEADER_X_LOADBALANCER_VBB_SPAMBLOCK  => null,
            SeaRequestInterface::PARAMETER_CUSTOM_ID                  => null,
        ];

        $data = [
            'google click'                             => [
                'url'            => '/?q=ipad+air&asid=abcd&nw=g&de=t&ac=123&gclid=efgh&cid=2&aid=3&kid=kwd-4&lpid=54',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'cid'   => 2,
                    'asid'  => 'abcd',
                    'de'    => 't',
                    'nw'    => 'g',
                    'ac'    => 123,
                    'aid'   => 3,
                    'kid'   => 4,
                    'gclid' => 'efgh',
                    'lpid'  => '54',
                ],
            ],
            'microsoft click'                          => [
                'url'            => '/?q=ipad+air&asid=abcd&nw=g&de=t&ac=123&msclkid=efgh&cid=2&aid=3&kid=kwd-4',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'cid'     => 2,
                    'asid'    => 'abcd',
                    'de'      => 't',
                    'nw'      => 'g',
                    'ac'      => 123,
                    'aid'     => 3,
                    'kid'     => 4,
                    'msclkid' => 'efgh',
                ],
            ],
            'publisher'                                => [
                'url'            => '/?q=ipad+air&pub=publisher.com',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'pub' => 'publisher.com',
                ],
            ],
            'invalid values'                           => [
                'url'            => '/?de=p&nw=zz',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                ],
            ],
            'incorrect keyword id value'               => [
                'url'            => '/?de=t&kid=akwd-467ae2',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'de'  => 't',
                    'kid' => 467,
                ],
            ],
            'incorrect encoded value'                  => [
                'url'            => '/?asid=pizz'.urldecode('%C2').'&rac=r'.urldecode('%C2').'c',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'asid' => 'pizzÂ',
                    'rac'  => 'rÂc',
                ],
            ],
            'empty values'                             => [
                'url'            => '/?q=ipad+air&asid=%20&nw=&de=&ac=0&gclid=&msclkid=&kid=&aid=&cid=&rac=',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                ],
            ],
            'none'                                     => [
                'url'            => '/',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                ],
            ],
            'single channel'                           => [
                'url'            => '/?ch=123',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'ch' => ['123'],
                ],
            ],
            'multiple channel'                         => [
                'url'            => '/?ch=123,,12,abc,%20abc3',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'ch' => ['123', '12', 'abc', 'abc3'],
                ],
            ],
            'Priority click-id gclid'                  => [
                'url'            => '/?q=ipad+air&asid=abcd&nw=g&de=t&ac=123&gclid=efgh&cid=2&aid=3&kid=kwd-4&lpid=54&gbraid=ijkl',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'cid'   => 2,
                    'asid'  => 'abcd',
                    'de'    => 't',
                    'nw'    => 'g',
                    'ac'    => 123,
                    'aid'   => 3,
                    'kid'   => 4,
                    'gclid' => 'efgh',
                    'lpid'  => '54',
                ],
            ],
            'clid lowest priority'                     => [
                'url'            => '/?q=ipad+air&asid=abcd&nw=g&de=t&ac=123&kid=kwd-4&lpid=54&gbraid=ijkl&clid=1337',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'asid'   => 'abcd',
                    'de'     => 't',
                    'nw'     => 'g',
                    'ac'     => 123,
                    'kid'    => 4,
                    'gbraid' => 'ijkl',
                    'lpid'   => '54',
                ],
            ],
            'No priority click-id, use first click-id' => [
                'url'            => '/?q=ipad+air&asid=abcd&nw=g&de=t&ac=123&kid=kwd-4&lpid=54&gbraid=ijkl&qclid=1337',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'asid'   => 'abcd',
                    'de'     => 't',
                    'nw'     => 'g',
                    'ac'     => 123,
                    'kid'    => 4,
                    'gbraid' => 'ijkl',
                    'lpid'   => '54',
                ],
            ],
        ];

        // Network values
        $data['Network invalid'] = [
            'url'            => '/?q=car&nw=zz',
            'expectedValues' => [
                ...$defaultExpectedValues,
            ],
        ];

        foreach (Network::cases() as $network) {
            $data[sprintf('Network %s', $network->name)] = [
                'url'            => sprintf('/?q=car&nw=%s', $network->value),
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    'nw' => $network->value,
                ],
            ];
        }

        return $data;
    }

    /**
     * @param mixed[] $expectedValues
     */
    #[DataProvider('urlParsingDataProvider')]
    public function testUrlParsing(string $url, array $expectedValues): void
    {
        $this->assertRequest(
            $url,
            null,
            $this->seaRequest,
            $expectedValues,
        );
    }
}
