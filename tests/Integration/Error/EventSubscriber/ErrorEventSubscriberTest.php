<?php

declare(strict_types=1);

namespace Tests\Integration\Error\EventSubscriber;

use App\BingAds\Helper\BingAdsHelper;
use App\Error\EventSubscriber\ErrorEventSubscriber;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\JsonTemplate\View\ViewInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\GoogleCsa\GoogleCsaStubBuilder;
use Visymo\BingAds\BingAds;
use Visymo\BingAds\PageOptions\PageOptions;

class ErrorEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            ErrorEventSubscriber::class,
            'onJsonTemplateHandled',
        );
    }

    public function testOnJsonTemplateHandledAddsErrorChannelWhenStatusCodeIs400OrHigher(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $bingAdsHelper = $this->createMock(BingAdsHelper::class);

        $googleCsa = (new GoogleCsaStubBuilder())->withPublisherId('test-publisher')->create();
        $bingAds = $this->createMock(BingAds::class);
        $pageOptions = $this->createMock(PageOptions::class);

        $response = new Response('', 500);
        $view = $this->createMock(ViewInterface::class);
        $view->method('getResponse')->willReturn($response);

        // Set up the GoogleCsa in the registry
        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $bingAdsHelper->method('getBingAds')->willReturn($bingAds);
        $bingAds->method('getPageOptions')->willReturn($pageOptions);

        $pageOptions->expects($this->once())->method('addTracingTag')->with('error_500');

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new ErrorEventSubscriber($googleCsaRegistry, $bingAdsHelper);
        $subscriber->onJsonTemplateHandled($event);

        // Verify that the channel was added to GoogleCsa
        self::assertContains('error_500', $googleCsa->getChannels());
    }

    public function testOnJsonTemplateHandledDoesNothingWhenStatusCodeIsBelow400(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $bingAdsHelper = $this->createMock(BingAdsHelper::class);

        $response = new Response('', 200);
        $view = $this->createMock(ViewInterface::class);
        $view->method('getResponse')->willReturn($response);

        $bingAdsHelper->method('getBingAds')->willReturn(null);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new ErrorEventSubscriber($googleCsaRegistry, $bingAdsHelper);
        $subscriber->onJsonTemplateHandled($event);

        $this->expectNotToPerformAssertions();
    }

    public function testOnJsonTemplateHandledHandlesNullGoogleCsaAndBingAds(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $bingAdsHelper = $this->createMock(BingAdsHelper::class);

        $response = new Response('', 404);
        $view = $this->createMock(ViewInterface::class);
        $view->method('getResponse')->willReturn($response);

        $bingAdsHelper->method('getBingAds')->willReturn(null);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new ErrorEventSubscriber($googleCsaRegistry, $bingAdsHelper);
        $subscriber->onJsonTemplateHandled($event);

        $this->expectNotToPerformAssertions();
    }
}
