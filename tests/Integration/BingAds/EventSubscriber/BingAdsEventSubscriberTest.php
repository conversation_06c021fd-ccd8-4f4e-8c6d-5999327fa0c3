<?php

declare(strict_types=1);

namespace Tests\Integration\BingAds\EventSubscriber;

use App\BingAds\EventSubscriber\BingAdsEventSubscriber;
use App\BingAds\Helper\BingAdsHelper;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class BingAdsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            BingAdsEventSubscriber::class,
            'onJsonTemplateHandled',
        );
    }

    public function testOnJsonTemplateHandledCallsInitFromViewDataRequest(): void
    {
        $bingAdsHelper = $this->createMock(BingAdsHelper::class);

        $dataRequest = new ViewDataRequest();
        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn($dataRequest);

        $bingAdsHelper->expects($this->once())
            ->method('initFromViewDataRequest')
            ->with($dataRequest->bingAds());

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new BingAdsEventSubscriber($bingAdsHelper);
        $subscriber->onJsonTemplateHandled($event);
    }
}
