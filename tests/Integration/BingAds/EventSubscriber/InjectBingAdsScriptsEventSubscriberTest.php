<?php

declare(strict_types=1);

namespace Tests\Integration\BingAds\EventSubscriber;

use App\BingAds\DebugInfoProvider\BingAdsDebugInfoRegistry;
use App\BingAds\EventSubscriber\InjectBingAdsScriptsEventSubscriber;
use App\BingAds\Helper\BingAdsHelper;
use App\Office\Request\OfficeRequestInterface;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;
use Visymo\BingAds\BingAds;
use Visymo\BingAds\BingAdsRendererInterface;

class InjectBingAdsScriptsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private BingAdsHelper & MockObject $bingAdsHelper;

    private BingAdsRendererInterface & MockObject $bingAdsRenderer;

    private BingAdsDebugInfoRegistry & MockObject $debugInfoRegistry;

    private OfficeRequestInterface & MockObject $officeRequest;

    private Environment & MockObject $twig;

    protected function setUp(): void
    {
        parent::setUp();

        $this->bingAdsHelper = $this->createMock(BingAdsHelper::class);
        $this->bingAdsRenderer = $this->createMock(BingAdsRendererInterface::class);
        $this->debugInfoRegistry = $this->createMock(BingAdsDebugInfoRegistry::class);
        $this->officeRequest = $this->createMock(OfficeRequestInterface::class);
        $this->twig = $this->createMock(Environment::class);
    }

    /**
     * @return mixed[]
     */
    public static function renderTemplateHeadersDataProvider(): array
    {
        return [
            'has units with office request'     => [
                'hasUnits'          => true,
                'isOffice'          => true,
                'expectRender'      => true,
                'expectedAdStyleId' => 12345,
            ],
            'has units with non-office request' => [
                'hasUnits'          => true,
                'isOffice'          => false,
                'expectRender'      => true,
                'expectedAdStyleId' => null,
            ],
            'no units'                          => [
                'hasUnits'          => false,
                'isOffice'          => true,
                'expectRender'      => false,
                'expectedAdStyleId' => null,
            ],
        ];
    }

    /**
     * @return mixed[]
     */
    public static function renderTemplateFootersDataProvider(): array
    {
        return [
            'headers rendered and bing ads available'     => [
                'headersRendered'     => true,
                'bingAdsAvailable'    => true,
                'expectRender'        => true,
                'expectDebugRegister' => true,
            ],
            'headers not rendered'                        => [
                'headersRendered'     => false,
                'bingAdsAvailable'    => true,
                'expectRender'        => false,
                'expectDebugRegister' => false,
            ],
            'headers rendered but bing ads not available' => [
                'headersRendered'     => true,
                'bingAdsAvailable'    => false,
                'expectRender'        => false,
                'expectDebugRegister' => false,
            ],
        ];
    }

    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            InjectBingAdsScriptsEventSubscriber::class,
            'renderTemplateHeaders',
        );
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateFootersEvent::NAME,
            InjectBingAdsScriptsEventSubscriber::class,
            'renderTemplateFooters',
        );
    }

    #[DataProvider('renderTemplateHeadersDataProvider')]
    public function testRenderTemplateHeaders(
        bool $hasUnits,
        bool $isOffice,
        bool $expectRender,
        ?int $expectedAdStyleId
    ): void
    {
        $this->bingAdsHelper->method('hasUnits')->willReturn($hasUnits);
        $this->officeRequest->method('isOffice')->willReturn($isOffice);

        if ($expectRender) {
            $bingAds = $this->createMock(BingAds::class);
            $this->bingAdsHelper->method('getBingAds')->willReturn($bingAds);
            $this->bingAdsHelper->method('getAdStyleId')->willReturn($expectedAdStyleId);
            $this->bingAdsRenderer->method('getScriptHtml')->with($bingAds)->willReturn('script-html');
            $this->twig->expects($this->once())
                ->method('render')
                ->with(
                    '@theme/bing_ads/bing_ads_template_headers.html.twig',
                    [
                        'script_html'       => 'script-html',
                        'debug_ad_style_id' => $expectedAdStyleId,
                    ],
                )
                ->willReturn('header-content');
        } else {
            $this->twig->expects($this->never())->method('render');
        }

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectBingAdsScriptsEventSubscriber(
            $this->bingAdsHelper,
            $this->bingAdsRenderer,
            $this->debugInfoRegistry,
            $this->officeRequest,
            $this->twig,
        );
        $subscriber->renderTemplateHeaders($event);

        if ($expectRender) {
            self::assertCount(1, $event->getItems());
            self::assertSame('header-content', $event->getItems()[0]);
        } else {
            self::assertEmpty($event->getItems());
        }
    }

    #[DataProvider('renderTemplateFootersDataProvider')]
    public function testRenderTemplateFooters(
        bool $headersRendered,
        bool $bingAdsAvailable,
        bool $expectRender,
        bool $expectDebugRegister
    ): void
    {
        $subscriber = new InjectBingAdsScriptsEventSubscriber(
            $this->bingAdsHelper,
            $this->bingAdsRenderer,
            $this->debugInfoRegistry,
            $this->officeRequest,
            $this->twig
        );

        if ($headersRendered) {
            // Setup for headers rendering
            $this->bingAdsHelper->method('hasUnits')->willReturn(true);
            $this->officeRequest->method('isOffice')->willReturn(false);
            $bingAds = $this->createMock(BingAds::class);

            if ($bingAdsAvailable) {
                $this->bingAdsHelper->method('getBingAds')->willReturnOnConsecutiveCalls($bingAds, $bingAds);
                $this->bingAdsRenderer->method('getScriptHtml')->with($bingAds)->willReturn('script-html');
                $this->bingAdsRenderer->method('getJavaScript')->with($bingAds)->willReturn('javascript-code');
            } else {
                $this->bingAdsHelper->method('getBingAds')->willReturnOnConsecutiveCalls($bingAds, null);
                $this->bingAdsRenderer->method('getScriptHtml')->with($bingAds)->willReturn('script-html');
            }

            $this->twig->method('render')
                ->willReturnCallback(
                    static fn ($template, $params) => match ($template) {
                        '@theme/bing_ads/bing_ads_template_headers.html.twig' => 'header-content',
                        '@theme/bing_ads/bing_ads_template_footers.html.twig' => 'footer-content',
                        default                                               => '',
                    },
                );

            // First, render headers to set the flag
            $headerEvent = new RenderTemplateHeadersEvent();
            $subscriber->renderTemplateHeaders($headerEvent);
        }

        if ($expectDebugRegister) {
            $this->debugInfoRegistry->expects($this->once())->method('register');
        } else {
            $this->debugInfoRegistry->expects($this->never())->method('register');
        }

        if (!$headersRendered) {
            $this->twig->expects($this->never())->method('render');
        }

        // Then render footers
        $footerEvent = new RenderTemplateFootersEvent();
        $subscriber->renderTemplateFooters($footerEvent);

        if ($expectRender) {
            self::assertCount(1, $footerEvent->getItems());
            self::assertSame('footer-content', $footerEvent->getItems()[0]);
        } else {
            self::assertEmpty($footerEvent->getItems());
        }
    }
}
