<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase\WebSearch;

use App\Debug\Request\DebugRequestInterface;
use App\Generic\Device\Device;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\Network;
use App\Tracking\Request\SeaRequestInterface;
use Tests\Frontend\FrontendTestSettings;
use Tests\Frontend\TestCase\AbstractTestCase;

final class WebSearchTestCase extends AbstractTestCase
{
    /**
     * @param int[] $responsiveBreakpoints
     */
    protected function __construct(
        string $brandSlug,
        string $name,
        Device $device,
        string $url,
        array $responsiveBreakpoints,
        ?float $allowedScreenshotDifference,
        public readonly string $domain,
        public readonly string $query,
        public readonly bool $hasGoogleAds,
        public readonly bool $hasGoogleRelatedTerms,
        public readonly bool $hasRelated,
        public readonly bool $createScreenshotFromBottomOfPage
    )
    {
        parent::__construct(
            brandSlug                       : $brandSlug,
            name                            : $name,
            device                          : $device,
            url                             : $url,
            responsiveBreakpoints           : $responsiveBreakpoints,
            allowedScreenshotDifference     : $allowedScreenshotDifference,
            createScreenshotFromBottomOfPage: $createScreenshotFromBottomOfPage,
        );
    }

    protected function getTestCaseSlug(): string
    {
        return 'ws';
    }

    /**
     * @param int[] $responsiveBreakpoints
     */
    public static function create(
        string $brandSlug,
        string $name,
        Device $device,
        string $domain,
        string $query,
        array $responsiveBreakpoints,
        bool $hasGoogleAds,
        bool $hasGoogleRelatedTerms,
        bool $hasRelated,
        bool $createScreenshotFromBottomOfPage,
        ?float $allowedScreenshotDifference = null
    ): self
    {
        $url = sprintf(
            'https://%s/ws?%s',
            $domain,
            http_build_query(
                [
                    SearchRequestInterface::PARAMETER_QUERY      => $query,
                    SeaRequestInterface::PARAMETER_CAMPAIGN_NAME => 'sw_uk_example_01',
                    SeaRequestInterface::PARAMETER_DEVICE        => $device->getShortValue(),
                    SeaRequestInterface::PARAMETER_CAMPAIGN_ID   => '30266123',
                    SeaRequestInterface::PARAMETER_AD_GROUP_ID   => '266532210',
                    SeaRequestInterface::PARAMETER_KEYWORD_ID    => 'kwd-1408394680',
                    SeaRequestInterface::PARAMETER_NETWORK       => Network::GOOGLE_SEARCH->value,
                    ClickIdSource::GOOGLE_CLICK_ID->value        => 'gclid-61c2da873856d',

                    DebugRequestInterface::PARAMETER_DEBUG_COUNTRY_CODE        => 'UK',

                    // Special test Ad from Google
                    DebugRequestInterface::PARAMETER_DEBUG_SHOW_GOOGLE_TEST_AD => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_STYLE_ID      => FrontendTestSettings::GOOGLE_AD_STYLE_ID,
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_MOCK_SEARCH   => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_SHOW_FIXED_STATS    => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_PROFILER    => 1,

                    // Ad bot needs to be disabled for landing page to work, but better performance when not needed
                    DebugRequestInterface::PARAMETER_DEBUG_IS_AD_BOT           => 0,

                    // Prevent being included in split test (invalid values have no effect)
                    DebugRequestInterface::PARAMETER_DEBUG_SPLIT_TEST_VARIANT  => 'visual_browser_test',
                    DebugRequestInterface::PARAMETER_DEBUG_ENABLE_MODULE       => 1,
                ],
            ),
        );

        return new self(
            brandSlug                       : $brandSlug,
            name                            : $name,
            device                          : $device,
            url                             : $url,
            responsiveBreakpoints           : $responsiveBreakpoints,
            allowedScreenshotDifference     : $allowedScreenshotDifference,
            domain                          : $domain,
            query                           : $query,
            hasGoogleAds                    : $hasGoogleAds,
            hasGoogleRelatedTerms           : $hasGoogleRelatedTerms,
            hasRelated                      : $hasRelated,
            createScreenshotFromBottomOfPage: $createScreenshotFromBottomOfPage,
        );
    }

    public function getTestRunner(): string
    {
        return WebSearchTestRunner::class;
    }
}
