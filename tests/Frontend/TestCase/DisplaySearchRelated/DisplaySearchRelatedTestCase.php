<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase\DisplaySearchRelated;

use App\Debug\Request\DebugRequestInterface;
use App\Generic\Device\Device;
use App\JsonTemplate\Request\JsonTemplateRequestInterface;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\Network;
use App\Tracking\Request\SeaRequestInterface;
use Tests\Frontend\FrontendTestSettings;
use Tests\Frontend\TestCase\AbstractTestCase;

final class DisplaySearchRelatedTestCase extends AbstractTestCase
{
    /**
     * @param int[] $responsiveBreakpoints
     */
    protected function __construct(
        string $brandSlug,
        string $name,
        Device $device,
        string $url,
        array $responsiveBreakpoints,
        public readonly string $domain,
        public readonly string $query,
        public readonly bool $hasVisymoRelatedTerms,
        public readonly bool $hasGoogleRelatedTerms,
        public readonly bool $hasGoogleRelatedTermsWithFallback,
        public readonly bool $hasGoogleAdsOnRelatedPage,
        public readonly bool $hasGoogleAdManager,
        public readonly bool $expectsQueryInTitle,
        public readonly bool $createScreenshotFromBottomOfPage
    )
    {
        parent::__construct(
            brandSlug                       : $brandSlug,
            name                            : $name,
            device                          : $device,
            url                             : $url,
            responsiveBreakpoints           : $responsiveBreakpoints,
            createScreenshotFromBottomOfPage: $createScreenshotFromBottomOfPage,
        );
    }

    protected function getTestCaseSlug(): string
    {
        return 'dsr';
    }

    /**
     * @param int[] $responsiveBreakpoints
     */
    public static function create(
        string $brandSlug,
        string $name,
        Device $device,
        string $domain,
        string $query,
        ?string $templateVariant,
        array $responsiveBreakpoints,
        bool $hasVisymoRelatedTerms,
        bool $hasGoogleRelatedTerms,
        bool $hasGoogleRelatedTermsWithFallback,
        bool $hasGoogleAdManager,
        bool $hasGoogleAdsOnRelatedPage,
        bool $createScreenshotFromBottomOfPage,
        bool $expectsQueryInTitle = false
    ): self
    {
        $url = sprintf(
            'https://%s/dsr?%s',
            $domain,
            http_build_query(
                [
                    SearchRequestInterface::PARAMETER_QUERY                  => $query,
                    SeaRequestInterface::PARAMETER_CAMPAIGN_NAME             => 'sw_uk_example_01',
                    SeaRequestInterface::PARAMETER_DEVICE                    => $device->getShortValue(),
                    SeaRequestInterface::PARAMETER_CAMPAIGN_ID               => '30266123',
                    SeaRequestInterface::PARAMETER_AD_GROUP_ID               => '266532210',
                    SeaRequestInterface::PARAMETER_KEYWORD_ID                => 'kwd-1408394680',
                    SeaRequestInterface::PARAMETER_NETWORK                   => Network::GOOGLE_SEARCH->value,
                    ClickIdSource::GOOGLE_CLICK_ID->value                    => 'gclid-61c2da873856d',
                    JsonTemplateRequestInterface::PARAMETER_TEMPLATE_VARIANT => $templateVariant,

                    DebugRequestInterface::PARAMETER_DEBUG_COUNTRY_CODE        => 'UK',

                    // Special test Ad from Google
                    DebugRequestInterface::PARAMETER_DEBUG_SHOW_GOOGLE_TEST_AD => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_STYLE_ID      => FrontendTestSettings::GOOGLE_AD_STYLE_ID,
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_MOCK_SEARCH   => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_SHOW_FIXED_STATS    => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_PROFILER    => 1,

                    // Ad bot needs to be disabled for landing page to work, but better performance when not needed
                    DebugRequestInterface::PARAMETER_DEBUG_IS_AD_BOT           => 0,

                    // Prevent being included in split test (invalid values have no effect)
                    DebugRequestInterface::PARAMETER_DEBUG_SPLIT_TEST_VARIANT  => 'visual_browser_test',
                    DebugRequestInterface::PARAMETER_DEBUG_ENABLE_MODULE       => 1,
                ],
            ),
        );

        return new self(
            brandSlug                        : $brandSlug,
            name                             : $name,
            device                           : $device,
            url                              : $url,
            responsiveBreakpoints            : $responsiveBreakpoints,
            domain                           : $domain,
            query                            : $query,
            hasVisymoRelatedTerms            : $hasVisymoRelatedTerms,
            hasGoogleRelatedTerms            : $hasGoogleRelatedTerms,
            hasGoogleRelatedTermsWithFallback: $hasGoogleRelatedTermsWithFallback,
            hasGoogleAdsOnRelatedPage        : $hasGoogleAdsOnRelatedPage,
            hasGoogleAdManager               : $hasGoogleAdManager,
            expectsQueryInTitle              : $expectsQueryInTitle,
            createScreenshotFromBottomOfPage : $createScreenshotFromBottomOfPage,
        );
    }

    public function getTestRunner(): string
    {
        return DisplaySearchRelatedTestRunner::class;
    }
}
