<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase\Page;

use Tests\Frontend\Framework\Helper\ScreenshotHelper;
use Tests\Frontend\Framework\Helper\TestEnvironmentHelper;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;
use Tests\Frontend\TestCase\TestCaseInterface;
use Tests\Frontend\TestCase\TestRunnerInterface;

final readonly class PageTestRun<PERSON> implements TestRunnerInterface
{
    public function __construct(
        private TestEnvironmentHelper $testEnvironmentHelper,
        private ScreenshotHelper $screenshotHelper
    )
    {
    }

    public function run(TestCaseInterface $testCase, WebDriverInterface $webDriver): void
    {
        if (!$testCase instanceof PageTestCase) {
            throw new \RuntimeException(
                sprintf(
                    'This test runner only supports %s',
                    PageTestCase::class,
                ),
            );
        }

        // Wait for processing request
        $webDriver->getUrlAndWaitUntilDocumentReady(
            $this->testEnvironmentHelper->addDevelopToUrl($testCase->getUrl()),
        );

        // Take and compare screenshots
        $this->screenshotHelper->createAndCompareScreenshots($testCase, $webDriver);
    }
}
