<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase\Page;

use App\Debug\Request\DebugRequestInterface;
use App\Generic\Device\Device;
use App\Tracking\Request\SeaRequestInterface;
use Tests\Frontend\TestCase\AbstractTestCase;

final class PageTestCase extends AbstractTestCase
{
    /**
     * @param int[] $responsiveBreakpoints
     */
    public static function create(
        string $brandSlug,
        string $name,
        Device $device,
        string $url,
        array $responsiveBreakpoints = [],
        ?float $allowedScreenshotDifference = null,
        bool $createScreenshotFromBottomOfPage = false
    ): self
    {
        $url = sprintf(
            '%s%s%s',
            $url,
            str_contains($url, '?') ? '&' : '?',
            http_build_query(
                [
                    DebugRequestInterface::PARAMETER_DEBUG_DISABLE_PROFILER   => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_FORCE_MOCK_SEARCH  => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_SHOW_FIXED_STATS   => 1,
                    DebugRequestInterface::PARAMETER_DEBUG_SPLIT_TEST_VARIANT => 'visual_browser_test',
                    DebugRequestInterface::PARAMETER_DEBUG_ENABLE_MODULE      => 1,
                    SeaRequestInterface::PARAMETER_DEVICE                     => $device->getShortValue(),
                ],
            ),
        );

        return new self(
            brandSlug                       : $brandSlug,
            name                            : $name,
            device                          : $device,
            url                             : $url,
            responsiveBreakpoints           : $responsiveBreakpoints,
            allowedScreenshotDifference     : $allowedScreenshotDifference,
            createScreenshotFromBottomOfPage: $createScreenshotFromBottomOfPage,
        );
    }

    protected function getTestCaseSlug(): string
    {
        return 'page';
    }

    public function getTestRunner(): string
    {
        return PageTestRunner::class;
    }
}
