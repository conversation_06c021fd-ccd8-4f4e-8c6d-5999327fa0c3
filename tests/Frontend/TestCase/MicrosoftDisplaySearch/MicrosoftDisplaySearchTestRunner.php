<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase\MicrosoftDisplaySearch;

use App\RelatedTerms\Request\RelatedTermsRequestInterface;
use App\Tracking\Entry\Request\TrackingEntryRequestInterface;
use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use PHPUnit\Framework\TestCase;
use Tests\Frontend\Framework\Helper\BingAdsHelper;
use Tests\Frontend\Framework\Helper\GoogleAdsHelper;
use Tests\Frontend\Framework\Helper\ScreenshotHelper;
use Tests\Frontend\Framework\Helper\TestCaseAssertionHelper;
use Tests\Frontend\Framework\Helper\TestEnvironmentHelper;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;
use Tests\Frontend\TestCase\TestCaseInterface;
use Tests\Frontend\TestCase\TestRunnerInterface;

final readonly class MicrosoftDisplaySearchTestRunner implements TestRunnerInterface
{
    public function __construct(
        private TestEnvironmentHelper $testEnvironmentHelper,
        private ScreenshotHelper $screenshotHelper,
        private TestCaseAssertionHelper $testCaseAssertionHelper,
        private BingAdsHelper $bingAdsHelper,
        private GoogleAdsHelper $googleAdsHelper
    )
    {
    }

    public function run(TestCaseInterface $testCase, WebDriverInterface $webDriver): void
    {
        if (!$testCase instanceof MicrosoftDisplaySearchTestCase) {
            throw new \RuntimeException(
                sprintf(
                    'This test runner only supports %s',
                    MicrosoftDisplaySearchTestCase::class,
                ),
            );
        }

        // Wait for processing request
        $webDriver->getUrlAndWaitUntilDocumentReady(
            $this->testEnvironmentHelper->addDevelopToUrl($testCase->getUrl()),
        );

        // Check if query is included in page title
        if ($testCase->expectsQueryInTitle) {
            TestCase::assertStringContainsStringIgnoringCase(
                $testCase->query,
                $webDriver->getRemote()->getTitle(),
                'expecting query in webpage title',
            );
        } else {
            TestCase::assertStringNotContainsStringIgnoringCase(
                $testCase->query,
                $webDriver->getRemote()->getTitle(),
                'not expecting query in webpage title',
            );
        }

        try {
            // Wait for search results (after ads are shown with a timeout)
            $webDriver->waitUntilVisible(WebDriverBy::cssSelector('[id^="delayed-container-"]'));
        } catch (NoSuchElementException) {
            // delayed container is not always present
        }

        $this->assertAds($testCase, $webDriver);

        // Take and compare screenshots
        $screenshotsAreEqual = $this->screenshotHelper->createAndCompareScreenshots($testCase, $webDriver);

        if ($testCase->hasVisymoRelatedTerms && $testCase->testVisymoRelated) {
            $relatedTermsSearchLinks = $webDriver->getRemote()->findElements(
                WebDriverBy::cssSelector(
                    '.related-terms .related-terms__columns a.related-terms__link',
                ),
            );

            TestCase::assertNotEmpty(
                $relatedTermsSearchLinks,
                sprintf(
                    'Expect related terms links%s',
                    !$screenshotsAreEqual ? ': could be caused by screenshot difference upload' : '',
                ),
            );

            $relatedTermsSearchLink = current($relatedTermsSearchLinks);

            $relatedTermsSearchUrl = (string)$relatedTermsSearchLink->getAttribute('href');

            $this->testCaseAssertionHelper->assertNotEmptyUrlParameters(
                $relatedTermsSearchUrl,
                [
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_ZONE,
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_LINK_INDEX,
                    TrackingEntryRequestInterface::PARAMETER_SERIALIZED_TRACKING_ENTRY,
                ],
            );
        }
    }

    private function assertAds(
        MicrosoftDisplaySearchTestCase $testCase,
        WebDriverInterface $webDriver
    ): void
    {
        // Bing ads
        if ($testCase->hasBingAds) {
            $this->bingAdsHelper->waitUntilLoaded($webDriver, false);
            $this->bingAdsHelper->maskBingAds($webDriver, false);
        }

        $this->bingAdsHelper->expectBingAds($webDriver, $testCase->hasBingAds);

        // Google Ads
        if ($testCase->hasGoogleAds) {
            $this->googleAdsHelper->waitUntilLoaded($webDriver, false);
            $this->googleAdsHelper->maskGoogleAds($webDriver, false);
        }

        $this->googleAdsHelper->expectGoogleAds($webDriver, $testCase->hasGoogleAds);
    }
}
