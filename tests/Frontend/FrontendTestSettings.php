<?php

declare(strict_types=1);

namespace Tests\Frontend;

use Tests\Frontend\Framework\WebDriver\Browser\Browser;
use Tests\Frontend\Framework\WebDriver\LambdaTest\LambdaTestDesktopCapabilities;
use Tests\Frontend\Framework\WebDriver\LambdaTest\LambdaTestDeviceCapabilities;
use Tests\Frontend\Framework\WebDriver\WebDriverCapabilitiesInterface;
use Tests\Frontend\Framework\WebDriver\WebDriverResolution;

class FrontendTestSettings
{
    // Frontend-test specific ad style id
    public const int GOOGLE_AD_STYLE_ID = 6683017520;
    public const int BING_AD_STYLE_ID   = 1;

    // Upload expected screenshots, decreases performance somewhat on fail and sometimes crashes LambdaTest video because the images
    // are transported by data uri
    public const bool UPLOAD_EXPECTED_SCREENSHOTS = false;

    private const bool ENABLE_TESTS_DESKTOP           = true;
    private const bool ENABLE_TESTS_RESPONSIVE_DEVICE = true;

    public const bool ENABLE_EXTERNAL_SERVICE_MASKING = true;

    /**
     * @link https://www.lambdatest.com/capabilities-generator/ for possible capabilities. Select Appium for device emulator capabilities
     *
     * @return WebDriverCapabilitiesInterface[]
     */
    public static function getDefaultCapabilities(): array
    {
        $capabilities = [];

        if (self::ENABLE_TESTS_DESKTOP) {
            $capabilities[] = LambdaTestDesktopCapabilities::create(
                operatingSystem : LambdaTestDesktopCapabilities::OS_WINDOWS_11,
                browser         : Browser::CHROME,
                browserVersion  : LambdaTestDesktopCapabilities::BROWSER_VERSION_LATEST,
                resolution      : WebDriverResolution::RESOLUTION_2048X1536,
                windowResolution: WebDriverResolution::RESOLUTION_1980X1320,
            );

            $capabilities[] = LambdaTestDesktopCapabilities::create(
                operatingSystem : LambdaTestDesktopCapabilities::OS_WINDOWS_11,
                browser         : Browser::FIREFOX,
                browserVersion  : LambdaTestDesktopCapabilities::BROWSER_VERSION_LATEST,
                resolution      : WebDriverResolution::RESOLUTION_2048X1536,
                windowResolution: WebDriverResolution::RESOLUTION_1980X1320,
            );

            $capabilities[] = LambdaTestDesktopCapabilities::create(
                operatingSystem : LambdaTestDesktopCapabilities::OS_MAC_SEQUOIA,
                browser         : Browser::SAFARI,
                browserVersion  : LambdaTestDesktopCapabilities::BROWSER_VERSION_LATEST,
                resolution      : WebDriverResolution::RESOLUTION_2048X1536,
                windowResolution: WebDriverResolution::RESOLUTION_1980X1320,
            );
        }

        if (self::ENABLE_TESTS_RESPONSIVE_DEVICE) {
            // Chrome on Windows has a minimum of 500 pixel width
            $capabilities[] = LambdaTestDeviceCapabilities::createForResponsiveMobile(
                operatingSystem : LambdaTestDesktopCapabilities::OS_WINDOWS_11,
                browser         : Browser::CHROME,
                browserVersion  : LambdaTestDesktopCapabilities::BROWSER_VERSION_LATEST,
                windowResolution: WebDriverResolution::RESOLUTION_500X840,
            );
        }

        return $capabilities;
    }
}
