<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver\LambdaTest;

use App\Generic\Device\Device;
use Facebook\WebDriver\Exception\TimeoutException;
use Facebook\WebDriver\Exception\UnknownCommandException;
use Facebook\WebDriver\Remote\DriverCommand;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverElement;
use Facebook\WebDriver\WebDriverExpectedCondition;
use Tests\Frontend\Framework\WebDriver\WebDriverCapabilitiesInterface;
use Tests\Frontend\Framework\WebDriver\WebDriverException;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;

class LambdaTestWebDriver implements WebDriverInterface
{
    private const int WAIT_TIMEOUT          = 10;  // seconds
    private const int WAIT_TIMEOUT_FALLBACK = 5;   // seconds waiting when regular timeout is not supported
    private const int WAIT_INTERVAL         = 500; // milliseconds

    private bool $failed = false;

    public function __construct(
        private readonly RemoteWebDriver $remoteWebDriver,
        private readonly WebDriverCapabilitiesInterface $webDriverCapabilities,
        private readonly string $resourcesDir
    )
    {
    }

    public function getCapabilities(): WebDriverCapabilitiesInterface
    {
        return $this->webDriverCapabilities;
    }

    public function getRemote(): RemoteWebDriver
    {
        return $this->remoteWebDriver;
    }

    public function quit(): void
    {
        $this->getRemote()->quit();
    }

    /**
     * All desktop OS (not supported on mobile devices)
     * All browsers (Chrome, Safari, Firefox, IE, Edge) except Safari 10.1
     *
     * @see https://www.browserstack.com/docs/automate/selenium/change-browser-window-size
     */
    public function maximizeWindow(): void
    {
        // The Fullscreen window command is only supported in W3C mode and maximize only on desktop (with resolution)
        if ($this->webDriverCapabilities->getDevice() === Device::DESKTOP) {
            $this->getRemote()->manage()->window()->maximize();
        }
    }

    public function setWindowSize(int $width, int $height): void
    {
        $this->getRemote()->execute(
            DriverCommand::SET_WINDOW_SIZE,
            [
                'width'         => $width,
                'height'        => $height,
                'x'             => 0,
                'y'             => 0,
                ':windowHandle' => 'current',
            ],
        );
    }

    public function wait(int $timeoutInMillisecond): void
    {
        $timeoutInSecond = $timeoutInMillisecond / 1000;
        $end = microtime(true) + $timeoutInSecond;

        $timeoutInSecond = (int)ceil($timeoutInSecond);
        $this->waitUntil(
            $timeoutInSecond,
            $timeoutInMillisecond,
            static fn (): bool => microtime(true) > $end,
        );
    }

    public function getUrlAndWaitUntilDocumentReady(string $url): void
    {
        $this->getRemote()->get($url);

        $this->waitUntilDocumentReady($url);
        $this->checkNetworkError();
    }

    public function waitUntilDocumentReady(?string $url = null): void
    {
        $webDriver = $this->getRemote();

        if ($url !== null) {
            // Wait until the url matches the loaded url, because some devices have a startpage which will also
            // trigger the document.readyState check
            $this->waitUntil(
                self::WAIT_TIMEOUT,
                self::WAIT_INTERVAL,
                static function () use ($webDriver, $url): bool {
                    $currentUrl = $webDriver->getCurrentURL();

                    // compare without slashes, because optional path slashes are sometimes stripped by the browser
                    $currentUrl = str_replace('/', '', $currentUrl);

                    $compareUrl = str_replace('/', '', $url);

                    return $currentUrl === $compareUrl;
                },
            );
        }

        $this->waitUntil(
            self::WAIT_TIMEOUT,
            self::WAIT_INTERVAL,
            // Runs in the remote browser. Supposedly supported in IE 8+, but works in IE 7 also.
            static fn (): bool => (string)$webDriver->executeScript('return document.readyState;') === 'complete',
        );
    }

    /**
     * @throws WebDriverException
     */
    private function checkNetworkError(): void
    {
        $documentUrl = $this->getRemote()->executeScript('return document.location.href');

        if (!is_string($documentUrl)) {
            throw WebDriverException::create();
        }

        if (preg_match('~^(https|http)://~', $documentUrl) !== 1) {
            throw WebDriverException::createWithUrl($documentUrl);
        }
    }

    public function waitUntilPresent(WebDriverBy $element): ?WebDriverElement
    {
        try {
            return $this->getRemote()->wait(self::WAIT_TIMEOUT, self::WAIT_INTERVAL)->until(
                WebDriverExpectedCondition::presenceOfElementLocated($element),
            );
        } catch (TimeoutException) {
            // Wait throws a TimeoutException when done
        }

        return null;
    }

    public function waitUntilVisible(WebDriverBy $element): ?WebDriverElement
    {
        try {
            return $this->getRemote()->wait(self::WAIT_TIMEOUT, self::WAIT_INTERVAL)->until(
                WebDriverExpectedCondition::visibilityOfElementLocated($element),
            );
        } catch (UnknownCommandException) {
            // On some platforms (like OSX/Safari) element visibility is not supported, fallback to a static wait
            // It's not possible to return the element based on visible or not
            $this->wait(self::WAIT_TIMEOUT_FALLBACK);
        } catch (TimeoutException) {
            // Wait throws a TimeoutException when done
        }

        return null;
    }

    private function waitUntil(int $timeoutInSecond, int $intervalInMillisecond, callable $function): void
    {
        try {
            $this->getRemote()->wait($timeoutInSecond, $intervalInMillisecond)->until(
                $function,
            );
        } catch (TimeoutException) {
            // Wait throws a TimeoutException when done
        }
    }

    private function blurActiveElement(): void
    {
        $this->getRemote()->executeScript('!!document.activeElement ? document.activeElement.blur() : 0');
    }

    private function moveMouseToTopLeft(): void
    {
        $this->getRemote()->getMouse()->mouseMove(
            x_offset: 0,
            y_offset: 0,
        );
    }

    public function takeScreenshot(?string $saveAs = null): void
    {
        // Blur active elements like inputs or buttons
        $this->blurActiveElement();
        // Prevent hover styles from being applied by moving to top left of the document
        $this->moveMouseToTopLeft();

        $this->getRemote()->takeScreenshot($saveAs);
    }

    public function uploadScreenshot(string $relativeScreenshotFilePath): void
    {
        $screenshotFilePath = $this->resourcesDir.'/'.$relativeScreenshotFilePath;

        $imageData = (string)file_get_contents($screenshotFilePath);
        $dataUri = sprintf(
            'data:image/png;base64,%s',
            base64_encode($imageData),
        );

        $this->getRemote()->get($dataUri);

        $this->takeScreenshot();
    }

    public function markTestAsPassed(): void
    {
        if ($this->failed) {
            return;
        }

        // @link https://www.lambdatest.com/support/docs/lambda-hooks/
        $this->getRemote()->executeScript('lambda-status=passed');
    }

    public function markTestAsFailed(): void
    {
        $this->failed = true;

        // @link https://www.lambdatest.com/support/docs/lambda-hooks/
        $this->getRemote()->executeScript('lambda-status=failed');
        $this->annotate('Test failed', WebDriverInterface::ANNOTATE_TYPE_ERROR);
    }

    /**
     * @inheritDoc
     */
    public function log(array $content): void
    {
        // Console log value is only visible in LambdaTest if it is a string without newlines
        $logValue = json_encode($content, JSON_THROW_ON_ERROR);
        $logValue = str_replace("\n", ' ', $logValue);

        $this->getRemote()->executeScript(
            sprintf('window.console.info(\'%s\');', $logValue),
        );
    }

    public function annotate(string $message, string $type = WebDriverInterface::ANNOTATE_TYPE_INFO): void
    {
        $this->getRemote()->executeScript(
            sprintf(
                'lambda-action=%s',
                json_encode(
                    [
                        'reason' => $message,
                        'status' => $type,
                    ],
                    JSON_THROW_ON_ERROR,
                ),
            ),
        );
    }

    public function getResourcesDir(): string
    {
        return $this->resourcesDir;
    }
}
