<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver\LambdaTest;

use App\Generic\Device\Device;
use Tests\Frontend\Framework\WebDriver\Browser\Browser;
use Tests\Frontend\Framework\WebDriver\WebDriverResolution;

final class LambdaTestDesktopCapabilities extends AbstractLambdaTestCapabilities
{
    public const string OS_WINDOWS_11  = 'Windows 11';
    public const string OS_MAC_SEQUOIA = 'MacOS Sequoia';

    public const string BROWSER_VERSION_LATEST = 'latest';

    public static function create(
        string $operatingSystem,
        Browser $browser,
        string $browserVersion,
        WebDriverResolution $resolution,
        WebDriverResolution $windowResolution
    ): self
    {
        $label = implode(
            ' ',
            [
                Device::DESKTOP->value,
                $operatingSystem,
                $browser->value,
                $browserVersion,
                $resolution->value,
            ],
        );
        $capabilities = array_merge(
            self::DEFAULT_META_CAPABILITIES,
            [
                'browserName'    => $browser->value,
                'browserVersion' => $browserVersion,
                'platformName'   => $operatingSystem,
                'resolution'     => $resolution->value,
            ],
        );

        return new self(
            hubUrlTemplate  : self::LAMBDA_TEST_HUB_URL_TEMPLATE,
            label           : $label,
            browser         : $browser,
            device          : Device::DESKTOP,
            windowResolution: $windowResolution,
            capabilities    : $capabilities,
        );
    }
}
