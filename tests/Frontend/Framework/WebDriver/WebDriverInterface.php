<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverElement;

interface WebDriverInterface
{
    public const string ANNOTATE_TYPE_DEBUG   = 'debug';
    public const string ANNOTATE_TYPE_INFO    = 'info';
    public const string ANNOTATE_TYPE_WARNING = 'warn';
    public const string ANNOTATE_TYPE_ERROR   = 'error';
    public const string ANNOTATE_TYPE_FAILED  = 'failed';

    public function getCapabilities(): WebDriverCapabilitiesInterface;

    public function getRemote(): RemoteWebDriver;

    public function quit(): void;

    public function maximizeWindow(): void;

    public function setWindowSize(int $width, int $height): void;

    public function wait(int $timeoutInMillisecond): void;

    public function getUrlAndWaitUntilDocumentReady(string $url): void;

    public function waitUntilDocumentReady(?string $url = null): void;

    /**
     * Wait until element is present
     */
    public function waitUntilPresent(WebDriverBy $element): ?WebDriverElement;

    /**
     * Wait until element is present and has a dimension
     */
    public function waitUntilVisible(WebDriverBy $element): ?WebDriverElement;

    public function takeScreenshot(?string $saveAs = null): void;

    public function uploadScreenshot(string $relativeScreenshotFilePath): void;

    public function markTestAsPassed(): void;

    public function markTestAsFailed(): void;

    /**
     * @param mixed[] $content
     */
    public function log(array $content): void;

    public function annotate(string $message, string $type = self::ANNOTATE_TYPE_INFO): void;

    public function getResourcesDir(): string;
}
