<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver\PlatformType;

use App\Generic\Device\Device;

enum WebDriverPlatformType: string
{
    case DESKTOP = 'Desktop';
    case MOBILE  = 'Mobile';
    case TABLET  = 'Tablet';

    public function getDevice(): Device
    {
        return match ($this) {
            self::DESKTOP => Device::DESKTOP,
            self::MOBILE  => Device::MOBILE,
            self::TABLET  => Device::TABLET,
        };
    }
}
