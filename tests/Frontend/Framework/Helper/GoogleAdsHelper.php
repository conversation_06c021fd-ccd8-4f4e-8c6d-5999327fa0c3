<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Helper;

use App\Debug\Request\DebugRequestInterface;
use App\GoogleCsa\Container\GoogleCsaContainerAffixHelperInterface;
use App\Http\Url\DevelopHostHelper;
use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use PHPUnit\Framework\TestCase;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;
use Tests\Frontend\FrontendTestSettings;

class GoogleAdsHelper
{
    private const string GOOGLE_ADS_IFRAME_TOP_UNIT_ID    = 'master-1';
    private const string GOOGLE_ADS_IFRAME_BOTTOM_UNIT_ID = 'slave-1-1';

    private const string CONTAINER_ELEMENT_TOP_ID    = 'csa-top';
    private const string CONTAINER_ELEMENT_BOTTOM_ID = 'csa-bottom';

    public function __construct(
        private readonly DevelopHostHelper $developHostHelper,
        private readonly HtmlDocumentHelper $htmlDocumentHelper,
        private readonly GoogleCsaContainerAffixHelperInterface $googleCsaContainerAffixHelper
    )
    {
    }

    public function waitUntilLoaded(WebDriverInterface $webDriver, bool $hasBottomAds): void
    {
        // Wait for Google Ads to become present
        $this->waitUntilUnitIsPresent(
            $webDriver,
            $this->googleCsaContainerAffixHelper->getAffixedContainer(self::GOOGLE_ADS_IFRAME_TOP_UNIT_ID),
            $this->googleCsaContainerAffixHelper->getAffixedContainer(self::CONTAINER_ELEMENT_TOP_ID),
        );

        if ($hasBottomAds) {
            $this->waitUntilUnitIsPresent(
                $webDriver,
                $this->googleCsaContainerAffixHelper->getAffixedContainer(self::GOOGLE_ADS_IFRAME_BOTTOM_UNIT_ID),
                $this->googleCsaContainerAffixHelper->getAffixedContainer(self::CONTAINER_ELEMENT_BOTTOM_ID),
            );
        }
    }

    private function waitUntilUnitIsPresent(
        WebDriverInterface $webDriver,
        string $googleIframeUnitId,
        string $containerElementId
    ): void
    {
        $unitElement = $webDriver->waitUntilVisible(
            WebDriverBy::cssSelector(sprintf('#%s', $googleIframeUnitId)),
        );

        if ($unitElement !== null) {
            // Unit was found, no log needed
            return;
        }

        // Log content of container element for debugging
        try {
            // Find container element starting with the given ID,
            // Now the id can change depending on the environment, so we need to use a wildcard
            // for example, there is csa-top, csa-top-dark, csa-top-falcon
            $containerElement = $webDriver->getRemote()->findElement(
                WebDriverBy::cssSelector(sprintf('[id^="%s"]', $containerElementId)),
            );
        } catch (NoSuchElementException) {
            return;
        }

        $html = html_entity_decode($containerElement->getDomProperty('innerHTML'));

        // Strip name attribute value, it contains the complete Google Ads config which is not helpful for log
        $html = (string)preg_replace('~name=".+" id="~', 'id="', $html);

        $webDriver->log(
            [
                'message'               => 'Could not find visible Google Ads unit element',
                'google_iframe_unit_id' => $googleIframeUnitId,
                'container_element_id'  => $containerElementId,
                'html'                  => $html,
            ],
        );
    }

    public function expectGoogleAds(WebDriverInterface $webDriver, bool $googleAdsEnabled, ?string $query = null): void
    {
        // Assert that a Google Ads iframe is present
        // In tests where Google Ads is not expected the Google Ads iframe should not be found
        // Use findElements instead of a waitUntil function to prevent exceptions which can be seen as errors in LambdaTest
        $googleAdsIFrames = $webDriver->getRemote()->findElements(
            WebDriverBy::cssSelector(sprintf('iframe#%s', self::GOOGLE_ADS_IFRAME_TOP_UNIT_ID)),
        );

        if (!$googleAdsEnabled) {
            TestCase::assertEmpty($googleAdsIFrames, 'Google Ads are not expected');

            return;
        }

        TestCase::assertNotEmpty($googleAdsIFrames, 'Google Ads are expected');

        // Assert "adtest" setting value
        // Cannot determine exact settings in different environment, assume ad test on for develop and off for production
        $expectedAdTestValue = $this->developHostHelper->hasDevVmName() ? 'on' : 'off';
        $googleCsa = sprintf(
            '"query": "%s", "adtest": "%s"',
            $query ?? DebugRequestInterface::GOOGLE_TEST_AD_QUERY,
            $expectedAdTestValue,
        );
        TestCase::assertTrue(
            $this->htmlDocumentHelper->hasContentInHead($webDriver, $googleCsa),
            sprintf('Google Ads parameters do not match: %s', $googleCsa),
        );

        $this->removeEmptyAdContainerCssModifier($webDriver);
    }

    /**
     * Mask the display of Google Ads
     */
    public function maskGoogleAds(WebDriverInterface $webDriver, bool $hasBottomAds): void
    {
        if (!FrontendTestSettings::ENABLE_EXTERNAL_SERVICE_MASKING) {
            return;
        }

        // Mask top unit
        // Because too often, there are slight variations on the test ad. There's the wiki variation, but even when
        // masking only the wiki variations, there are still differences sometimes. Probably because Google periodically
        // runs split tests on their end.
        $this->htmlDocumentHelper->maskElementByPartialId(
            $webDriver,
            $this->googleCsaContainerAffixHelper->getAffixedContainer(self::CONTAINER_ELEMENT_TOP_ID),
            'Google Ads',
        );

        // Fully hide bottom unit
        // Because sometimes, the ad is not loaded at all, even for a GoogleTestAd query
        // So simply masking the result would also result in an unstable assertion
        if ($hasBottomAds) {
            $this->htmlDocumentHelper->hideElementById($webDriver, self::GOOGLE_ADS_IFRAME_BOTTOM_UNIT_ID);
        }
    }

    private function removeEmptyAdContainerCssModifier(WebDriverInterface $webDriver): void
    {
        $webDriver->getRemote()->executeScript(
            <<<JS
                document.querySelectorAll('.ga-results.csa--empty').forEach((element) =>
                    element.classList.remove('csa--empty')
                );
            JS,
        );
    }
}
