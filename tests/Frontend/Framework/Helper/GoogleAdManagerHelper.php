<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Helper;

use Facebook\WebDriver\WebDriverBy;
use PHPUnit\Framework\TestCase;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;
use Tests\Frontend\FrontendTestSettings;

readonly class GoogleAdManagerHelper
{
    public function __construct(
        private HtmlDocumentHelper $htmlDocumentHelper
    )
    {
    }

    public function expectGoogleAdManager(WebDriverInterface $webDriver, bool $googleAdManagerEnabled): void
    {
        // Assert that ad iframes are present
        // On browsers without Ads the Google AdManager iframe is not found, use findElements to prevent exception
        $googleAdManagerIFrames = $webDriver->getRemote()->findElements(
            WebDriverBy::cssSelector('.display-banner iframe'),
        );

        if (!$googleAdManagerEnabled) {
            TestCase::assertEmpty($googleAdManagerIFrames, 'Google AdManager ads are not expected');

            return;
        }

        TestCase::assertNotEmpty($googleAdManagerIFrames, 'Google AdManager Ads are expected');
    }

    public function hideAdManagerAds(WebDriverInterface $webDriver): void
    {
        $webDriver->getRemote()->executeScript(
            <<<JS
            if (document.querySelector('.display-banner') !== null) {
                document.querySelector('.display-banner').style = 'display: none;';
            }
            JS,
        );
    }

    /**
     * Mask the display of Google Ad Manager ads
     */
    public function maskAdManagerAds(WebDriverInterface $webDriver): void
    {
        if (!FrontendTestSettings::ENABLE_EXTERNAL_SERVICE_MASKING) {
            return;
        }

        // Mask all display ads
        // Because there is no way to force a specific display ad.
        $this->htmlDocumentHelper->maskAllElementsByCssSelector(
            $webDriver,
            '.display-banner iframe',
            'Google AdManager',
        );
    }
}
