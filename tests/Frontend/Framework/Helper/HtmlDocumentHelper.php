<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Helper;

use Tests\Frontend\Framework\WebDriver\WebDriverInterface;

class HtmlDocumentHelper
{
    public function hideElementById(
        WebDriverInterface $webDriver,
        string $targetElementId
    ): void
    {
        $script = <<<JS
            document.querySelector('#{$targetElementId}').style = 'display: none;';
        JS;

        $webDriver->getRemote()->executeScript($script);
    }

    public function maskElementById(
        WebDriverInterface $webDriver,
        string $targetElementId,
        string $maskLabel,
        bool $replaceTargetContent = false
    ): void
    {
        $this->maskAllElementsByCssSelector(
            $webDriver,
            sprintf('#%s', $targetElementId),
            $maskLabel,
            $replaceTargetContent,
        );
    }

    public function maskElementByPartialId(
        WebDriverInterface $webDriver,
        string $targetElementId,
        string $maskLabel,
        bool $replaceTargetContent = false
    ): void
    {
        $this->maskAllElementsByCssSelector(
            $webDriver,
            sprintf('[id^="%s"]', $targetElementId),
            $maskLabel,
            $replaceTargetContent,
        );
    }

    public function maskAllElementsByCssSelector(
        WebDriverInterface $webDriver,
        string $targetElementSelector,
        string $maskLabel,
        bool $replaceTargetContent = false
    ): void
    {
        $styles = [
            'align-items'     => 'center',
            'background'      => '#ffffff',
            'border'          => '0.1rem solid #000000',
            'color'           => '#000000',
            'display'         => 'flex',
            'font-size'       => '20px',
            'font-weight'     => 'bold',
            'height'          => '100px',
            'justify-content' => 'center',
            'position'        => 'relative',
            'text-align'      => 'center',
            'width'           => '100%',
        ];

        $placeholderCss = '';

        foreach ($styles as $key => $value) {
            $placeholderCss .= sprintf('%s:%s; ', $key, $value);
        }

        if ($replaceTargetContent) {
            // Replace target content with mask element
            $script = <<<JS
                try {
                    var targetElements = document.querySelectorAll('{$targetElementSelector}');
                    
                    targetElements.forEach((targetElement) => {
                        var maskingElement = document.createElement('div');
                        maskingElement.innerHTML = '{$maskLabel}';
                        maskingElement.style.cssText = '{$placeholderCss}';
                        
                        targetElement.innerHTML = '';
                        targetElement.insertAdjacentElement('afterbegin', maskingElement);
                    });
                } catch(error) {
                    window.console && window.console.error && window.console.error(error);
                }
            JS;
        } else {
            // 1. Add mask element before target element
            // 2. Hide target element
            $script = <<<JS
                try {
                    var targetElements = document.querySelectorAll('{$targetElementSelector}');
                    
                    targetElements.forEach((targetElement) => {
                        var maskingElement = document.createElement('div');
                        maskingElement.innerHTML = '{$maskLabel}';
                        maskingElement.style.cssText = '{$placeholderCss}';
                        
                        targetElement.parentElement.insertBefore(maskingElement, targetElement);
                        targetElement.style.display = 'none';
                    });
                } catch(error) {
                    window.console && window.console.error && window.console.error(error);
                }
            JS;
        }

        $webDriver->getRemote()->executeScript($script);
    }

    public function hasContentInHead(WebDriverInterface $webDriver, string $content): bool
    {
        $script = sprintf(
            'return document.head.innerHTML.indexOf(\'%s\');',
            str_replace('\'', '\\\'', $content),
        );

        // `indexOf` returns `-1` if it was not found
        return (int)$webDriver->getRemote()->executeScript($script) !== -1;
    }

    public function hasContentInBody(WebDriverInterface $webDriver, string $content): bool
    {
        $script = sprintf(
            'return document.body.innerHTML.indexOf(\'%s\');',
            str_replace('\'', '\\\'', $content),
        );

        // `indexOf` returns `-1` if it was not found
        return (int)$webDriver->getRemote()->executeScript($script) !== -1;
    }
}
