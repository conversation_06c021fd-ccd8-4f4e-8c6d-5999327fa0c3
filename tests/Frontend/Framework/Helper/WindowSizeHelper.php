<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Helper;

use Tests\Frontend\Framework\WebDriver\WebDriverInterface;

final class WindowSizeHelper
{
    /** @var array<string, array<string, int>> */
    private array $adjustedWindowSizes = [];

    public function setWindowSize(
        int $windowWidth,
        int $windowHeight,
        bool $adjustWindowToMatchWidth,
        bool $adjustWindowToMatchHeight,
        WebDriverInterface $webDriver
    ): void
    {
        $requestedResolution = $this->getRequestedResolutionKey($webDriver, $windowWidth);
        $hasPreviouslyResized = array_key_exists($requestedResolution, $this->adjustedWindowSizes);
        $adjustWindow = $adjustWindowToMatchWidth || $adjustWindowToMatchHeight;

        if ($hasPreviouslyResized && $adjustWindow) {
            return;
        }

        $webDriver->setWindowSize($windowWidth, $windowHeight);

        if (!$adjustWindow) {
            // No adjustment needed
            return;
        }

        $webDriver->waitUntilDocumentReady();

        // Validate viewport size
        $visualViewPort = $webDriver->getRemote()
            ->executeScript(
                <<<JAVASCRIPT
                return window.visualViewport;
            JAVASCRIPT,
            );

        $diffWidth = $adjustWindowToMatchWidth ? $windowWidth - (int)$visualViewPort['width'] : 0;
        $diffHeight = $adjustWindowToMatchHeight ? $windowHeight - (int)$visualViewPort['height'] : 0;

        if ($diffWidth === 0 && $diffHeight === 0) {
            // No difference
            return;
        }

        // Adjust window size to match requested viewport size
        $newWidth = $windowWidth + $diffWidth;
        $newHeight = $windowHeight + $diffHeight;

        $this->adjustedWindowSizes[$requestedResolution] = ['width' => $newWidth, 'height' => $newHeight];

        $webDriver->setWindowSize($newWidth, $newHeight);
    }

    public function getRequestedResolutionKey(WebDriverInterface $webDriver, int $windowWidth): string
    {
        $webDriverCapabilities = $webDriver->getCapabilities()->getCapabilities();

        $platformName = $webDriverCapabilities['platformName'];
        $browserName = $webDriverCapabilities['browserName'];
        $browserVersion = $webDriverCapabilities['browserVersion'];

        return sprintf(
            '%s %s %s: %d',
            $platformName,
            $browserName,
            $browserVersion,
            $windowWidth,
        );
    }
}
