- name: Set brand website config for unit testing
  ansible.builtin.set_fact:
    cicd_qa_phpunit_tests:
      - configuration: phpunit.xml.dist
- name: Find bundles
  ansible.builtin.find:
    path: '{{ ci_project_dir }}/bundles/'
    file_type: directory
    recurse: false
  register: cicd_bundles_find_fact
- name: Build configuration for unit testing
  ansible.builtin.set_fact:
    cicd_qa_phpunit_tests: '{{ cicd_qa_phpunit_tests + [{ "configuration": "bundles/" + cicd_bundle_name + "/phpunit.xml.dist" }] }}'
  loop: '{{ cicd_bundles_find_fact.files | json_query("[].path") | map("basename") }}'
  loop_control:
    loop_var: cicd_bundle_name
