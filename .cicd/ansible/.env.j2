# Symfony
APP_ENV={{ lookup('env', 'APP_ENV') }}
APP_SECRET={{ lookup('env', 'APP_SECRET') }}

TRUSTED_PROXIES={{ lookup('env', 'TRUSTED_PROXIES') }}

CONFIG_FILE_PATH={{ lookup('env', 'BRAND_WEBSITES_CONFIG_FILE_PATH') }}

{% if lookup('env', 'BRAND_WEBSITES_APP_LOG_DIR', default='') | length > 0 %}
APP_LOG_DIR={{ lookup('env', 'BRAND_WEBSITES_APP_LOG_DIR') }}
{% endif %}

PHP_FPM_SOCKET_PATH={{ lookup('env', 'BRAND_WEBSITES_PHP_FPM_SOCKET_PATH') }}
COMPOSITE_SEARCH_API_URL={{ lookup('env', 'BRAND_WEBSITES_COMPOSITE_SEARCH_API_URL') }}
AUTOSUGGEST_API_URL={{ lookup('env', 'BRAND_WEBSITES_AUTOSUGGEST_API_URL') }}

BING_AUTO_SUGGEST_API_URL={{ lookup('env', 'BRAND_WEBSITES_BING_AUTO_SUGGEST_API_URL') }}
BING_AUTO_SUGGEST_APP_ID={{ lookup('env', 'BRAND_WEBSITES_BING_AUTO_SUGGEST_APP_ID') }}

SENTRY_DSN={{ lookup('env', 'BRAND_WEBSITES_SENTRY_DSN') }}

LOGSTASH_MONOLOG_CONNECTION_STRING={{ lookup('env', 'BRAND_WEBSITES_LOGSTASH_MONOLOG_CONNECTION_STRING') }}
STATISTICS_MONOLOG_CONNECTION_STRING={{ lookup('env', 'BRAND_WEBSITES_STATISTICS_MONOLOG_CONNECTION_STRING') }}
JAVASCRIPT_ERROR_MONOLOG_CONNECTION_STRING={{ lookup('env', 'BRAND_WEBSITES_JAVASCRIPT_ERROR_MONOLOG_CONNECTION_STRING') }}
JAVASCRIPT_RELATED_TERMS_VIEW_MONOLOG_CONNECTION_STRING={{ lookup('env', 'BRAND_WEBSITES_JAVASCRIPT_RELATED_TERMS_VIEW_MONOLOG_CONNECTION_STRING') }}

GEO_IP2_COUNTRY={{ lookup('env', 'BRAND_WEBSITES_GEO_IP2_COUNTRY') }}

# LambdaTest Automation account
LAMBDA_TEST_USERNAME={{ lookup('env', 'BRAND_WEBSITES_LAMBDA_TEST_USERNAME') }}
LAMBDA_TEST_ACCESS_KEY={{ lookup('env', 'BRAND_WEBSITES_LAMBDA_TEST_ACCESS_KEY') }}

DEV_VM_NAME={{ lookup('env', 'BRAND_WEBSITES_DEV_VM_NAME') }}

# Redis
REDIS_DSN={{ lookup('env', 'BRAND_WEBSITES_REDIS_DSN') }}
REDIS_CLUSTER={{ lookup('env', 'BRAND_WEBSITES_REDIS_CLUSTER') }}

MAILER_DSN={{ lookup('env', 'BRAND_WEBSITES_MAILER_DSN') }}

# Google Cloud
GOOGLE_CLOUD_PROJECT_ID={{ lookup('env', 'BRAND_WEBSITES_GOOGLE_CLOUD_PROJECT_ID') }}
GOOGLE_CLOUD_API_KEY={{ lookup('env', 'BRAND_WEBSITES_GOOGLE_CLOUD_API_KEY') }}

GOOGLE_API_PROJECT_ID={{ lookup('env', 'BRAND_WEBSITES_GOOGLE_API_PROJECT_ID') }}
GOOGLE_API_CREDENTIALS_JSON='{{ lookup('env', 'BRAND_WEBSITES_GOOGLE_API_CREDENTIALS_JSON') }}'

ADD_PROJECT_TO_CONFIG_FILE_PATH={{ lookup('env', 'BRAND_WEBSITES_ADD_PROJECT_TO_CONFIG_FILE_PATH') }}

CLOUDFLARE_CDN_URL={{ lookup('env', 'BRAND_WEBSITES_CLOUDFLARE_CDN_URL') }}

### Artemis URL // Enkel benodigd voor develop
ARTEMIS_URL={{ lookup('env', 'BRAND_WEBSITES_ARTEMIS_URL') }}
