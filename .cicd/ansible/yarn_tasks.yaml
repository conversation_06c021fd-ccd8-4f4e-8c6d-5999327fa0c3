-   name: Upgrade NodeJS to v20.18.1
    ansible.builtin.yum:
      name: nodejs-20.18.1
      state: present
-   name: Install or update Yarn
    community.general.npm:
      name: 'yarn'
      global: true
      state: latest
-   name: Install or update Corepack
    community.general.npm:
      name: 'corepack'
      global: true
      state: latest
-   name: Enable Corepack
    ansible.builtin.command: corepack enable
-   name: Install project Yarn version
    ansible.builtin.command: corepack install
    args:
      chdir: '{{ ci_project_dir }}'
-   name: Configure Yarn
    changed_when: true
    ansible.builtin.shell: |
      yarn config set enableGlobalCache false
      yarn config set cacheFolder '{{ ci_project_dir }}/.yarn-cache'
      yarn config set cacheMigrationMode always
      
      yarn config set enableImmutableInstalls true
      
      yarn config set enableProgressBars false
      yarn config set enableColors false
      yarn config set enableHyperlinks false
      yarn config set enableInlineBuilds true
    args:
      chdir: '{{ ci_project_dir }}'
-   name: Install Yarn dependencies
    ansible.builtin.command: yarn install --immutable
    args:
      chdir: '{{ ci_project_dir }}'
    register: cicd_yarn_install_dependencies_result
-   name: Print Yarn dependencies installation output
    ansible.builtin.debug:
      msg: '{{ cicd_yarn_install_dependencies_result.stdout }}'

# production environment
-   name: Build assets for production
    when: app_env == 'prod'
    changed_when: true
    ansible.builtin.command: yarn production
    args:
      chdir: '{{ ci_project_dir }}'

# testing environment
-   name: Build assets for test and development
    when: app_env in ['dev', 'test']
    block:
      -   name: Build assets dev
          changed_when: true
          ansible.builtin.command: yarn dev
          args:
            chdir: '{{ ci_project_dir }}'
      -   name: Run yarn lint
          changed_when: false
          ansible.builtin.command: yarn lint
          args:
            chdir: '{{ ci_project_dir }}'
