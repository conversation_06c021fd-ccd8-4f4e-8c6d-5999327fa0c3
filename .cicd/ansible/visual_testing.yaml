- name: Run visual testing
  gather_facts: false
  hosts: localhost

  vars_files:
    - vars.yaml

  vars:
    cicd_composer_security_advisories: false
    cicd_composer_validate: false
    cicd_qa_phpunit_tests:
      - configuration: phpunit-frontend-test.xml.dist
        testsuite: frontend

  tasks:
    - name: Template .env and dump it
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/env
    - name: Install PHP dependencies using composer
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/composer
        tasks_from: install
    - name: Start filebeat
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/filebeat
    - name: Run visual browser test
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/quality_assurance
        tasks_from: phpunit
