{"checksum": "a43b9c8d752697dfe9f37ff5ff74eafb", "brand": {"name": "<PERSON><PERSON>", "partner_slug": null, "slug": "vinden", "conversion_pixel_url": null, "google_adsense": {"approval": true, "contract_type": "direct", "default_client": "vinden-web", "default_channel": "vinden_nl_seo"}, "bing_ads": {"approval": false, "default_ad_unit_id": null}, "active": true, "article": {"enabled": false}, "cheq": {"enabled": false}, "content_page": {"enabled": true, "collection": "default2", "author": {"slug": "editorial_team", "name": "Editorial Team"}, "use_brand_for_organic_results": false, "organic_result_route": null}, "content_page_home": {"enabled": false}, "content_search": {"enabled": false}, "display_search": {"enabled": false}, "display_search_related": {"enabled": false}, "google_publisher_tag": {"enabled": false}, "google_tag_manager": {"enabled": false}, "image_search": {"enabled": true}, "info_pages": {"link_to_external_about_page": false, "link_to_visymo_publishing": false, "page_type": "search"}, "javascript_related_terms": {"enabled": false}, "json_template": [], "microsoft_search": {"enabled": false}, "microsoft_search_related": {"enabled": false}, "monetization": [], "news_search": {"enabled": true}, "one_trust": {"enabled": false, "domain_script_id": "9f4c917a-30e5-4b2f-9473-32c1ba9bbe76"}, "pageview_conversion": {"enabled": false}, "search": {"enabled": true, "seo_enabled": true, "style_id_desktop": "**********", "style_id_mobile": "**********"}, "spam_click_detect": {"enabled": false}, "tracking": {"campaign_name_validation_enabled": true}, "web_search": {"enabled": true, "style_id_desktop": "**********", "style_id_mobile": "**********"}}, "domains": {"www.vinden.be": {"javascript_related_terms_enabled": false, "locales": [{"locale": "nl_BE", "is_default": true}], "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true}}, "www.vinden.nl": {"javascript_related_terms_enabled": false, "locales": [{"locale": "nl_NL", "is_default": true}], "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true}}}, "redirect_domains": [], "accounts": {"55287": {"name": "VND - Competitor - TEST", "service": "Google Ads", "campaigns_v2": [{"name": "S2S Seisim"}, {"name": "S2S Questionsanswered web"}, {"name": "S2S Questionsanswered dcl"}, {"name": "Amazon"}, {"name": "<PERSON>"}, {"name": "DuckDuckGo"}, {"name": "Favoes"}, {"name": "Yahoo"}, {"name": "be_gc2_03"}, {"name": "be_gc2_04"}], "payment_mode": null, "conversion_log": {"enabled": true, "offline_conversion": true}, "google_adsense": {"enabled": true, "sem_client": "vinden-smh", "web_client": "vinden-smh"}, "bing_ads": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": []}}, "split_tests": []}