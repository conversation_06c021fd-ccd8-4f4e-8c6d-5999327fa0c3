<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\ScreenshotDiff\Result;

use Visymo\Filesystem\File\Iterator\FileIteratorInterface;

readonly class ScreenshotDiffResultsFactory
{
    public function __construct(
        private FileIteratorInterface $expectedScreenshotFileIterator,
        private ScreenshotDiffResultFactory $screenshotDiffResultFactory
    )
    {
    }

    public function create(): ScreenshotDiffResults
    {
        $results = [];

        foreach ($this->expectedScreenshotFileIterator->iterate() as $expectedFile) {
            $screenshotDiffResult = $this->screenshotDiffResultFactory->createFromExpectedFile(
                $expectedFile,
            );

            if ($screenshotDiffResult->comparisonFile->exists()) {
                $results[] = $screenshotDiffResult;
            }
        }

        return new ScreenshotDiffResults(
            $results,
        );
    }
}
