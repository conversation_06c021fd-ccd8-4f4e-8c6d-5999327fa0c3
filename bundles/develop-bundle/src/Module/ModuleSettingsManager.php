<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Module;

use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\ModuleSettings\ModuleSettingsFactoryInterface;
use App\ModuleSettings\ModuleSettingsInterface;
use App\ModuleSettings\Value\ModuleValue;
use Symfony\Component\DependencyInjection\ContainerInterface;

final readonly class ModuleSettingsManager
{
    /** @var array<string, ModuleSettingsFactoryInterface> */
    public array $moduleSettingsFactories;

    /**
     * @param \Traversable<string, ModuleSettingsFactoryInterface> $moduleSettingsFactories
     */
    public function __construct(
        private ContainerInterface $container,
        iterable $moduleSettingsFactories
    )
    {
        $this->moduleSettingsFactories = iterator_to_array($moduleSettingsFactories);
    }

    /**
     * @return ModuleValue[]
     */
    public function getModuleValues(ModuleSettingsFactoryInterface $moduleSettingsFactory): array
    {
        $moduleName = $moduleSettingsFactory::getModuleName();
        $projectModuleConfig = $this->getProjectModuleConfig($moduleName);

        return $moduleSettingsFactory->getModuleValues($projectModuleConfig);
    }

    public function getModuleSettingsForModule(string $moduleName): ModuleSettingsInterface
    {
        $moduleSettingsFactory = $this->moduleSettingsFactories[$moduleName] ?? null;

        if ($moduleSettingsFactory === null) {
            throw new \RuntimeException(
                sprintf('Module settings factory for module "%s" not found', $moduleName),
            );
        }

        if ($moduleSettingsFactory instanceof ArtemisModuleSettingsFactoryInterface) {
            return $moduleSettingsFactory->create();
        }

        $projectModuleConfig = $this->getProjectModuleConfig($moduleName);

        return $moduleSettingsFactory->create($projectModuleConfig);
    }

    /**
     * @return mixed[]
     */
    private function getProjectModuleConfig(string $moduleName): array
    {
        /** @var mixed[] $projectModuleConfig */
        $projectModuleConfig = $this->container->getParameter(
            sprintf('brand_website.%s.config', $moduleName),
        );

        return $projectModuleConfig;
    }

    public function instanceOfArtemisModuleSettingsFactory(object $moduleSettingsFactory): bool
    {
        return is_a(
            $moduleSettingsFactory::class,
            ArtemisModuleSettingsFactoryInterface::class,
            true,
        );
    }
}
