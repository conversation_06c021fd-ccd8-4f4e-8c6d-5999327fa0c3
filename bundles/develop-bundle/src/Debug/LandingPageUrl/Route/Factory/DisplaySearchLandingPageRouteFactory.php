<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Route\Factory;

use App\Account\Service\AccountService;
use App\DisplaySearch\Settings\DisplaySearchSettings;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\TrafficSource;
use App\Tracking\Request\SeaRequestInterface;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Options\LandingPageUrlGeneratorOptions;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Route\LandingPageRoute;

readonly class DisplaySearchLandingPageRouteFactory implements LandingPageRouteFactoryInterface
{
    public function __construct(
        private DisplaySearchSettings $displaySearchSettings
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function create(LandingPageUrlGeneratorOptions $options): array
    {
        if (!$this->displaySearchSettings->enabled) {
            return [];
        }

        return [
            new LandingPageRoute(
                'Display Search LandingPage Microsoft',
                'route_display_search',
                null,
                AccountService::MICROSOFT_ADVERTISING,
                TrafficSource::MICROSOFT,
                [
                    SeaRequestInterface::PARAMETER_CAMPAIGN_NAME => uniqid('campaign-', false),
                    ClickIdSource::MICROSOFT_CLICK_ID->value     => uniqid('msclkid-', false),
                ],
            ),
        ];
    }
}
