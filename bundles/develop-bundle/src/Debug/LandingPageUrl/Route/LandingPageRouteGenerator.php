<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Route;

use Visymo\DevelopBundle\Debug\LandingPageUrl\Options\LandingPageUrlGeneratorOptions;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Route\Factory\LandingPageRouteFactoryInterface;

readonly class LandingPageRouteGenerator
{
    /**
     * @param iterable<LandingPageRouteFactoryInterface> $landingPageRouteFactories
     */
    public function __construct(private iterable $landingPageRouteFactories)
    {
    }

    /**
     * @return LandingPageRoute[]
     */
    public function generate(LandingPageUrlGeneratorOptions $options): array
    {
        $landingPageRoutes = [];

        foreach ($this->landingPageRouteFactories as $landingPageRouteFactory) {
            $landingPageRoutes = [
                ...$landingPageRoutes,
                ...$landingPageRouteFactory->create($options),
            ];
        }

        return $this->filterLandingPageRoutes($options, $landingPageRoutes);
    }

    /**
     * @param LandingPageRoute[] $landingPageRoutes
     *
     * @return LandingPageRoute[]
     */
    private function filterLandingPageRoutes(LandingPageUrlGeneratorOptions $options, array $landingPageRoutes): array
    {
        return array_filter(
            $landingPageRoutes,
            static function (LandingPageRoute $landingPageRoute) use ($options): bool {
                $accountService = $landingPageRoute->accountService;

                if ($landingPageRoute->redirectRoute === null && $options->redirectUrl) {
                    return false;
                }

                if ($options->trafficSource !== null && $options->trafficSource !== $landingPageRoute->trafficSource) {
                    return false;
                }

                if ($options->accountService !== null &&
                    $accountService !== null &&
                    $accountService !== $options->accountService
                ) {
                    return false;
                }

                if ($accountService === null && $options->offlineConversionTracking !== null) {
                    return false;
                }

                return true;
            },
        );
    }
}
