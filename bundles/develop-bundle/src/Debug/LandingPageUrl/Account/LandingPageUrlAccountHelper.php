<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Account;

use App\Account\Settings\AccountSettings;
use App\Account\Settings\AccountSettingsFactory;
use App\WebsiteSettings\Configuration\WebsiteConfigurationRepository;
use App\WebsiteSettings\Settings\ConversionLog\ConversionLogSettingsFactory;
use App\WebsiteSettings\Settings\Module\AdProviderSettingsFactoryInterface;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Account\Filter\LandingPageUrlAccountFilterInterface;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Options\LandingPageUrlGeneratorOptions;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Route\LandingPageRoute;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Url\LandingPageUrl;

readonly class LandingPageUrlAccountHelper
{
    /**
     * @param iterable<LandingPageUrlAccountFilterInterface> $accountFilters
     * @param iterable<AdProviderSettingsFactoryInterface>   $adProviderSettingsFactories
     */
    public function __construct(
        private iterable $accountFilters,
        private iterable $adProviderSettingsFactories,
        private AccountSettingsFactory $accountSettingsFactory,
        private ConversionLogSettingsFactory $conversionLogSettingsFactory,
        private WebsiteConfigurationRepository $websiteConfigurationRepository
    )
    {
    }

    /**
     * @param mixed[] $brandConfig
     * @param mixed[] $domainConfig
     * @param mixed[] $accountIds
     */
    public function getAccountSettings(
        LandingPageUrlGeneratorOptions $options,
        LandingPageRoute $landingPageRoute,
        LandingPageUrl $landingPageUrl,
        array $brandConfig,
        array $domainConfig,
        array $accountIds
    ): ?AccountSettings
    {
        $filteredAccountIds = $accountIds;

        foreach ($this->accountFilters as $accountFilter) {
            $filteredAccountIds = $accountFilter->filter(
                $brandConfig,
                $domainConfig,
                $filteredAccountIds,
                $landingPageRoute,
                $options,
            );
        }

        if ($filteredAccountIds === []) {
            // There are no accounts found, this might happen when the accounts are not defined
            // in Antelope yet, or when the current brand is a partner brand
            $landingPageUrl->addInfo(
                'Note',
                'No account found for this service. Account/campaign parameters are not added to links.',
            );

            return null;
        }

        $accountId = $this->selectRandomAccountId($filteredAccountIds);
        $accountConfig = $this->websiteConfigurationRepository->getForBrand($options->brand)->getAccountConfig($accountId);

        if ($accountConfig === null) {
            return null;
        }

        $this->addAccountInfoToLandingPageUrl(
            $landingPageUrl,
            $brandConfig,
            $domainConfig,
            $accountConfig,
        );

        return $this->accountSettingsFactory->create($accountConfig);
    }

    /**
     * @param mixed[]|null $accountConfig
     */
    public function selectRandomCampaignFromAccountSettings(
        ?AccountSettings $accountSettings,
        ?array $accountConfig
    ): ?string
    {
        if ($accountSettings === null || $accountConfig === null) {
            return null;
        }

        $campaigns = $accountConfig[AccountSettings::KEY_CAMPAIGNS] ?? [];

        if ($campaigns !== []) {
            return (string)array_rand($campaigns);
        }

        return null;
    }

    /**
     * @param mixed[] $brandConfig
     * @param mixed[] $domainConfig
     * @param mixed[] $accountConfig
     */
    private function addAccountInfoToLandingPageUrl(
        LandingPageUrl $landingPageUrl,
        array $brandConfig,
        array $domainConfig,
        array $accountConfig
    ): void
    {
        $conversionLogSettings = $this->conversionLogSettingsFactory->create(
            $brandConfig,
            $domainConfig,
            $accountConfig,
        );
        $landingPageUrl->addInfo(
            'Conversion tracking',
            $conversionLogSettings->isOfflineConversion() ? 'offline' : 'online',
        );

        $adProviders = $this->getAccountAdProviders(
            $brandConfig,
            $domainConfig,
            $accountConfig,
        );
        $adProvidersList = implode(', ', $adProviders);
        $landingPageUrl->addInfo(
            'Ad providers',
            $adProvidersList === '' ? '(none)' : $adProvidersList,
        );
    }

    /**
     * @param mixed[] $brandConfig
     * @param mixed[] $domainConfig
     * @param mixed[] $accountConfig
     *
     * @return string[]
     */
    private function getAccountAdProviders(
        array $brandConfig,
        array $domainConfig,
        array $accountConfig
    ): array
    {
        $adProviders = [];

        foreach ($this->adProviderSettingsFactories as $adProviderSettingsFactory) {
            $adProviderSettings = $adProviderSettingsFactory->create(
                $brandConfig,
                $domainConfig,
                $accountConfig,
            );

            if ($adProviderSettings->isEnabled()) {
                $adProviders[] = $adProviderSettingsFactory->getType();
            }
        }

        return $adProviders;
    }

    /**
     * @param int[] $accountIds
     */
    private function selectRandomAccountId(array $accountIds): int
    {
        if (count($accountIds) < 1) {
            throw new \RuntimeException('No accounts to select from');
        }

        return $accountIds[array_rand($accountIds)];
    }
}
