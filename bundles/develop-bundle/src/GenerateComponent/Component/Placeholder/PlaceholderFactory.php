<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\Placeholder;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class PlaceholderFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return PlaceholderComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new PlaceholderComponent(
            layout: PlaceholderLayout::from($options[LayoutInterface::KEY]),
        );
    }
}
