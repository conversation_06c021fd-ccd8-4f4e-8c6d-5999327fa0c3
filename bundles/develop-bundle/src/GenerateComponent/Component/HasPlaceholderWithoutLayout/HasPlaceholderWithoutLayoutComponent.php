<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\HasPlaceholderWithoutLayout;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionComponent;

final class HasPlaceholderWithoutLayoutComponent extends AbstractSearchApiConditionComponent
{
    public static function getType(): string
    {
        return 'has_placeholder_without_layout';
    }

    public function getRenderer(): string
    {
        return HasPlaceholderWithoutLayoutRenderer::class;
    }
}
