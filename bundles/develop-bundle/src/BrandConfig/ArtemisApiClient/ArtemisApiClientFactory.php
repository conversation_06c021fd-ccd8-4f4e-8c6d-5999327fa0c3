<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\BrandConfig\ArtemisApiClient;

use App\Http\Url\DevelopHostHelper;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestFactoryInterface;
use Psr\Http\Message\StreamFactoryInterface;
use Psr\Log\LoggerInterface;
use Visymo\ArtemisApiClient\ArtemisApiClient;
use Visymo\ArtemisApiClient\ArtemisApiClientInterface;
use Visymo\ArtemisApiClient\Http\Client\HttpClient;
use Visymo\ArtemisApiClient\Http\Client\HttpClientInterface;

final readonly class ArtemisApiClientFactory
{
    public function __construct(
        private ClientInterface $client,
        private RequestFactoryInterface $httpRequestFactory,
        private StreamFactoryInterface $httpStreamFactory,
        private DevelopHostHelper $developHostHelper,
        private LoggerInterface $logger,
        private string $apiUrl
    )
    {
    }

    public function createForProduction(): ArtemisApiClientInterface
    {
        return new ArtemisApiClient(
            httpClient: $this->createHttpClient($this->apiUrl),
            logger    : $this->logger,
        );
    }

    public function createForDevelop(): ArtemisApiClientInterface
    {
        $apiUrl = $this->developHostHelper->addDevelopToUrl($this->apiUrl);

        return new ArtemisApiClient(
            httpClient: $this->createHttpClient($apiUrl),
            logger    : $this->logger,
        );
    }

    private function createHttpClient(string $apiUrl): HttpClientInterface
    {
        return new HttpClient(
            client            : $this->client,
            httpRequestFactory: $this->httpRequestFactory,
            httpStreamFactory : $this->httpStreamFactory,
            apiUrl            : $apiUrl,
        );
    }
}
