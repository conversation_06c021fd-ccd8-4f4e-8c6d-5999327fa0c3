<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\SharedStyle\Console;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\DevelopBundle\SharedStyle\Generator\SharedStyleConfigGenerator;

#[AsCommand(
    name       : 'develop:config:create-shared-style',
    description: 'Generate shared style config file'
)]
final class GeneratedSharedStyleConsole extends Command
{
    public function __construct(
        private readonly SharedStyleConfigGenerator $sharedStyleConfigGenerator
    )
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->sharedStyleConfigGenerator->generate();

        return Command::SUCCESS;
    }
}
