<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\RobotsTxtDiff\Result;

use Visymo\Filesystem\File\Iterator\FileIteratorInterface;

class RobotsTxtResultRegistry
{
    /** @var array<string, int[]> */
    private array $versions = [];

    /** @var RobotsTxtResult[] */
    private array $results;

    /** @var mixed[] */
    private array $removeResultSuggestions = [];

    public function __construct(
        private readonly RobotsTxtResultFactory $robotsTxtResultFactory,
        private readonly FileIteratorInterface $sharedRobotsTxtFileIterator,
        private readonly FileIteratorInterface $robotsTxtFileIterator
    )
    {
    }

    /**
     * @return string[]
     */
    public function getTypes(): array
    {
        $this->init();

        return array_keys($this->versions);
    }

    /**
     * @return int[]
     */
    public function getVersionsByType(string $type): array
    {
        $this->init();

        return array_values($this->versions[$type]);
    }

    /**
     * @return string[]
     */
    public function getProjectsByTypeAndVersion(string $type, int $version): array
    {
        $this->init();

        $results = array_filter(
            $this->results,
            static fn (RobotsTxtResult $robotsTxtResult) => $robotsTxtResult->type === $type &&
                                                            $robotsTxtResult->version === $version,
        );

        return array_map(
            static fn (RobotsTxtResult $robotsTxtResult) => $robotsTxtResult->project,
            $results,
        );
    }

    public function getFirstResultByTypeAndVersion(?string $project, string $type, int $version): ?RobotsTxtResult
    {
        $this->init();

        $results = array_filter(
            $this->results,
            static function (RobotsTxtResult $robotsTxtResult) use ($project, $type, $version) {
                if ($project !== null && $robotsTxtResult->project !== $project) {
                    return false;
                }

                return $robotsTxtResult->type === $type &&
                       $robotsTxtResult->version === $version;
            },
        );

        return $results !== [] ? current($results) : null;
    }

    public function getFirstResult(): ?RobotsTxtResult
    {
        $this->init();

        return $this->results !== [] ? current($this->results) : null;
    }

    /**
     * @return RobotsTxtRemoveResultSuggestion[]
     */
    public function getRemoveResultSuggestions(string $project, string $type, int $version): array
    {
        if (isset($this->removeResultSuggestions[$project][$type][$version])) {
            return $this->removeResultSuggestions[$project][$type][$version];
        }

        $robotsTxtResult = $this->getFirstResultByTypeAndVersion($project, $type, $version);

        if ($robotsTxtResult === null) {
            return [];
        }

        foreach ($this->results as $result) {
            if ($robotsTxtResult->isOfSameTypeAndVersion($result)) {
                $this->removeResultSuggestions[$project][$type][$version][] = new RobotsTxtRemoveResultSuggestion(
                    $result,
                    $robotsTxtResult,
                );
            }
        }

        return $this->removeResultSuggestions[$project][$type][$version] ?? [];
    }

    public function hasRemoveResultSuggestions(string $project, string $type, int $version): bool
    {
        return $this->getRemoveResultSuggestions($project, $type, $version) !== [];
    }

    private function init(): void
    {
        if (isset($this->results)) {
            return;
        }

        $this->results = [];

        foreach ($this->sharedRobotsTxtFileIterator->iterate() as $file) {
            $type = $file->getFileName();
            $contentHash = hash('md5', $file->readContent());
            $version = $this->getVersion($type, $contentHash);

            $this->results[] = $this->robotsTxtResultFactory->create(
                $file,
                $type,
                $contentHash,
                $version,
            );
        }

        foreach ($this->robotsTxtFileIterator->iterate() as $file) {
            $type = $file->getFileName();
            $contentHash = hash('md5', $file->readContent());
            $version = $this->getVersion($type, $contentHash);

            $this->results[] = $this->robotsTxtResultFactory->create(
                $file,
                $type,
                $contentHash,
                $version,
            );
        }
    }

    private function getVersion(string $type, string $contentHash): int
    {
        $version = $this->versions[$type][$contentHash] ?? null;

        if ($version !== null) {
            return $version;
        }

        if (!isset($this->versions[$type])) {
            $this->versions[$type] = [];
        }

        $this->versions[$type][$contentHash] = count($this->versions[$type]) + 1;

        return $this->versions[$type][$contentHash];
    }
}
