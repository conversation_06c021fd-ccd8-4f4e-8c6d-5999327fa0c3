<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\RobotsTxtDiff\Request;

use App\Http\Request\RequestInterface;

interface RobotsTxtDiffRequestInterface extends RequestInterface
{
    public const string PARAMETER_PROJECT = 'project';
    public const string PARAMETER_TYPE    = 'type';
    public const string PARAMETER_VERSION = 'version';

    public function getProject(): ?string;

    public function getType(): ?string;

    public function getVersion(): ?int;
}
