<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Filter;

use Visymo\DevelopBundle\JsonTemplateDiff\Request\JsonTemplateDiffRequestInterface;

final readonly class JsonTemplateDiffFilterFactory
{
    public function __construct(
        private JsonTemplateDiffRequestInterface $jsonTemplateDiffRequest
    )
    {
    }

    public function create(): JsonTemplateDiffFilter
    {
        return new JsonTemplateDiffFilter(
            module                 : $this->jsonTemplateDiffRequest->getModule(),
            device                 : $this->jsonTemplateDiffRequest->getDevice(),
            query                  : $this->jsonTemplateDiffRequest->getQuery(),
            componentType          : $this->jsonTemplateDiffRequest->getComponentType(),
            componentLayout        : $this->jsonTemplateDiffRequest->getComponentLayout(),
            resolveComponentOptions: $this->jsonTemplateDiffRequest->getResolveComponentOptions(),
        );
    }
}
