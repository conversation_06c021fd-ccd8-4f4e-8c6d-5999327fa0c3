<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Result;

final class JsonTemplateModuleResult
{
    private const string KEY_OVERRIDES = 'overrides';
    private const string KEY_SHARED    = 'shared';

    /** @var array<string, array<JsonTemplateResult>> */
    private array $results = [];

    /** @var array<string, array<JsonTemplateRemoveResultSuggestion>> */
    private array $removeResultSuggestions = [];

    public function __construct(
        public readonly string $module
    )
    {
    }

    /**
     * @return array<string, array<JsonTemplateResult>>
     */
    public function getResults(): array
    {
        return $this->results;
    }

    public function addJsonTemplateResult(JsonTemplateResult $jsonTemplateResult): void
    {
        $this->results[$jsonTemplateResult->getName()][] = $jsonTemplateResult;
    }

    /**
     * @return JsonTemplateResult[]
     */
    public function getResultsByModuleContentVersionId(string $moduleContentVersionId): array
    {
        $results = [];

        foreach ($this->results as $jsonTemplateResults) {
            foreach ($jsonTemplateResults as $jsonTemplateResult) {
                if ($jsonTemplateResult->moduleContentVersionId === $moduleContentVersionId) {
                    $results[] = $jsonTemplateResult;
                }
            }
        }

        return $this->sortResults($results);
    }

    /**
     * @return JsonTemplateRemoveResultSuggestion[]
     */
    public function getRemoveResultSuggestions(string $moduleContentVersionId): array
    {
        if (array_key_exists($moduleContentVersionId, $this->removeResultSuggestions)) {
            return $this->removeResultSuggestions[$moduleContentVersionId];
        }

        $results = $this->getResultsByModuleContentVersionId($moduleContentVersionId);
        $suggestions = [];

        foreach ($results as $result) {
            $relatedResults = $this->getRelatedResults($result);

            if (isset($relatedResults[self::KEY_OVERRIDES]) &&
                $moduleContentVersionId === $relatedResults[self::KEY_OVERRIDES]->moduleContentVersionId &&
                $result->isJsonTemplateOverride()
            ) {
                $suggestions[] = new JsonTemplateRemoveResultSuggestion(
                    $result,
                    $relatedResults[self::KEY_OVERRIDES],
                );
            } elseif (isset($relatedResults[self::KEY_SHARED]) &&
                      $moduleContentVersionId === $relatedResults[self::KEY_SHARED]->moduleContentVersionId
            ) {
                $suggestions[] = new JsonTemplateRemoveResultSuggestion(
                    $result,
                    $relatedResults[self::KEY_SHARED],
                );
            }
        }

        $this->removeResultSuggestions[$moduleContentVersionId] = $suggestions;

        return $this->removeResultSuggestions[$moduleContentVersionId];
    }

    public function hasRemoveResultSuggestions(string $moduleContentVersionId): bool
    {
        return $this->getRemoveResultSuggestions($moduleContentVersionId) !== [];
    }

    public function getSharedResult(JsonTemplateResult $selectedResult): ?JsonTemplateResult
    {
        if ($selectedResult->isSharedTemplate()) {
            return $selectedResult;
        }

        foreach ($this->results as $jsonTemplateResults) {
            foreach ($jsonTemplateResults as $jsonTemplateResult) {
                if ($jsonTemplateResult->fileName !== $selectedResult->fileName) {
                    continue;
                }

                if ($jsonTemplateResult->isSharedTemplate()) {
                    return $jsonTemplateResult;
                }
            }
        }

        return null;
    }

    /**
     * @return array<string, JsonTemplateResult>
     */
    private function getRelatedResults(JsonTemplateResult $selectedResult): array
    {
        $results = [];

        if ($selectedResult->isSharedTemplate()) {
            return $results;
        }

        foreach ($this->results as $jsonTemplateResults) {
            foreach ($jsonTemplateResults as $jsonTemplateResult) {
                if ($selectedResult->fileName !== $jsonTemplateResult->fileName) {
                    continue;
                }

                if ($jsonTemplateResult->jsonTemplateOverrideGroup !== $selectedResult->jsonTemplateOverrideGroup &&
                    $jsonTemplateResult->isJsonTemplateOverride() &&
                    $jsonTemplateResult->equals($selectedResult)
                ) {
                    $results[self::KEY_OVERRIDES] = $jsonTemplateResult;

                    if (count($results) === 2) {
                        break 2;
                    }
                }

                if ($jsonTemplateResult->isSharedTemplate()) {
                    $results[self::KEY_SHARED] = $jsonTemplateResult;

                    if (count($results) === 2) {
                        break 2;
                    }
                }
            }
        }

        return $this->sortResults($results);
    }

    /**
     * @param array<array-key, JsonTemplateResult> $results
     *
     * @return array<array-key, JsonTemplateResult>
     */
    private function sortResults(array $results): array
    {
        uasort(
            $results,
            static fn (JsonTemplateResult $a, JsonTemplateResult $b): int => $b->getSortValue() <=> $a->getSortValue(),
        );

        return $results;
    }
}
