<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Result;

use Visymo\DevelopBundle\JsonTemplateDiff\Normalizer\JsonTemplateNormalized;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

final readonly class JsonTemplateResult
{
    public string $fileName;

    public string $filePath;

    public function __construct(
        public SerializedFileInterface $jsonTemplateFile,
        public JsonTemplateNormalized $jsonTemplateNormalized,
        private string $projectDir,
        public ?string $jsonTemplateOverrideGroup,
        public string $module,
        public string $moduleContentVersionId,
        public int $contentVersion
    )
    {
        $this->fileName = $jsonTemplateFile->getFileName();
        $this->filePath = $jsonTemplateFile->getRealFilePath();
    }

    public function getName(): string
    {
        if ($this->jsonTemplateNormalized->isGroupComponentModule()) {
            return 'Layout';
        }

        if ($this->jsonTemplateOverrideGroup !== null) {
            return sprintf(
                'Override group "%s"',
                $this->jsonTemplateOverrideGroup,
            );
        }

        return 'Shared';
    }

    public function getDisplayFilePath(): string
    {
        return str_replace($this->projectDir, '', $this->filePath);
    }

    public function isJsonTemplateOverride(): bool
    {
        return $this->jsonTemplateOverrideGroup !== null;
    }

    public function isSharedTemplate(): bool
    {
        return !$this->isJsonTemplateOverride();
    }

    /**
     * @return array<int, bool>
     */
    public function getSortValue(): array
    {
        return [
            $this->isJsonTemplateOverride(),
            $this->isSharedTemplate(),
        ];
    }

    public function equals(?self $jsonTemplateResult): bool
    {
        if ($jsonTemplateResult === null) {
            return false;
        }

        return $this->moduleContentVersionId === $jsonTemplateResult->moduleContentVersionId;
    }
}
