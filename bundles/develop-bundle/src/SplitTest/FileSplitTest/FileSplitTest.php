<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\SplitTest\FileSplitTest;

use Visymo\Filesystem\File\FileInterface;

final readonly class FileSplitTest
{
    public function __construct(
        private string $projectDir,
        public FileInterface $file,
        public int $lineNumber
    )
    {
    }

    public function getShortFilePath(): string
    {
        return str_replace($this->projectDir, '', $this->file->getFilePath());
    }
}
