<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\SplitTest\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Visymo\DevelopBundle\SplitTest\Helper\SplitTestVariantHelper;

class SplitTestMonitorController extends AbstractController
{
    private const string ANTELOPE_SPLIT_TEST_QUERY_URL = 'https://antelope.visymo.com/ab-experiments/?query=';

    public function __construct(
        private readonly SplitTestVariantHelper $splitTestVariantHelper
    )
    {
    }

    #[Route(path: '/dev/split-test-monitor', name: 'route_develop_split_test_monitor', methods: ['GET'])]
    public function index(): Response
    {
        return $this->render(
            '@develop/split_test_monitor/split_test_monitor.html.twig',
            [
                'antelope_split_test_query_url' => self::ANTELOPE_SPLIT_TEST_QUERY_URL,
                'split_tests'                   => $this->splitTestVariantHelper->collectVariants(),
            ],
        );
    }
}
