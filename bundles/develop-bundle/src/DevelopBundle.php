<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle;

use Symfony\Component\DependencyInjection\Extension\ExtensionInterface;
use Symfony\Component\HttpKernel\Bundle\Bundle;
use Visymo\DevelopBundle\DependencyInjection\DevelopExtension;

class DevelopBundle extends Bundle
{
    public function getContainerExtension(): ?ExtensionInterface
    {
        return new DevelopExtension();
    }
}
