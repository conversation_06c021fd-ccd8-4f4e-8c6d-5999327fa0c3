<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonSchema\Component;

use App\Http\Url\UrlValidationRouter;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\ComponentResolverLocator;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Visymo\DevelopBundle\JsonSchema\OptionsResolver\DebugComponentOptionsResolver;
use Visymo\DevelopBundle\JsonSchema\OptionsResolver\DebugComponentResolverDecorator;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;

final readonly class ComponentSchemaFactory
{
    public function __construct(
        private ComponentResolverLocator $componentResolverLocator,
        private UrlValidationRouter $urlValidationRouter,
        private ContainerInterface $container
    )
    {
    }

    public function create(string $componentType): ComponentSchema
    {
        $componentResolver = $this->componentResolverLocator->getResolver($componentType);
        $componentResolverClass = $componentResolver::class;

        $debugComponentOptionsResolver = new DebugComponentOptionsResolver(
            componentResolver       : $componentResolver,
            componentResolverLocator: $this->componentResolverLocator,
            urlValidationRouter     : $this->urlValidationRouter,
        );
        $debugComponentResolver = $this->getDebugComponentResolver(
            $debugComponentOptionsResolver,
            $componentResolverClass,
        );

        $debugComponentResolver->resolve(
            [],
            new DebugComponentResolverDecorator(
                $this->componentResolverLocator,
            ),
        );

        return new ComponentSchema(
            componentClass    : $componentResolver::getSupportedComponent(),
            requiredProperties: $debugComponentOptionsResolver->getRequiredProperties(),
            properties        : $debugComponentOptionsResolver->getProperties(),
        );
    }

    private function getDebugComponentResolver(
        DebugComponentOptionsResolver $debugComponentOptionsResolver,
        string $componentResolverClass
    ): ComponentResolverInterface
    {
        $constructorArguments = [];
        $reflection = new \ReflectionClass($componentResolverClass);

        foreach ($reflection->getConstructor()?->getParameters() ?? [] as $parameter) {
            if (!$parameter->getType() instanceof \ReflectionNamedType) {
                throw new \RuntimeException(
                    sprintf(
                        'Constructor parameter "%s" of component resolver "%s" has a type that is not supported',
                        $parameter->getName(),
                        $componentResolverClass,
                    ),
                );
            }

            $parameterClass = $parameter->getType()->getName();

            if (in_array($parameterClass, [OptionsResolverInterface::class, ComponentOptionsResolverInterface::class], true)) {
                $constructorArguments[] = $debugComponentOptionsResolver;

                continue;
            }

            $constructorArguments[] = $this->container->get($parameterClass);
        }

        /** @var ComponentResolverInterface $debugComponentResolver */
        $debugComponentResolver = new $componentResolverClass(...$constructorArguments);

        return $debugComponentResolver;
    }
}
