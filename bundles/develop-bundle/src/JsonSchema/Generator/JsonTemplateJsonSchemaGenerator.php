<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonSchema\Generator;

use App\Component\Generic\Container\ContainerComponent;
use App\Component\Generic\Container\ContainerResolver;
use App\Http\Url\UrlValidationRouter;
use App\JsonTemplate\Component\ComponentResolverLocator;
use App\JsonTemplate\Template\JsonTemplateResolver;
use App\PageHeadTags\Tags\PageHeadTagsHelper;
use Visymo\DevelopBundle\JsonSchema\Component\ComponentSchema;
use Visymo\DevelopBundle\JsonSchema\Component\ComponentSchemaFactory;
use Visymo\DevelopBundle\JsonSchema\OptionsResolver\DebugComponentOptionsResolver;
use Visymo\DevelopBundle\JsonSchema\OptionsResolver\DebugComponentResolver;
use Visymo\DevelopBundle\JsonSchema\OptionsResolver\DebugComponentResolverDecorator;

final class JsonTemplateJsonSchemaGenerator extends AbstractJsonSchemaGenerator
{
    public function __construct(
        ComponentResolverLocator $componentResolverLocator,
        ComponentSchemaFactory $componentSchemaFactory,
        private readonly PageHeadTagsHelper $pageHeadTagsHelper,
        private readonly UrlValidationRouter $urlValidationRouter
    )
    {
        parent::__construct($componentResolverLocator, $componentSchemaFactory);
    }

    /**
     * @return mixed[]
     */
    public function generate(): array
    {
        $componentSchemas = $this->getComponentSchemas();
        $componentsData = $this->getComponentsData($componentSchemas);
        [$componentsData, $propertyDefinitions] = $this->optimizeComponentPropertyDefinitions(
            $componentsData,
        );

        $debugComponentOptionsResolver = $this->getJsonTemplateDebugComponentOptionsResolver();

        return [
            '$id'                  => 'https://www.visymo.com/json_template.schema.json',
            '$schema'              => 'http://json-schema.org/draft-07/schema#',
            'additionalProperties' => false,
            'type'                 => 'object',
            'definitions'          => [
                'property'   => $propertyDefinitions,
                'component'  => [
                    'anyOf' => array_map(
                        static fn (ComponentSchema $componentSchema) => [
                            '$ref' => $componentSchema->getDefinitionReference(),
                        ],
                        $componentSchemas,
                    ),
                ],
                'components' => $componentsData,
            ],
            'required'             => $debugComponentOptionsResolver->getRequiredProperties(),
            'properties'           => [
                ...$debugComponentOptionsResolver->getProperties(),
                ...[
                    'components' => [
                        'additionalProperties' => false,
                        'default'              => [],
                        'items'                => [
                            '$ref' => '#/definitions/component',
                        ],
                        'type'                 => 'array',
                    ],
                ],
            ],
        ];
    }

    private function getJsonTemplateDebugComponentOptionsResolver(): DebugComponentOptionsResolver
    {
        $debugComponentOptionsResolver = new DebugComponentOptionsResolver(
            componentResolver       : new DebugComponentResolver(),
            componentResolverLocator: $this->componentResolverLocator,
            urlValidationRouter     : $this->urlValidationRouter,
        );

        /** @var ContainerResolver $containerResolver */
        $containerResolver = $this->componentResolverLocator->getResolver(
            ContainerComponent::getType(),
        );
        $componentResolver = new DebugComponentResolverDecorator(
            $this->componentResolverLocator,
        );

        $jsonTemplateResolver = new JsonTemplateResolver(
            optionsResolver   : $debugComponentOptionsResolver,
            containerResolver : $containerResolver,
            componentResolver : $componentResolver,
            pageHeadTagsHelper: $this->pageHeadTagsHelper,
        );
        $jsonTemplateResolver->resolve([]);

        return $debugComponentOptionsResolver;
    }
}
