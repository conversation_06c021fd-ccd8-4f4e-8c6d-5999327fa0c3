<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonSchema\Generator;

use App\JsonTemplate\Component\ComponentResolverLocator;
use Visymo\DevelopBundle\JsonSchema\Component\ComponentSchema;
use Visymo\DevelopBundle\JsonSchema\Component\ComponentSchemaFactory;

abstract class AbstractJsonSchemaGenerator
{
    public function __construct(
        protected readonly ComponentResolverLocator $componentResolverLocator,
        private readonly ComponentSchemaFactory $componentSchemaFactory
    )
    {
    }

    /**
     * @return ComponentSchema[]
     */
    protected function getComponentSchemas(): array
    {
        $componentTypes = [];

        foreach ($this->componentResolverLocator->getResolvers() as $componentResolver) {
            $componentClass = $componentResolver::getSupportedComponent();

            if (!$componentClass::isInternal()) {
                $componentTypes[] = $componentClass::getType();
            }
        }

        sort($componentTypes);

        $componentSchemas = [];

        foreach ($componentTypes as $componentType) {
            $componentSchemas[] = $this->componentSchemaFactory->create($componentType);
        }

        return $componentSchemas;
    }

    /**
     * @param ComponentSchema[] $componentSchemas
     *
     * @return mixed[]
     */
    protected function getComponentsData(array $componentSchemas): array
    {
        $componentsData = [];

        foreach ($componentSchemas as $componentSchema) {
            $componentsData[$componentSchema->getDefinitionName()] = $componentSchema->toArray();
        }

        return $componentsData;
    }

    /**
     * @param mixed[] $componentsData
     *
     * @return mixed[]
     */
    protected function optimizeComponentPropertyDefinitions(array $componentsData): array
    {
        $propertyDefinitionList = [];

        foreach ($componentsData as $componentDefinitionName => $componentData) {
            foreach ($componentData['properties'] ?? [] as $property => $propertyData) {
                $containsEnum = array_key_exists('enum', $propertyData);

                if (!$containsEnum) {
                    $containsEnum = array_key_exists('enum', $propertyData['items'] ?? []);
                }

                if (!$containsEnum) {
                    continue;
                }

                $hash = hash('md5', json_encode($propertyData, JSON_THROW_ON_ERROR));

                if (!array_key_exists($hash, $propertyDefinitionList)) {
                    $propertyDefinitionName = sprintf(
                        'property%u',
                        count($propertyDefinitionList) + 1,
                    );
                    $propertyDefinitionList[$hash] = [
                        'name' => $propertyDefinitionName,
                        'data' => $propertyData,
                    ];
                }

                $propertyDefinition = sprintf(
                    '#/definitions/property/%s',
                    $propertyDefinitionList[$hash]['name'],
                );

                // Replace component property with reference to definition
                $componentsData[$componentDefinitionName]['properties'][$property] = [
                    '$ref' => $propertyDefinition,
                ];
            }
        }

        $propertyDefinitions = [];

        foreach ($propertyDefinitionList as $propertyDefinition) {
            $propertyDefinitions[$propertyDefinition['name']] = $propertyDefinition['data'];
        }

        return [$componentsData, $propertyDefinitions];
    }
}
