<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonSchema\Helper;

final class JsonSchemaComponentHelper
{
    public static function getDefinitionReference(string $componentClass): string
    {
        return sprintf('#/definitions/components/%s', self::getDefinitionName($componentClass));
    }

    public static function getDefinitionName(string $componentClass): string
    {
        $componentClassParts = explode('\\', $componentClass);
        $componentName = array_pop($componentClassParts);

        return lcfirst($componentName);
    }
}
