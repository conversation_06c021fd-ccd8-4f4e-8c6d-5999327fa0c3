<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Tracking\Offline;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\Logging\ConversionVendor;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerPluginInterface;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerResult;

readonly class OfflineConversionTracker implements VisymoConversionTrackerPluginInterface
{
    public function __construct(
        private WebsiteSettingsHelper $websiteSettingsHelper
    )
    {
    }

    public function handle(Conversion $conversion, VisymoConversionTrackerResult $result): ?bool
    {
        $isOfflineConversion = $this->websiteSettingsHelper->getSettings()->getConversionLog()->isOfflineConversion();

        if ($conversion->conversionVendor === ConversionVendor::GOOGLE_AD_MANAGER) {
            $isOfflineConversion = false;
        }

        $result->setExtraLogData(
            [
                ConversionLogExtra::TRACKING_OFFLINE_CONVERSION => $isOfflineConversion,
            ],
        );

        return $isOfflineConversion ? $result->stopPropagation() : $result->continuePropagation();
    }
}
