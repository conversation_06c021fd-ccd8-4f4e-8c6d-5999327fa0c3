<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Tracking;

use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\Logging\ConversionTrackingResultInterface;

class VisymoConversionTrackerResult implements ConversionTrackingResultInterface
{
    private bool $isOnlineConversion = false;

    private ?string $conversionTrackingUrl = null;

    private ?bool $conversionTrackingResult = null;

    private ?bool $allowClientSideTrackingFallback = null;

    /** @var array<string, mixed> */
    private array $extraLogData = [];

    public function getClientSideTrackingUrl(): ?string
    {
        if ($this->conversionTrackingResult === false && $this->allowClientSideTrackingFallback === true) {
            return $this->conversionTrackingUrl;
        }

        return null;
    }

    /**
     * @inheritDoc
     */
    public function getExtraLogData(): array
    {
        return [
            ...$this->extraLogData,
            ConversionLogExtra::TRACKING_ONLINE_CONVERSION => $this->isOnlineConversion,
        ];
    }

    /**
     * @inheritDoc
     */
    public function setExtraLogData(array $logData): self
    {
        $this->extraLogData = [
            ...$this->extraLogData,
            ...$logData,
        ];

        return $this;
    }

    /**
     * Indicate that conversion tracking is not done and other trackers can be tried
     */
    public function continuePropagation(): ?bool
    {
        return null;
    }

    /**
     * Indicate that conversion tracking is not done and other trackers should not be called
     */
    public function stopPropagation(): ?bool
    {
        $this->isOnlineConversion = false;

        return false;
    }

    /**
     * Indicate that conversion tracking is done and other trackers should not be called
     */
    public function finishTracking(
        string $conversionTrackingUrl,
        bool $conversionTrackingResult,
        bool $allowClientSideTrackingFallback
    ): ?bool
    {
        $this->isOnlineConversion = true;
        $this->conversionTrackingUrl = $conversionTrackingUrl;
        $this->conversionTrackingResult = $conversionTrackingResult;
        $this->allowClientSideTrackingFallback = $allowClientSideTrackingFallback;

        return true;
    }
}
