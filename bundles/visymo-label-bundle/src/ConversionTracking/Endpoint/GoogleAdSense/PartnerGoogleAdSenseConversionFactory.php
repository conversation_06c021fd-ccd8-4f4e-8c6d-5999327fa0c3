<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdSense;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseRequestInterface;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\TrackingOrder\ClickCount\ClickCountRepository;
use App\ConversionTracking\TrackingOrder\TrackingOrderFactory;

readonly class PartnerGoogleAdSenseConversionFactory
{
    public function __construct(
        private GoogleAdSenseRequestInterface $googleAdSenseRequest,
        private TrackingOrderFactory $trackingOrderFactory,
        private ClickCountRepository $clickCountRepository
    )
    {
    }

    public function create(): Conversion
    {
        $adBlockNumber = $this->googleAdSenseRequest->getBlock();
        $adNumber = $this->googleAdSenseRequest->getAd();

        $trackingOrder = $this->trackingOrderFactory->onePerAdV2($adBlockNumber, $adNumber)
                         ?? $this->trackingOrderFactory->random();

        $clickCount = $this->clickCountRepository->increaseClickCount($trackingOrder);

        return new Conversion(
            trackingOrder: $trackingOrder,
            eventType    : ConversionEventType::CLICK_AD,
            extraLogData : [
                               ConversionLogExtra::AD_TYPE                  => 'google',
                               ConversionLogExtra::AD_UNIQUE_AD_CLICK_COUNT => $clickCount,
                           ],
        );
    }
}
