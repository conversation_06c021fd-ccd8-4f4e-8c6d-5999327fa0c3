<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\SubmitSearch;

use App\Brand\Settings\BrandSettingsHelper;
use App\ConversionTracking\Endpoint\EndpointResponse;
use App\ConversionTracking\Endpoint\EndpointResponseInterface;
use App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchHandlerInterface;
use App\ConversionTracking\Logging\ConversionLogger;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTracker;

readonly class SubmitSearchHandler implements SubmitSearchHandlerInterface
{
    public function __construct(
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private SubmitSearchConversionFactory $submitSearchConversionFactory,
        private VisymoConversionTracker $visymoConversionTracker,
        private ConversionLogger $conversionLogger,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function handle(): EndpointResponseInterface
    {
        if (!$this->isSupported()) {
            return new EndpointResponse();
        }

        $brandSettings = $this->brandSettingsHelper->getSettings();

        if ($brandSettings->getPartnerSlug() !== null) {
            return new EndpointResponse();
        }

        $conversion = $this->submitSearchConversionFactory->create();
        $conversionTrackingResult = $this->visymoConversionTracker->track($conversion);

        $this->conversionLogger->logConversion($conversion, $conversionTrackingResult);

        return new EndpointResponse();
    }

    private function isSupported(): bool
    {
        $trackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();

        // Not valid when tracking entry is empty
        if ($trackingEntry->isEmpty) {
            return false;
        }

        return true;
    }
}
