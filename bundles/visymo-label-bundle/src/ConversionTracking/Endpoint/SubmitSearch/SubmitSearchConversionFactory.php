<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\SubmitSearch;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Logging\ConversionVendor;
use App\ConversionTracking\TrackingOrder\TrackingOrderFactory;

readonly class SubmitSearchConversionFactory
{
    public function __construct(
        private TrackingOrderFactory $trackingOrderFactory
    )
    {
    }

    public function create(): Conversion
    {
        $trackingOrder = $this->trackingOrderFactory->random();

        return new Conversion(
            trackingOrder             : $trackingOrder,
            eventType                 : ConversionEventType::SUBMIT_SEARCH,
            conversionVendor          : ConversionVendor::VISYMO,
            supportsConversionTracking: false,
        );
    }
}
