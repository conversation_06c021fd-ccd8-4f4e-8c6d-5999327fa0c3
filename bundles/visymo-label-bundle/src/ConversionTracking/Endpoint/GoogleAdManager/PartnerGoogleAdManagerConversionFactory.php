<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdManager;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\TrackingOrder\TrackingOrderFactory;

readonly class PartnerGoogleAdManagerConversionFactory
{
    public function __construct(
        private TrackingOrderFactory $trackingOrderFactory
    )
    {
    }

    public function createDisplay(): Conversion
    {
        $trackingOrder = $this->trackingOrderFactory->fromWebsiteSettings()
                         ?? $this->trackingOrderFactory->random();

        return new Conversion(
            trackingOrder: $trackingOrder,
            eventType    : ConversionEventType::DISPLAY_AD,
            extraLogData : [
                               ConversionLogExtra::AD_TYPE => 'google_ad_manager',
                           ],
        );
    }
}
