<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdManager;

use App\Brand\Settings\BrandSettingsHelper;
use App\ConversionTracking\Endpoint\EndpointResponse;
use App\ConversionTracking\Endpoint\EndpointResponseInterface;
use App\ConversionTracking\Endpoint\GoogleAdManager\GoogleAdManagerDisplayHandlerInterface;
use App\ConversionTracking\Logging\ConversionLogger;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTracker;

readonly class GoogleAdManagerDisplayHandler implements GoogleAdManagerDisplayHandlerInterface
{
    public function __construct(
        private GoogleAdManagerConversionFactory $googleAdManagerConversionFactory,
        private VisymoConversionTracker $visymoConversionTracker,
        private ConversionLogger $conversionLogger,
        private PartnerGoogleAdManagerConversionFactory $partnerGoogleAdManagerConversionFactory,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function handle(): EndpointResponseInterface
    {
        $brandSettings = $this->brandSettingsHelper->getSettings();

        if ($brandSettings->getPartnerSlug() !== null) {
            $conversion = $this->partnerGoogleAdManagerConversionFactory->createDisplay();
            $this->conversionLogger->logConversion($conversion);

            return new EndpointResponse();
        }

        $conversion = $this->googleAdManagerConversionFactory->createDisplay();
        $conversionTrackingResult = $this->visymoConversionTracker->track($conversion);

        $this->conversionLogger->logConversion($conversion, $conversionTrackingResult);

        return new EndpointResponse();
    }
}
