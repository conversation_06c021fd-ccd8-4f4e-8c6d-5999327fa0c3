<?php

declare(strict_types=1);

namespace Visymo\PrototypeBundle;

use Symfony\Component\DependencyInjection\Extension\ExtensionInterface;
use Symfony\Component\HttpKernel\Bundle\Bundle;
use Visymo\PrototypeBundle\DependencyInjection\PrototypeExtension;

class PrototypeBundle extends Bundle
{
    public function getContainerExtension(): ?ExtensionInterface
    {
        return new PrototypeExtension();
    }
}
