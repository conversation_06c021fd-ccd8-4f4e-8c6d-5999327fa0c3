import gulp from 'gulp';
import clean from 'gulp-clean';
import {mergedStreams} from '../helpers.js';
import fs from 'node:fs';
import { createEnhancedTaskFn } from '../enhancedTasks.js';

export default function cleanFolder(folder, removeOnExit = false) {
    return createEnhancedTaskFn(function (callback) {
        const events = [];

        events.push(
            gulp.src(folder, {read: false, allowEmpty: true})
                .pipe(clean())
        );

        if (removeOnExit) {
            process.on('beforeExit', function () {
                // After all tasks have finished clean up the ./tmp directory to prevent race conditions
                fs.rmSync(folder, {recursive: true, force: true});
            });
        }

        return mergedStreams(...events, callback);
    }, `clean ${folder} folder`);
}
