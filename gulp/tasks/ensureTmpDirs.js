import { mkdirSafe } from '../helpers.js';
import { buildLocations } from '../buildConfiguration.js';
import { createEnhancedTaskFn } from '../enhancedTasks.js';

/**
 * Ensures that all temporary directories exist before tasks run
 * This helps prevent race conditions where tasks might fail if directories don't exist
 */
export default function ensureTmpDirs() {
    return createEnhancedTaskFn(function (callback) {
        mkdirSafe(`${buildLocations.css.build}/Components`);
        mkdirSafe(`${buildLocations.js.build}/Components`);

        callback();
    },'ensureTmpDirs');
};
