parameters:
    level: 8
    paths:
        - src
        - tests
        - bundles/develop-bundle/src
        - bundles/develop-bundle/tests
        - bundles/prototype-bundle/src
        - bundles/prototype-bundle/tests
        - bundles/visymo-label-bundle/src
        - bundles/visymo-label-bundle/tests
    bootstrapFiles:
        - visymo-phpstan-bootstrap.php
    symfony:
        consoleApplicationLoader: visymo-phpstan-console.php
    reportUnmatchedIgnoredErrors: false
    dynamicConstantNames:
        - Tests\Frontend\FrontendTestSettings::UPLOAD_EXPECTED_SCREENSHOTS
        - Tests\Frontend\FrontendTestSettings::ENABLE_TESTS_DESKTOP
        - Tests\Frontend\FrontendTestSettings::ENABLE_TESTS_RESPONSIVE_DEVICE
        - Tests\Frontend\FrontendTestSettings::ENABLE_EXTERNAL_SERVICE_MASKING
    ignoreErrors:
        - identifier: missingType.generics
        # Prevent deprecated classes from blocking commits
        - '#deprecated#'
        # This is triggered by "@psalm-param class-string", which we don't use, but some packages do
        - '#expects class-string#'
includes:
    - vendor/phpstan/phpstan-deprecation-rules/rules.neon
    - vendor/phpstan/phpstan-phpunit/extension.neon
    - vendor/phpstan/phpstan-phpunit/rules.neon
    - vendor/phpstan/phpstan-strict-rules/rules.neon
    - vendor/phpstan/phpstan-symfony/extension.neon
    - vendor/phpstan/phpstan-symfony/rules.neon
