@import "defaultVariables";
@import "defaultFontVariables";
@import "Component/component";
@import "Fonts/visymoFontFace";
@import "Fonts/droidArabicKufiFontFace";

// stylelint-disable visymo/require-bem-block-define
* {
    &,
    &::before,
    &::after {
        box-sizing: border-box;
        font-family: var(--font-family, #{$font-family-sans-serif});
        margin: 0;
        padding: 0;
        vertical-align: top;
    }
}

html {
    background: #ffffff;
    font-size: 62.5%;
    text-align: left;

    // Prevent webkit browsers to scale up font size to readable size
    // As example this happens in Safari on mobile in landscape mode
    -webkit-text-size-adjust: none;
}

body {
    font-size: 1.5rem;
    -webkit-font-smoothing: antialiased;
    font-weight: 400;
    line-height: 1.5rem;
    min-width: 32rem;
}

html,
body {
    height: 100%;
    width: 100%;
}

img {
    border: 0;
    display: block;
    font-size: 0.1rem;
    overflow: hidden;
}

input,
button {
    // Turns off iOS and Android default roundings.
    // http://stackoverflow.com/questions/2918707/turn-off-iphone-safari-input-element-rounding
    -webkit-appearance: none;
    background-image: none;
    border-radius: 0;
}

input::-ms-clear {
    display: none;
}

button {
    -webkit-appearance: none;
    border: 0;
    border-collapse: separate;
    cursor: pointer;
    outline: 0;

    &::-moz-focus-inner {
        border: 0;
        padding: 0;
    }
}

ul {
    list-style: none;
}

div {
    display: block;
}

a {
    text-decoration: none;
}

// Visymo icon font
.vsi {
    &::after,
    &::before {
        display: block;
        font-family: $custom-font-family-visymo;
        -webkit-font-smoothing: antialiased;
        font-weight: 400;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: auto;
    }
}

// Clear, solution using float elements for support of old browsers not supporting display: inline-block.
.clear {
    clear: both;
}

.logo--filter {
    &-color-white {
        filter: invert(50%) brightness(100);
    }
}

// stylelint-disable selector-class-pattern
html.variant-visual_browser_test {
    /* Hide scrollbar for Chrome, Safari and Opera */
    &::-webkit-scrollbar {
        display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    & {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
}
