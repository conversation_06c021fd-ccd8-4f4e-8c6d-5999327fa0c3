// @see App\JavaScriptRelatedTerms\Helper\JavaScriptRelatedTermsContentHelper
var _vrtQuery = '';
var _vrtViewLogUrl = '';
var _vrtIgnoredPageParams = null;
var _vrtPageOptions = {};
var _vrtUnitOptions = {};
var _vrtGoogleLoaded = false;

function _vrtCreateUnitOptions(optionArg) {
    // Clone options
    var unitOptions = JSON.parse(JSON.stringify(_vrtUnitOptions));

    unitOptions.adLoadedCallback = optionArg.loadedCallback || null;
    unitOptions.columns = optionArg.columns || unitOptions.columns;
    unitOptions.container = optionArg.container;
    unitOptions.relatedSearches = optionArg.amount || unitOptions.relatedSearches;
    unitOptions.width = optionArg.width || null;

    return unitOptions;
}

function _vrtGoogle(options) {
    if (!_vrtGoogleLoaded) {
        _vrtGoogleLoaded = true;

        var script = document.createElement('script');

        script.type = 'text/javascript';
        script.async = 1;
        script.src = 'https://www.google.com/adsense/search/ads.js';

        document.head.appendChild(script);

        (function (g, o) {
            (function(g,o){g[o]=g[o]||function(){(g[o]['q']=g[o]['q']||[]).push(
                arguments)},g[o]['t']=1*new Date})(window,'_googCsa');
        })(window, '_googCsa');
    }

    // Unpack options
    _googCsa.apply(null, options);
}

function _vrtLogView() {
    (new Image).src = _vrtViewLogUrl
        + (_vrtViewLogUrl.indexOf('?') === -1 ? '?' : '&') + 'q=' + encodeURIComponent(_vrtQuery)
        + '&url=' + encodeURIComponent(top.location.href);
}

function _vrtHandleRequest(unitOptions) {
    var unitOptionsLength = unitOptions.length;

    if (unitOptionsLength === 0) {
        console.error('No related terms containers given.');

        return;
    }

    if (unitOptionsLength > 2) {
        console.error('Maximum of 2 related terms containers are allowed.');

        return;
    }

    var csaOptions = ['relatedsearch', _vrtPageOptions];

    for (var i = 0; i < unitOptionsLength; i++) {
        if (!unitOptions.hasOwnProperty(i)) {
            continue;
        }

        csaOptions.push(_vrtCreateUnitOptions(unitOptions[i]));
    }

    _vrtGoogle(csaOptions);
    _vrtLogView();
}

function _vrtGetIgnoredPageParams(allowPageParams) {
    var urlParameters = window.top.location.search.substring(1).split('&');

    if (urlParameters === []) {
        return null;
    }

    var ignoredPageParams = [];

    for (var i = 0; i < urlParameters.length; ++i) {
        var parameter = urlParameters[i].split('=', 2)[0];

        if (allowPageParams.indexOf(parameter) === -1) {
            ignoredPageParams.push(parameter);
        }
    }

    return ignoredPageParams.join(',');
}

// Function is executed after variable override
// @see App\JavaScriptRelatedTerms\Helper\JavaScriptRelatedTermsContentHelper
function _vrtRun() {
    var queue = window._vrt && typeof window._vrt.q === 'object' ? window._vrt.q : [];

    // Take over `_vrt` function
    window._vrt = function () {
        _vrtHandleRequest(arguments);
    };

    for (var i = 0, l = queue.length; i < l; i++) {
        // Unpack options
        window._vrt.apply(null, queue[i]);
    }
}
