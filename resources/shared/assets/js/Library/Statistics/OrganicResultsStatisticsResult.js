function OrganicResultsStatisticsResult() {
    StatisticsResult.call(
        this,
        'or',
        {
            // organic_results_amount
            a: null,
        }
    );
}

OrganicResultsStatisticsResult.prototype = Object.create(StatisticsResult.prototype);
OrganicResultsStatisticsResult.prototype.constructor = OrganicResultsStatisticsResult;

OrganicResultsStatisticsResult.prototype.setOrganicResultsAmount = function setOrganicResultsAmount(value) {
    this.setValueOnce('a', value);

    this.sendStatistics();
};
