function DelayedContainerStatisticsResult() {
    StatisticsResult.call(
        this,
        'dc',
        {
            // first_shown
            fs: null,
        }
    );
}

DelayedContainerStatisticsResult.prototype = Object.create(StatisticsResult.prototype);
DelayedContainerStatisticsResult.prototype.constructor = DelayedContainerStatisticsResult;

DelayedContainerStatisticsResult.prototype.setFirstShown = function setFirstShown(value) {
    this.setValueOnce('fs', value);

    this.sendStatistics();
};
