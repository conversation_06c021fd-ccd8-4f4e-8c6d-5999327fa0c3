function GoogleGptDisplayConversionLogger(baseUrl, additionalParams) {
    this._baseUrl = baseUrl;
    this._additionalParams = additionalParams;
    this._googleGpt = new GoogleGpt();
    this._googleGpt.on('slotOnload', this.handleSlotOnload.bind(this));
}

/**
 * @listens GoogleGpt#slotOnload
 *
 * @param {GoogleGpt} googleGpt
 * @param {GoogleGptSlot} slot
 */
GoogleGptDisplayConversionLogger.prototype.handleSlotOnload = function handleSlotOnload(googleGpt, slot) {
    if (!slot.hasImpression()) {
        return;
    }

    var querySymbol = this._baseUrl.includes('?') ? '&' : '?';

    var url = this._baseUrl + querySymbol + 'aup=' + encodeURIComponent(slot.getAdUnitPath());

    if (this._additionalParams !== null) {
        url += '&' + this._additionalParams;
    }

    if (navigator.sendBeacon) {
        navigator.sendBeacon(url);
    } else {
        (new Image).src = url;
    }
};
