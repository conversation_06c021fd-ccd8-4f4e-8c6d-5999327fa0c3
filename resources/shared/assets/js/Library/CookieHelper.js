var CookieHelper = {
    cookieEnabled: function () {
        return navigator.cookieEnabled;
    },
    getCookie: function (name) {
        if (!this.cookieEnabled()) {
            return null;
        }

        var arg = name + '=';
        var index = 0;

        while (index < document.cookie.length) {
            var pos = index + arg.length;

            if (document.cookie.substring(index, pos) === arg) {
                var endPos = document.cookie.indexOf(';', pos);

                if (endPos === -1) {
                    endPos = document.cookie.length;
                }

                return decodeURI(document.cookie.substring(pos, endPos));
            }

            index = document.cookie.indexOf(' ', index) + 1;

            if (index === 0) {
                break;
            }
        }

        return null;
    },
    setCookie: function (name, value) {
        if (!this.cookieEnabled()) {
            return;
        }

        if (value === null) {
            this.deleteCookie(name);
        } else {
            var today = new Date();
            var expires = new Date(today.getTime() + 31536000000);

            document.cookie = name + '=' + encodeURI(value) + ';expires=' + expires.toUTCString() + ';path=/';
        }
    },
    deleteCookie: function (name) {
        if (!this.cookieEnabled()) {
            return;
        }

        document.cookie = name + '=;Max-Age=-99999999;path=/';
    }
};
