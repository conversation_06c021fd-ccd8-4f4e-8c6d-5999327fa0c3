{"options": {"keyword_highlight": "forced_false", "organic_keyword_highlight": "forced_false", "page_head_tags_type": "display_search_related"}, "container": {"layout": "dsr", "mode": "dark"}, "components": [{"type": "brand_logo", "link_to_home": true, "logo_dark_mode": true}, {"type": "columns", "one": [{"type": "search_bar", "layout": "dsrw", "show_search_query": false}, {"type": "related_source_matches_visymo", "yes": [{"type": "bing_ads_top_ad_unit", "amount": 4, "ad_style_id": 5}], "no": [{"type": "google_ads_top_unit", "amount": 4, "container_suffix": "dark"}]}, {"type": "organic_results_title", "layout": "dark"}, {"type": "organic_results_with_fallback", "results": {"type": "organic_content_page_results", "layout": "dark", "amount": 4}, "fallback": {"type": "organic_results", "layout": "dark", "amount": 4}}, {"type": "organic_error_message"}]}, {"type": "footer", "components": [{"type": "footer_logo", "logo_dark_mode": true}, {"type": "footer_navigation"}]}]}