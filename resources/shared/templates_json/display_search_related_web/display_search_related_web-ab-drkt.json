{"options": {"keyword_highlight": "forced_true", "organic_keyword_highlight": "forced_false", "page_head_tags_type": "display_search_related"}, "container": {"layout": "dsr"}, "components": [{"type": "brand_logo", "link_to_home": true, "logo_dark_mode": true}, {"type": "columns", "layout": "dsrw", "one": [{"type": "related_source_matches_visymo", "yes": [{"type": "bing_ads_top_ad_unit", "amount": 4, "ad_style_id": 4}], "no": [{"type": "google_ads_top_unit", "amount": 4}]}, {"type": "organic_results_title"}, {"type": "organic_results_with_fallback", "results": {"type": "content_page_results_as_organic_results", "amount": 4}, "fallback": {"type": "organic_results", "amount": 4}}, {"type": "organic_error_message"}]}, {"type": "footer", "components": [{"type": "columns", "section": "footer", "section_css_properties": ["background"], "one": [{"type": "has_organic_results", "yes": [{"type": "search_bar", "show_search_query": false, "component_space_modifiers": ["bottom-l", "top-l"]}]}, {"type": "footer_logo", "logo_dark_mode": true}]}, {"type": "footer_navigation", "layout": "dsr"}]}]}