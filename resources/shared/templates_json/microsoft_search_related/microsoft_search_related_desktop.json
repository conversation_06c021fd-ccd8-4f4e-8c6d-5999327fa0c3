{"options": {"keyword_highlight": "forced_false", "organic_keyword_highlight": "forced_false", "page_head_tags_type": "display_search_related"}, "container": {"layout": "dsr"}, "components": [{"type": "brand_logo", "link_to_home": true, "component_space_modifiers": ["top"]}, {"type": "columns", "one": [{"type": "search_bar", "show_search_query": false, "component_space_modifiers": ["top-l"]}, {"type": "has_content_page", "yes": [{"type": "content_page_header", "component_space_modifiers": ["top"]}, {"type": "current_page_matches", "page": 1, "yes": [{"type": "content_page_title"}, {"type": "content_page_excerpt", "component_space_modifiers": ["top", "bottom"]}], "no": [{"type": "content_page_paragraph", "component_space_modifiers": ["top", "bottom"]}]}], "no": [{"type": "organic_results", "result_amount_optimization": false, "amount": 1, "result_description_more_link": true, "result_display_url_link": false, "result_title_link": false, "show_result_display_url": false, "max_description_length": 500, "component_space_modifiers": ["bottom"]}]}, {"type": "related_terms", "amount": 6, "columns": 1, "layout": "chevron", "route": "route_microsoft_search_related_web", "zone": "i"}, {"type": "has_content_page", "yes": [{"type": "content_page_paragraph"}, {"type": "content_page_paragraph", "component_space_modifiers": ["top", "bottom-xl"]}], "no": [{"type": "title", "translation_id": "meta.description.display", "layout": "display", "component_space_modifiers": ["top-l"]}, {"type": "organic_results", "result_amount_optimization": false, "amount": 2, "result_description_more_link": true, "result_display_url_link": false, "result_title_link": false, "show_result_display_url": false}]}, {"type": "current_page_matches", "page": 1, "yes": [{"type": "related_terms", "amount": 4, "columns": 1, "layout": "chevron", "route": "route_microsoft_search_related_web", "zone": "i"}]}, {"type": "has_content_page", "yes": [{"type": "content_page_paragraph", "component_space_modifiers": ["top", "bottom"]}, {"type": "content_page_continue_reading"}, {"type": "content_page_footer"}, {"type": "share_page", "share": "content_page"}], "no": [{"type": "organic_error_message"}]}]}, {"type": "footer", "components": [{"type": "columns", "section": "footer", "section_css_properties": ["background"], "one": [{"type": "footer_logo"}, {"type": "disclaimer"}]}, {"type": "footer_navigation", "layout": "dsr"}]}]}