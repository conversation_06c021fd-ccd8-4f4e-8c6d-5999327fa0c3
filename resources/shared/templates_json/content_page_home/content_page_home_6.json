{"options": {"keyword_highlight": "default_true"}, "container": {"layout": "content-home-5", "font": "Inter"}, "components": [{"type": "columns_range", "start": 1, "end": 2, "section": "search", "section_css_properties": ["box-shadow"], "components": [{"type": "search_header", "layout": "content-category-1"}]}, {"type": "columns_range", "start": 1, "end": 2, "section": "search-category", "section_visible": false, "section_css_properties": ["background", "box-shadow"], "components": [{"type": "content_page_category_results", "max_level": 0, "has_content_pages_with_image": true, "component_space_modifiers": ["top-l", "bottom-xl"]}]}, {"type": "columns_range", "start": 1, "end": 2, "components": [{"type": "title", "layout": "bar", "translation_id": "content_page.recent_releases", "component_space_modifiers": ["top-xxl", "bottom-l"]}]}, {"type": "columns", "one": [{"type": "content_page_results", "result_amount_optimization": false, "layout": "card-7", "amount": 1, "amount_in_row": 1, "component_space_modifiers": ["bottom-xl"]}], "two": [{"type": "content_page_results", "result_amount_optimization": false, "layout": "card-8", "amount": 2, "amount_in_row": 1, "component_space_modifiers": ["bottom-xl"]}]}, {"type": "columns_range", "section": "content-dark", "section_css_properties": ["background", "color"], "start": 1, "end": 2, "components": [{"type": "content_page_results", "result_amount_optimization": false, "layout": "list-1", "title_component": {"type": "title", "layout": "bar", "translation_id": "content_page.updated_articles", "component_space_modifiers": ["top-l", "bottom-l"]}, "amount": 10, "amount_in_row": 2, "component_space_modifiers": ["bottom-xl"]}]}, {"type": "columns_range", "start": 1, "end": 2, "components": [{"type": "content_page_results", "result_amount_optimization": false, "layout": "card-7", "title_component": {"type": "title", "layout": "bar", "translation_id": "content_page.entire_article_collection", "component_space_modifiers": ["top-xxl", "bottom-l"]}, "amount": 16, "component_space_modifiers": ["bottom-xl"]}]}, {"type": "footer", "components": [{"type": "columns_range", "start": 1, "end": 2, "section": "footer-brand", "section_css_properties": ["background", "color"], "components": [{"type": "disclaimer", "component_space_modifiers": ["top-xl", "bottom-xl"]}]}, {"type": "footer_navigation", "layout": "content-4", "show_contact": false, "show_privacy": false}]}]}