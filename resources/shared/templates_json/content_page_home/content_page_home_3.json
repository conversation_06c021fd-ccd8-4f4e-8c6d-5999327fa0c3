{"options": {"keyword_highlight": "default_true"}, "container": {"layout": "content-home-2", "font": "Inter"}, "components": [{"type": "columns_range", "start": 1, "end": 3, "section": "search", "section_css_properties": ["box-shadow"], "components": [{"type": "search_header", "layout": "content-category-2"}]}, {"type": "columns_range", "start": 1, "end": 3, "section": "search-category", "section_visible": false, "section_css_properties": ["background", "box-shadow"], "components": [{"type": "content_page_category_results", "max_level": 0, "has_content_pages_with_image": true, "component_space_modifiers": ["top-l", "bottom-xl"]}]}, {"type": "columns", "one": [{"type": "title", "layout": "content-4", "translation_id": "content_page.latest", "title_highlight_translation_id": "content_page.latest", "component_space_modifiers": ["top-xl", "bottom"]}, {"type": "title", "layout": "content-1", "translation_id": "content_page.from_the_editors.title", "subtitle_translation_id": "content_page.from_the_editors.title_subtitle", "component_space_modifiers": ["top", "bottom-xl"]}], "two": [{"type": "content_page_results", "result_amount_optimization": false, "layout": "card-1", "amount": 1, "amount_in_row": 1, "component_space_modifiers": ["top-xl", "bottom-xl"]}], "three": [{"type": "content_page_results", "result_amount_optimization": false, "layout": "card-1", "amount": 1, "amount_in_row": 1, "component_space_modifiers": ["top-xl", "bottom-xl"]}]}, {"type": "columns_range", "start": 1, "end": 3, "section": "content", "section_css_properties": ["border-top"], "components": [{"type": "content_page_results", "result_amount_optimization": false, "layout": "card-1", "title_component": {"type": "title", "layout": "content-2", "translation_id": "content_page.more_articles.title", "title_highlight_translation_id": "content_page.more_articles.title_highlight", "component_space_modifiers": ["top-xl", "bottom-xl"]}, "amount": 28, "component_space_modifiers": ["bottom-xxl"]}]}, {"type": "footer", "components": [{"type": "columns_range", "start": 1, "end": 3, "section": "disclaimer", "section_css_properties": ["background", "border-top"], "components": [{"type": "disclaimer", "layout": "content", "component_space_modifiers": ["top-xl", "bottom-xl"]}]}, {"type": "columns_range", "start": 1, "end": 3, "components": [{"type": "footer_navigation", "layout": "content-3", "show_contact": false, "show_disclaimer": false}]}]}]}