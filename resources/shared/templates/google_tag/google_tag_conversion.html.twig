{# @var conversion_goal string #}
{# @var pageview string #}
<script>
    try {
        (function () {
            var onMessage = function (event) {
                if (!event.data.match('FSXDC,.aCS')) {
                    return;
                }

                if (event.origin !== 'https://www.google.com' && event.origin !== 'https://www.adsensecustomsearchads.com' && event.origin !== 'https://syndicatedsearch.goog') {
                    return;
                }

                pushEvent('event', 'conversion', {
                    'send_to': '{{ conversion_goal }}',
                    'transaction_id': '{{ pageview }}',
                    'value': 0.0,
                    'currency': 'USD'
                });
            };

            if (window.addEventListener) {
                window.addEventListener('message', onMessage);
            } else {
                window.attachEvent('onmessage', onMessage);
            }
        })();
    } catch (error) {}
</script>
