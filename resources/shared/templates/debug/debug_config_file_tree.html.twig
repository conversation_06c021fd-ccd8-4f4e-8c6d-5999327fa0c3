<html lang="en">
    <head>
        <title>Config File Tree</title>
        <style>
            table {
                border-collapse: collapse;
                width: 100%;
            }

            tr:hover {
                background-color: #eeeeee;
            }

            tr:nth-child(even) {
                background-color: #dddddd;
            }

            tr:nth-child(even):hover {
                background-color: #cccccc;
            }

            td, th {
                border: 1px solid #dddddd;
                text-align: left;
                padding: 8px;
            }
            
            td:first-child, th:first-child {
                width: 80%;
            }
        </style>
    </head>
    <body style="padding: 2rem;">
        <table>
            <thead>
                <tr>
                    <th>Path</th>
                    <th>Last modified at</th>
                </tr>
            </thead>
            <tbody>
                {% for directory in config_file_tree.directories %}
                    {% include '@theme/debug/config_file_tree/file_tree_directory.html.twig' with {
                        directory: directory,
                        level: 0,
                        parent_path: '/'
                    } only %}
                {% endfor %}
                {% for file in config_file_tree.files %}
                    {% include '@theme/debug/config_file_tree/file_tree_file.html.twig' with {
                        file: file,
                        level: 0,
                        parent_path: '/'
                    } only %}
                {% endfor %}
            </tbody>
        </table>
    </body>
</html>
