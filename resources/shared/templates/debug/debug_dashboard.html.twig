<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Dashboard</title>
    <style>
		body {
			background-color: #f8f9fa;
			color: #333333;
			font-family: Arial, sans-serif;
			line-height: 1.6rem;
			margin: 0;
			padding: 1.25rem;
		}

		.container {
			margin: 0 auto;
			max-width: 75rem;
			padding: 0 1rem;
		}

		h1 {
			border-bottom: 0.06rem solid #eeeeee;
			color: #333333;
			margin-bottom: 1.25rem;
			padding-bottom: 0.6rem;
		}

		ul {
			display: flex;
			flex-flow: column;
			gap: 0.3rem;
			list-style-type: none;
			padding: 0;

			li {
				border-radius: 0.3rem;
				cursor: pointer;
				padding: 0.3rem 0.5rem;
				position: relative;

				&:hover {
					background-color: #f0f0f0;
				}

				a {
					color: #0066cc;
					text-decoration: none;

					&:hover {
						text-decoration: underline;
					}

                    &.route-link {
						height: 100%;
						left: 0;
						opacity: 0;
						position: absolute;
						top: 0;
						width: 100%;
						z-index: 1;
					}
				}

				.route-content {
					pointer-events: none;
				}
			}
		}

		.card {
			background-color: #ffffff;
			border-radius: 0.3rem;
			box-shadow: 0 0.2rem 0.3rem rgba(0, 0, 0, 0.1);
			margin: 1.25rem 0;
			padding: 1.25rem;

			h2 {
				border-bottom: 0.06rem solid #eeeeee;
				color: #333333;
				margin-bottom: 1rem;
				margin-top: 0;
				padding-bottom: 0.6rem;
			}

			.method-tag {
				background-color: #d4edda;
				border-radius: 0.1875rem;
				color: #155724;
				display: inline;
				font-size: 0.7em;
				margin-left: 0.3rem;
				padding: 0.2rem 0.3rem;
				pointer-events: auto;

				&.post {
					background-color: #cce5ff;
					color: #004085;
				}
			}

			.post-form {
				background-color: #f8f9fa;
				border: 0.06rem solid #e9ecef;
				border-radius: 0.3rem;
				display: none;
				margin-top: 0.6rem;
				padding: 0.6rem;
				resize: vertical;

				&.active {
					display: block;
				}

				textarea {
					border: 0.06rem solid #ced4da;
					border-radius: 0.3rem;
					font-family: monospace;
					font-size: 0.875rem;
					margin-bottom: 0.6rem;
					min-height: 6.25rem;
					padding: 0.5rem;
					width: 100%;
				}

				button {
					background-color: #007bff;
					border: none;
					border-radius: 0.3rem;
					color: white;
					cursor: pointer;
					font-size: 0.875rem;
					padding: 0.5rem 0.75rem;

					&:hover {
						background-color: #0069d9;
					}
				}

				.form-help {
					color: #6c757d;
				}
			}
		}
    </style>
</head>
<body>
<div class="container">
    <h1>Debug Dashboard</h1>

    <div class="card">
        <h2>Routes</h2>

        <ul>
            {% for route_data in routes_with_parameters %}
                {% set route_original_path = route_data.route.getPath() %}

                <li{% if route_data.isPostOnly() %} class="post-only"{% endif %}
                        data-route-path="{{ route_original_path }}">
                    {% if not route_data.isPostOnly() %}
                        <a href="{{ route_data.path }}" class="route-link"></a>
                    {% endif %}
                    <div class="route-content">
                        {% if route_data.isPostOnly() %}
                            <span class="post-route">{{ route_original_path }}</span>
                            <span class="method-tag post">POST</span>
                        {% else %}
                            <span>{{ route_original_path }}</span>
                            {% for method in route_data.route.getMethods() %}
                                <span class="method-tag {{ method|lower }}">{{ method }}</span>
                            {% endfor %}
                        {% endif %}

                        {% if route_data.hasParameters %}
                            <small>(with default parameters)</small>
                        {% endif %}
                    </div>

                    {% if route_data.isPostOnly() %}
                        <div class="post-form" id="form-{{ route_data.getHtmlId() }}">
                            <form method="post" action="{{ route_data.path }}?{{ route_data.getPostQuery() }}" target="_blank">
                                <div class="form-help">Enter data to send with the POST request:</div>

                                {% for name, requirement in route_data.route.getRequirements() %}
                                    <label for="{{ name }}">{{ name|replace({'_' : ' '}) }}</label>
                                    <textarea name="{{ name }}"></textarea>
                                {% endfor %}

                                <button type="submit">Send POST Request</button>
                            </form>
                        </div>
                    {% endif %}
                </li>
            {% endfor %}
        </ul>
    </div>

    <div class="card">
        <h2>Default Route Parameters</h2>

        {% for param_name, param_value in route_parameters %}
            <div class="parameter-item">
                <strong>{{ param_name }}</strong>: {{ param_value }}
            </div>
        {% endfor %}
    </div>
</div>

<script>
    const postRoutes = document.querySelectorAll('.post-only');

    postRoutes.forEach(route => {
        route.addEventListener('click', (event) => {
            event.stopPropagation();
            const route = event.target.getAttribute('data-route-path');
            const formId = 'form-' + route.replace(/\//g, '-').replace(/[{}]/g, '');
            const form = document.getElementById(formId);

            form.classList.toggle('active');
        });
    });
</script>
</body>
</html>
