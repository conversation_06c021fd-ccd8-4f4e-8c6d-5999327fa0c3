{"options": {"keyword_highlight": "forced_false", "organic_keyword_highlight": "forced_false", "page_head_tags_type": "display_search_related"}, "container": {"layout": "dsr", "mode": "tt-dark"}, "components": [{"type": "brand_logo", "link_to_home": true, "logo_dark_mode": true}, {"type": "columns", "one": [{"type": "has_content_page", "yes": [{"type": "content_page_header", "component_space_modifiers": ["top"]}, {"type": "content_page_title", "layout": "small"}, {"type": "content_page_excerpt", "max_length": 200, "split_on_line_end": true, "component_space_modifiers": ["top", "bottom"]}], "no": [{"type": "title", "translation_id": "content_page.title", "subtitle_translation_id": "content_page.subtitle", "layout": "dsr-dark", "component_space_modifiers": ["top"]}, {"type": "organic_results_with_fallback", "results": {"type": "organic_content_page_results", "result_amount_optimization": false, "amount": 1, "link_to_active_brand": true, "max_description_length": 130, "component_space_modifiers": ["bottom"], "layout": "dsr-tt"}, "fallback": {"type": "organic_results", "result_amount_optimization": false, "amount": 1, "max_description_length": 130, "component_space_modifiers": ["bottom"], "layout": "dsr-dark"}}]}, {"type": "google_related_terms", "amount": 6, "container_suffix": "dark-tk", "target": "content", "terms_url_parameter_enabled": true, "route": "route_display_search_related_web", "fallback_related_terms": {"type": "related_terms", "amount": 6, "columns": 1, "layout": "fallback-dark", "route": "route_display_search_related_web", "zone": "i"}}, {"type": "has_content_page", "yes": [{"type": "content_page_excerpt", "start_after_length": 200, "split_on_line_end": true}, {"type": "content_page_paragraph", "component_space_modifiers": ["top", "bottom"]}, {"type": "content_page_paragraph", "component_space_modifiers": ["top", "bottom"]}], "no": [{"type": "organic_results_with_fallback", "results": {"type": "organic_content_page_results", "result_amount_optimization": false, "amount": 5, "link_to_active_brand": true, "layout": "dsr-tt"}, "fallback": {"type": "organic_results", "result_amount_optimization": false, "amount": 5, "layout": "dsr-dark"}}]}, {"type": "google_related_terms", "amount": 6, "target": "content", "container_suffix": "dark-tk", "terms_url_parameter_enabled": true, "route": "route_display_search_related_web", "fallback_related_terms": {"type": "related_terms", "amount": 6, "columns": 1, "layout": "fallback-dark", "route": "route_display_search_related_web", "zone": "i", "repeat_terms": false}}, {"type": "has_content_page", "yes": [{"type": "content_page_paragraph", "component_space_modifiers": ["top-l", "bottom-l"]}, {"type": "content_page_image", "show_caption": true}, {"type": "content_page_paragraphs", "layout": "container", "paragraph_component": {"type": "content_page_paragraph", "component_space_modifiers": ["top-l", "bottom-l"]}}, {"type": "display_banner", "formats": [{"width": 300, "height": 250}]}, {"type": "content_page_footer"}, {"type": "share_page", "share": "content_page"}, {"type": "scroll_to_top"}], "no": [{"type": "organic_error_message"}]}]}, {"type": "footer", "components": [{"type": "footer_logo", "logo_dark_mode": true}, {"type": "columns", "one": [{"type": "disclaimer"}]}, {"type": "footer_navigation", "layout": "dsr"}]}]}