<?php

declare(strict_types=1);

namespace App\Article\Twig;

use App\ContentPage\Url\ContentPageUrlGenerator;
use App\Http\Request\Info\RequestInfoInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

final class ArticlePathExtension extends AbstractExtension
{
    public function __construct(
        private readonly ContentPageUrlGenerator $contentPageUrlGenerator,
        private readonly RequestInfoInterface $requestInfo
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'persistent_article_path',
                $this->getPersistentArticlePath(...),
                [
                    'is_safe' => ['html'],
                ],
            ),
        ];
    }

    private function getPersistentArticlePath(ContentPage $contentPage, ?string $preferenceRoute = null): string
    {
        return $this->contentPageUrlGenerator->generatePersistentUrl(
            contentPage    : $contentPage,
            preferenceRoute: $preferenceRoute ?? $this->requestInfo->getRoute(),
        );
    }
}
