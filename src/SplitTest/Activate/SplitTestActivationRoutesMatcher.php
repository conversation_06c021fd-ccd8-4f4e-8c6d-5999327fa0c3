<?php

declare(strict_types=1);

namespace App\SplitTest\Activate;

use App\Http\Request\Info\RequestInfoInterface;
use App\SplitTest\Request\SplitTestRequestInterface;
use App\SplitTest\Settings\SplitTestActivation;
use Psr\Log\LoggerInterface;

readonly class SplitTestActivationRoutesMatcher
{
    public function __construct(
        private RequestInfoInterface $requestInfo,
        private SplitTestRequestInterface $splitTestRequest,
        private LoggerInterface $logger
    )
    {
    }

    public function isActivationRoute(SplitTestActivation $splitTestActivation, bool $forActivatedTest): bool
    {
        // Always match route is only used when matching for already activated tests
        if ($forActivatedTest && $this->splitTestRequest->hasAlwaysMatchRouteFlag()) {
            return true;
        }

        $activationRoutes = $splitTestActivation->getRoutes();

        // No filter on activation routes, accept all
        if ($activationRoutes === []) {
            return true;
        }

        $route = $this->requestInfo->getRoute();

        foreach ($activationRoutes as $activationRoute) {
            $mappedRoute = SplitTestRoute::tryFrom($activationRoute);

            if ($mappedRoute === null) {
                $this->logger->warning('Unknown {route} in split test activation', ['route' => $activationRoute]);

                continue;
            }

            if ($mappedRoute->value === $route) {
                return true;
            }
        }

        return false;
    }
}
