<?php

declare(strict_types=1);

namespace App\SplitTest\Activate;

use App\Domain\Settings\DomainSettingsHelperInterface;
use App\SplitTest\Settings\SplitTestActivation;
use App\SplitTest\Settings\SplitTestSettings;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\Tracking\Model\TrafficSource;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

final readonly class SplitTestActivator implements SplitTestActivationMatcherInterface
{
    private const array SERVICE_CONFIG_REQUEST_MAPPING = [
        SplitTestActivation::SERVICE_VALUE_GOOGLE    => TrafficSource::GOOGLE,
        SplitTestActivation::SERVICE_VALUE_MICROSOFT => TrafficSource::MICROSOFT,
    ];

    public function __construct(
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private DateTimeFactory $dateTimeFactory,
        private WebsiteSettingsHelper $websiteSettingsHelper,
        private SplitTestActivationRoutesMatcher $splitTestSettingsRoutesMatcher,
        private DomainSettingsHelperInterface $domainSettingsHelper
    )
    {
    }

    public static function getDefaultPriority(): int
    {
        // Default has the lowest priority
        return -100;
    }

    public function splitTestMatchesActivationConditions(SplitTestSettings $splitTest, bool $forActivatedTest): bool
    {
        $splitTestActivation = $splitTest->getActivation();

        if (!$this->matchesDevice($splitTestActivation)) {
            return false;
        }

        if (!$this->matchesService($splitTestActivation)) {
            return false;
        }

        if (!$this->matchesPeriod($splitTestActivation)) {
            return false;
        }

        if (!$this->matchesDomain($splitTestActivation)) {
            return false;
        }

        if (!$this->supportsAdSenseContainerSuffixTest($splitTest)) {
            return false;
        }

        return $this->matchesRoutes($splitTestActivation, $forActivatedTest);
    }

    private function matchesDevice(SplitTestActivation $splitTestActivation): bool
    {
        // No filter on device, accept all
        if ($splitTestActivation->getDevice() === null) {
            return true;
        }

        return $this->activeTrackingEntryHelper->getActiveTrackingEntry()->device === $splitTestActivation->getDevice();
    }

    private function matchesService(SplitTestActivation $splitTestActivation): bool
    {
        $service = $splitTestActivation->getService();

        // No filter on service, accept all
        if ($service === null) {
            return true;
        }

        $expectedTrafficSource = self::SERVICE_CONFIG_REQUEST_MAPPING[$service];
        $actualTrafficSource = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->trafficSource;

        return $expectedTrafficSource === $actualTrafficSource;
    }

    private function matchesPeriod(SplitTestActivation $splitTestActivation): bool
    {
        $dateNow = $this->dateTimeFactory->createNow();

        if ($splitTestActivation->getDateStart() > $dateNow) {
            return false;
        }

        return $splitTestActivation->getDateEnd() === null || $splitTestActivation->getDateEnd() >= $dateNow;
    }

    private function matchesDomain(SplitTestActivation $splitTestActivation): bool
    {
        $domains = $splitTestActivation->getDomains();

        // No filter on domains, accept all
        if ($domains === []) {
            return true;
        }

        $domain = $this->domainSettingsHelper->getSettings()->host;

        return in_array($domain, $domains, true);
    }

    private function supportsAdSenseContainerSuffixTest(SplitTestSettings $splitTestSettings): bool
    {
        $containerSuffix = null;

        foreach ($splitTestSettings->getVariants() as $variant) {
            if ($variant->getContainerSuffix() !== null) {
                $containerSuffix = $variant->getContainerSuffix();

                break;
            }
        }

        if ($containerSuffix === null) {
            return true;
        }

        return $this->websiteSettingsHelper->getSettings()->getGoogleAdSense()->isEnabled();
    }

    private function matchesRoutes(SplitTestActivation $splitTestActivation, bool $forActivatedTest): bool
    {
        return $this->splitTestSettingsRoutesMatcher->isActivationRoute($splitTestActivation, $forActivatedTest);
    }
}
