<?php

declare(strict_types=1);

namespace App\SplitTest\Activate;

use App\AdBot\Request\AdBotRequestInterface;
use App\Debug\Request\DebugRequestInterface;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Search\Request\SearchRequestInterface;
use App\SplitTest\Settings\SplitTestListSettingsRepository;
use App\SplitTest\Settings\SplitTestSettings;
use App\SplitTest\Settings\SplitTestVariant;
use App\SplitTest\SplitTestExtendedWriterInterface;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use Visymo\Shared\Domain\Generator\RandomPercentageGenerator;

class SplitTestActivateHelper
{
    /** @var SplitTestActivationMatcherInterface[] */
    private array $splitTestActivationMatchers;

    /**
     * @param iterable<SplitTestActivationMatcherInterface> $splitTestActivationMatchers
     */
    public function __construct(
        private readonly SplitTestExtendedWriterInterface $splitTestExtendedWriter,
        iterable $splitTestActivationMatchers,
        private readonly AdBotRequestInterface $adBotRequest,
        private readonly SearchRequestInterface $searchRequest,
        private readonly DebugRequestInterface $debugRequest,
        private readonly SplitTestListSettingsRepository $splitTestListSettingsRepository,
        private readonly RandomPercentageGenerator $randomPercentageGenerator,
        private readonly ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private readonly ActiveSplitTestFactory $activeSplitTestFactory,
        private readonly FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
        foreach ($splitTestActivationMatchers as $splitTestActivationMatcher) {
            $this->addSplitTestActivationMatcher($splitTestActivationMatcher);
        }
    }

    public function startSplitTest(): void
    {
        // Ad bots will not participate in split tests
        if ($this->adBotRequest->isAdBot() || $this->friendlyBotRequest->isFriendlyBot()) {
            return;
        }

        // It is not necessary to validate for debug. It should be possible to test a variant while the test is not active yet.
        $debugSplitTestVariant = $this->debugRequest->getSplitTestVariant();

        if ($debugSplitTestVariant !== null) {
            $this->splitTestExtendedWriter->setVariant($debugSplitTestVariant);
            // Set the id to 0, so we can identify debug
            $this->splitTestExtendedWriter->setId(0);

            return;
        }

        // Require tracking entry which is only created on landing pages
        if ($this->activeTrackingEntryHelper->getActiveTrackingEntry()->isEmpty) {
            return;
        }

        if ($this->validateActiveTest()) {
            return;
        }

        // Remove current active split test value
        $this->setActiveTrackingEntryWithActiveSplitTest(null);

        $this->startNewTest();
    }

    private function validateActiveTest(): bool
    {
        // No tests available
        if (!$this->splitTestListSettingsRepository->getSplitTestListSettings()->hasSplitTests()) {
            return false;
        }

        $activeSplitTest = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->activeSplitTest;

        if ($activeSplitTest === null) {
            return false;
        }

        $splitTest = $this->splitTestListSettingsRepository->getSplitTestListSettings()
            ->findById($activeSplitTest->getSplitTestId());

        if ($splitTest === null) {
            return false;
        }

        if (!$this->splitTestMatchesActivationConditions($splitTest, true)) {
            return false;
        }

        // Control variant
        if ($activeSplitTest->getSplitTestVariant() === null) {
            $this->setExtendedHelperValues($splitTest, null);

            return true;
        }

        $splitTestVariant = $splitTest->findVariantByValue($activeSplitTest->getSplitTestVariant());

        // Variant doest not exist (anymore)
        if ($splitTestVariant === null) {
            return false;
        }

        $this->setExtendedHelperValues($splitTest, $splitTestVariant);

        return true;
    }

    private function startNewTest(): void
    {
        $splitTest = $this->findMatchingSplitTest();

        // No matching test found
        if ($splitTest === null) {
            return;
        }

        $splitTestVariant = $this->selectSplitTestVariant($splitTest, false);

        $this->setSplitTestVariant($splitTest, $splitTestVariant);
    }

    private function findMatchingSplitTest(): ?SplitTestSettings
    {
        $splitTests = $this->splitTestListSettingsRepository->getSplitTestListSettings()->getSplitTests();

        foreach ($splitTests as $splitTest) {
            if ($this->splitTestMatchesActivationConditions($splitTest, false)) {
                return $splitTest;
            }
        }

        return null;
    }

    private function splitTestMatchesActivationConditions(SplitTestSettings $splitTest, bool $forActivatedTest): bool
    {
        $splitTestActivationMatcher = $this->findActivationMatcher($splitTest, $forActivatedTest);

        if ($splitTestActivationMatcher === null) {
            return false;
        }

        return (bool)$splitTestActivationMatcher->splitTestMatchesActivationConditions($splitTest, $forActivatedTest);
    }

    private function findActivationMatcher(
        SplitTestSettings $splitTest,
        bool $forActivatedTest
    ): ?SplitTestActivationMatcherInterface
    {
        foreach ($this->splitTestActivationMatchers as $splitTestActivationMatcher) {
            $splitTestMatchesActivationConditions = $splitTestActivationMatcher->splitTestMatchesActivationConditions(
                $splitTest,
                $forActivatedTest,
            );

            if ($splitTestMatchesActivationConditions !== null) {
                return $splitTestActivationMatcher;
            }
        }

        return null;
    }

    private function selectSplitTestVariant(SplitTestSettings $splitTest, bool $forActivatedTest): ?SplitTestVariant
    {
        $splitTestActivationMatcher = $this->findActivationMatcher($splitTest, $forActivatedTest);

        if ($splitTestActivationMatcher instanceof SplitTestVariantSelectorInterface) {
            return $splitTestActivationMatcher->selectSplitTestVariant($splitTest);
        }

        return $this->selectRandomSplitTestVariant($splitTest);
    }

    public function selectRandomSplitTestVariant(SplitTestSettings $splitTest): ?SplitTestVariant
    {
        $selectPercentage = $this->randomPercentageGenerator->generateRandomPercentage();
        $totalPercentage = 0;

        foreach ($splitTest->getVariants() as $splitTestVariant) {
            $totalPercentage += $splitTestVariant->getPercentage();

            if ($selectPercentage <= $totalPercentage) {
                return $splitTestVariant;
            }
        }

        // No test variant chosen, return control variant as null
        return null;
    }

    private function setSplitTestVariant(SplitTestSettings $splitTest, ?SplitTestVariant $splitTestVariant): void
    {
        $this->setExtendedHelperValues($splitTest, $splitTestVariant);

        $variant = $splitTestVariant?->getVariant();
        $activeSplitTest = $this->activeSplitTestFactory->create($splitTest->getId(), $variant);
        $this->setActiveTrackingEntryWithActiveSplitTest($activeSplitTest);
    }

    private function setExtendedHelperValues(SplitTestSettings $splitTest, ?SplitTestVariant $splitTestVariant): void
    {
        $isLandingPage = $this->searchRequest->isLandingPage();

        $this->splitTestExtendedWriter->setId($splitTest->getId());
        $this->splitTestExtendedWriter->setContainsVariants(array_keys($splitTest->getVariants()));

        if ($splitTestVariant !== null) {
            $channel = $isLandingPage
                ? $splitTestVariant->getChannels()->getLandingPage()
                : $splitTestVariant->getChannels()->getAdvertised();

            $this->splitTestExtendedWriter->setVariant($splitTestVariant->getVariant());
            $this->splitTestExtendedWriter->setChannel($channel);
            $this->splitTestExtendedWriter->setContainerSuffix($splitTestVariant->getContainerSuffix());

            return;
        }

        $controlChannel = $isLandingPage
            ? $splitTest->getControlChannels()->getLandingPage()
            : $splitTest->getControlChannels()->getAdvertised();

        $this->splitTestExtendedWriter->setVariant(null);
        $this->splitTestExtendedWriter->setChannel($controlChannel);
    }

    private function setActiveTrackingEntryWithActiveSplitTest(?ActiveSplitTest $activeSplitTest): void
    {
        $activeTrackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();

        if ($activeTrackingEntry->isEmpty) {
            return;
        }

        $trackingEntry = $activeTrackingEntry->withActiveSplitTest($activeSplitTest);
        $this->activeTrackingEntryHelper->setActiveTrackingEntry($trackingEntry);
    }

    private function addSplitTestActivationMatcher(
        SplitTestActivationMatcherInterface $splitTestActivationMatcher
    ): void
    {
        $this->splitTestActivationMatchers[] = $splitTestActivationMatcher;
    }
}
