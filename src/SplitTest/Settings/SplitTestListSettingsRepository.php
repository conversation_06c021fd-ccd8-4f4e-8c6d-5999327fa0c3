<?php

declare(strict_types=1);

namespace App\SplitTest\Settings;

use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;

class SplitTestListSettingsRepository
{
    private SplitTestListSettings $splitTestListSettings;

    public function __construct(
        private readonly WebsiteConfigurationHelper $websiteConfigurationHelper,
        private readonly SplitTestListSettingsFactory $splitTestListSettingsFactory
    )
    {
    }

    public function getSplitTestListSettings(): SplitTestListSettings
    {
        if (isset($this->splitTestListSettings)) {
            return $this->splitTestListSettings;
        }

        $splitTestsConfig = $this->websiteConfigurationHelper->getConfiguration()->getSplitTestsConfig();

        return $this->splitTestListSettings = $this->splitTestListSettingsFactory->create($splitTestsConfig);
    }
}
