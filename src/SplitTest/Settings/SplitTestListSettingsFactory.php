<?php

declare(strict_types=1);

namespace App\SplitTest\Settings;

final readonly class SplitTestListSettingsFactory
{
    /**
     * @param mixed[] $splitTestsConfig
     */
    public function create(array $splitTestsConfig): SplitTestListSettings
    {
        $splitTests = [];

        foreach ($splitTestsConfig as $splitTestId => $splitTestConfig) {
            $splitTests[] = SplitTestSettings::createFromArrayWithId((int)$splitTestId, $splitTestConfig);
        }

        return new SplitTestListSettings($splitTests);
    }
}
