<?php

declare(strict_types=1);

namespace App\Debug\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Helper\DebugHelper;
use App\Debug\Info\DebugInfo;

class CompositeSearchApiDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_CSAPI_REQUEST  = 'csapi request';
    public const string KEY_CSAPI_RESPONSE = 'csapi response';
    public const string KEY_CSAPI_TRACES   = 'csapi traces';

    public function __construct(
        private readonly DebugHelper $debugHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        $debugInfo = [];
        $compositeSearchApiRequests = $this->debugHelper->getCompositeSearchApiRequests();
        $compositeSearchApiResponses = $this->debugHelper->getCompositeSearchApiResponses();

        if ($compositeSearchApiRequests !== []) {
            $requestDebugInfo = [];

            foreach ($compositeSearchApiRequests as $compositeSearchApiRequest) {
                $requestDebugInfo[] = json_decode(
                    $compositeSearchApiRequest->toJson(),
                    false,
                    512,
                    JSON_THROW_ON_ERROR,
                );
            }

            $debugInfo[] = new DebugInfo(
                self::KEY_CSAPI_REQUEST,
                $requestDebugInfo,
            );
        }

        if ($compositeSearchApiResponses !== []) {
            $responseDebugInfo = [];
            $tracesDebugInfo = [];

            foreach ($compositeSearchApiResponses as $compositeSearchApiResponse) {
                $responseDebugInfo[] = $compositeSearchApiResponse->toArray();
                $tracesDebugInfo[] = $compositeSearchApiResponse->getDebug()->getTraces();
            }

            $debugInfo[] = new DebugInfo(
                self::KEY_CSAPI_RESPONSE,
                $responseDebugInfo,
            );
            $debugInfo[] = new DebugInfo(
                self::KEY_CSAPI_TRACES,
                $tracesDebugInfo,
            );
        }

        return $debugInfo;
    }

    public static function getDefaultPriority(): int
    {
        return 50;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_CSAPI_REQUEST,
            self::KEY_CSAPI_RESPONSE,
            self::KEY_CSAPI_TRACES,
        ];
    }
}
