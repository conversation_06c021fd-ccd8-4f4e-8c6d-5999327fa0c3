<?php

declare(strict_types=1);

namespace App\Debug\Helper;

use App\Debug\Model\DebugRouteModel;
use App\Debug\Request\DebugRequestInterface;
use App\Debug\Routing\DebugRouteChecker;
use <PERSON>ymfony\Component\Routing\Route;
use Symfony\Component\Routing\RouterInterface;

final readonly class DebugDashboardHelper
{
    public function __construct(
        private RouterInterface $router
    )
    {
    }

    /**
     * @return Route[]
     */
    public function getDebugRoutes(): array
    {
        $debugRoutes = [];
        $routeCollection = $this->router->getRouteCollection();
        $routeCollection->remove('route_debug_dashboard');

        foreach ($routeCollection as $route) {
            if (str_contains($route->getCondition(), DebugRouteChecker::getAlias())) {
                $debugRoutes[] = $route;
            }
        }

        return $debugRoutes;
    }

    /**
     * @return array<string, mixed>
     */
    public function getRouteParameterMapping(): array
    {
        return [
            'styleId' => DebugRequestInterface::DEBUG_GOOGLE_AD_STYLE_ID,
        ];
    }

    /**
     * Get debug routes with parameters applied where needed
     *
     * @return DebugRouteModel[]
     */
    public function getDebugRoutesWithParameters(): array
    {
        $routes = $this->getDebugRoutes();
        $parameterMapping = $this->getRouteParameterMapping();
        $result = [];

        foreach ($routes as $route) {
            $path = $route->getPath();
            $hasParameters = false;

            foreach ($parameterMapping as $paramName => $paramValue) {
                $pattern = sprintf('/{%s}/', $paramName);

                if (preg_match($pattern, $path) === 1) {
                    $hasParameters = true;
                    $path = (string)preg_replace($pattern, (string)$paramValue, $path);
                }
            }

            $result[] = new DebugRouteModel(
                $route,
                $path,
                $hasParameters,
            );
        }

        return $result;
    }
}
