<?php

declare(strict_types=1);

namespace App\Debug\Helper;

use Visymo\Filesystem\File\System\FilesystemInterface;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFormat;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final readonly class DebugProjectConfigFileHelper
{
    public function __construct(
        public SerializedFileInterface $projectConfigJsonFile,
        public SerializedFileInterface $projectConfigPhpFile
    )
    {
    }

    /**
     * @return mixed[]
     */
    public function getDebugInfo(): array
    {
        return [
            $this->projectConfigJsonFile->getFileName() => $this->formatModifiedAt(
                $this->projectConfigJsonFile,
            ),
            $this->projectConfigPhpFile->getFileName()  => $this->formatModifiedAt(
                $this->projectConfigPhpFile,
            ),
        ];
    }

    private function formatModifiedAt(FileSystemInterface $file): string
    {
        // Show date in Amsterdam timezone
        /** @var \DateTimeImmutable $lastModifiedAt */
        $lastModifiedAt = $file->getLastModifiedAt();
        $lastModifiedAt = $lastModifiedAt->setTimezone(TimezoneEnum::AMSTERDAM->toDateTimeZone());

        return $lastModifiedAt->format(
            sprintf('%s e', DateTimeFormat::DATE_TIME),
        );
    }
}
