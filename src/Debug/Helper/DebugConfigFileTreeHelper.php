<?php

declare(strict_types=1);

namespace App\Debug\Helper;

use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final readonly class DebugConfigFileTreeHelper
{
    public function __construct(
        private string $configPath,
        private DateTimeFactory $dateTimeFactory
    )
    {
    }

    /**
     * @return array<string,array<int,array<string,mixed>>>
     */
    public function getConfigFileTree(): array
    {
        return $this->scanDirectory($this->configPath);
    }

    /**
     * @return array<string,array<int,array<string,mixed>>>
     */
    private function scanDirectory(string $path): array
    {
        $directories = [];
        $files = [];

        $directoryItems = new \DirectoryIterator($path);

        foreach ($directoryItems as $item) {
            if ($item->isDot()) {
                continue;
            }

            if ($item->isFile()) {
                $files[$item->getPathname()] = [
                    'name'     => $item->getFilename(),
                    'path'     => $item->getPathname(),
                    'modified' => $this->dateTimeFactory->create(
                        '@'.$item->getMTime(),
                        TimezoneEnum::AMSTERDAM,
                    ),
                ];
            }
        }

        $directoryItems = new \DirectoryIterator($path);
        foreach ($directoryItems as $item) {
            if ($item->isDot()) {
                continue;
            }

            if ($item->isDir()) {
                $directories[$item->getPathname()] = [
                    'name'     => $item->getFilename(),
                    'path'     => $item->getPathname(),
                    'modified' => $this->dateTimeFactory->create(
                        '@'.$item->getMTime(),
                        TimezoneEnum::AMSTERDAM,
                    ),
                    'contents' => $this->scanDirectory($item->getPathname()),
                ];
            }
        }

        usort(
            $directories,
            static fn (array $a, array $b): int => strcmp($a['name'], $b['name']),
        );

        usort(
            $files,
            static fn (array $a, array $b): int => strcmp($a['name'], $b['name']),
        );

        return [
            'directories' => $directories,
            'files'       => $files,
        ];
    }
}
