<?php

declare(strict_types=1);

namespace App\Debug\Helper;

use App\Brand\Settings\BrandSettingsHelper;
use App\WebsiteSettings\Configuration\Import\ImportWebsiteConfigurationFileRepository;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFileRepository;
use Visymo\Filesystem\File\System\FilesystemInterface;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFormat;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final readonly class DebugBrandConfigFileHelper
{
    public function __construct(
        private BrandSettingsHelper $brandSettingsHelper,
        private ImportWebsiteConfigurationFileRepository $importWebsiteConfigurationFileRepository,
        private WebsiteConfigurationFileRepository $websiteConfigurationFileRepository,
        private SerializedFileInterface $domainToBrandMapSerializedFile
    )
    {
    }

    /**
     * @return mixed[]
     */
    public function getDebugInfo(): array
    {
        $brandSlug = $this->brandSettingsHelper->getSettings()->getSlug();
        $brandPhpFile = $this->websiteConfigurationFileRepository->getForBrand($brandSlug);
        $brandJsonFile = $this->importWebsiteConfigurationFileRepository->getForBrand($brandSlug);

        return array_merge(
            [
                $this->domainToBrandMapSerializedFile->getFileName() => $this->formatModifiedAt($this->domainToBrandMapSerializedFile),
            ],
            [
                $brandPhpFile->getFileName()  => $this->formatModifiedAt($brandPhpFile),
                $brandJsonFile->getFileName() => $this->formatModifiedAt($brandJsonFile),
            ],
        );
    }

    private function formatModifiedAt(FileSystemInterface $file): string
    {
        // Show date in Amsterdam timezone
        /** @var \DateTimeImmutable $lastModifiedAt */
        $lastModifiedAt = $file->getLastModifiedAt();
        $lastModifiedAt = $lastModifiedAt->setTimezone(TimezoneEnum::AMSTERDAM->toDateTimeZone());

        return $lastModifiedAt->format(
            sprintf('%s e', DateTimeFormat::DATE_TIME),
        );
    }
}
