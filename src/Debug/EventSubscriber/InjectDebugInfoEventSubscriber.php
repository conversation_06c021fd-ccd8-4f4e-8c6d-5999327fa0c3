<?php

declare(strict_types=1);

namespace App\Debug\EventSubscriber;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Helper\DebugHelper;
use App\Debug\Request\DebugRequestInterface;
use App\Kernel\KernelResponseEvent;
use App\Office\Request\OfficeRequestInterface;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Twig\Environment;

final class InjectDebugInfoEventSubscriber implements EventSubscriberInterface
{
    public const string DEBUG_INFO_HTML_PLACEHOLDER = '<!-- Debug Info -->';

    /** @var DebugInfoProviderInterface[] */
    private array $debugInfoProviders = [];

    /**
     * @param iterable<DebugInfoProviderInterface> $debugInfoProviders
     */
    public function __construct(
        private readonly Environment $twig,
        private readonly OfficeRequestInterface $officeRequest,
        private readonly DebugRequestInterface $debugRequest,
        private readonly DebugHelper $debugHelper,
        iterable $debugInfoProviders
    )
    {
        foreach ($debugInfoProviders as $debugInfoProvider) {
            $this->addDebugInfoProvider($debugInfoProvider);
        }
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            RenderTemplateHeadersEvent::NAME        => 'injectDebugData',
            RenderTemplateFootersEvent::NAME        => 'injectDebugInfo',
            // Add debug info as last to response
            KernelResponseEvent::NO_REDIRECT->value => ['onResponse', -20000],
        ];
    }

    public function injectDebugData(RenderTemplateHeadersEvent $event): void
    {
        if (!$this->officeRequest->isOffice()) {
            return;
        }

        $jsonTemplateLocation = $this->debugHelper->getJsonTemplateLocation();
        $debugData = [
            'template_path'    => $jsonTemplateLocation?->templateFilePath,
            'template_variant' => $jsonTemplateLocation?->variant,
        ];

        $event->addItem(
            $this->twig->render(
                '@theme/debug/debug_data.html.twig',
                [
                    'debug_data' => $debugData,
                ],
            ),
        );
    }

    public function injectDebugInfo(RenderTemplateFootersEvent $event): void
    {
        if (!$this->debugRequest->debugInfo()) {
            return;
        }

        $event->addItem(self::DEBUG_INFO_HTML_PLACEHOLDER);
    }

    public function onResponse(ResponseEvent $event): void
    {
        if (!$this->debugRequest->debugInfo()) {
            return;
        }

        $debugInfoHtml = [];

        foreach ($this->debugInfoProviders as $debugInfoProvider) {
            foreach ($debugInfoProvider->getDebugInfo() as $debugInfo) {
                $debugInfoHtml[] = $debugInfo->output($this->twig);
            }
        }

        // Replace the placeholder with the debug info
        $response = $event->getResponse();
        $response->setContent(
            str_replace(
                self::DEBUG_INFO_HTML_PLACEHOLDER,
                implode(PHP_EOL, $debugInfoHtml),
                (string)$response->getContent(),
            ),
        );
    }

    /**
     * @return string[]
     */
    public function getKeys(): array
    {
        $keys = [];

        foreach ($this->debugInfoProviders as $debugInfoProvider) {
            $keys = [...$keys, ...$debugInfoProvider->getKeys()];
        }

        return $keys;
    }

    private function addDebugInfoProvider(DebugInfoProviderInterface $debugInfoProvider): void
    {
        $this->debugInfoProviders[] = $debugInfoProvider;
    }
}
