<?php

declare(strict_types=1);

namespace App\Debug\Info;

use Twig\Environment;

final readonly class DebugInfo implements DebugInfoInterface
{
    /**
     * @param mixed[] $data
     */
    public function __construct(
        private string $group,
        private array $data
    )
    {
    }

    public function output(Environment $twig): string
    {
        return $twig->render(
            '@theme/debug/debug_info_json.html.twig',
            [
                'group'       => $this->group,
                'group_items' => $this->data,
            ],
        );
    }
}
