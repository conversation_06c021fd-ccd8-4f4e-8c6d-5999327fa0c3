<?php

declare(strict_types=1);

namespace App\Debug\Controller;

use App\Debug\Helper\DebugDashboardHelper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class DebugDashboardController extends AbstractController
{
    public function __construct(
        private readonly DebugDashboardHelper $debugDashboardHelper
    )
    {
    }

    #[Route(path: '/debug', name: 'route_debug_dashboard', methods: ['GET'])]
    public function dashboard(): Response
    {
        return $this->render(
            '@theme/debug/debug_dashboard.html.twig',
            [
                'routes_with_parameters' => $this->debugDashboardHelper->getDebugRoutesWithParameters(),
                'route_parameters'       => $this->debugDashboardHelper->getRouteParameterMapping(),
            ],
        );
    }
}
