<?php

declare(strict_types=1);

namespace App\Debug\Routing;

use App\Generic\Routing\RouteCheckerInterface;
use App\Office\Request\OfficeRequestInterface;
use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: self::ALIAS)]
final readonly class DebugRouteChecker implements RouteCheckerInterface
{
    private const string ALIAS = 'route_checker_debug';

    public function __construct(
        private OfficeRequestInterface $officeRequest
    )
    {
    }

    public static function getAlias(): string
    {
        return self::ALIAS;
    }

    public function check(): bool
    {
        return $this->officeRequest->isOffice();
    }
}
