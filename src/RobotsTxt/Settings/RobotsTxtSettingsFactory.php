<?php

declare(strict_types=1);

namespace App\RobotsTxt\Settings;

use App\BrandOverride\BrandOverrideModuleState;
use App\Domain\Settings\DomainSettingsHelperInterface;
use App\ModuleSettings\ModuleSettingsFactoryInterface;
use App\ModuleSettings\Value\ModuleValueFactory;
use App\RobotsTxt\RobotsTxtModule;

final class RobotsTxtSettingsFactory implements ModuleSettingsFactoryInterface
{
    public const string DEFAULT_ROBOTS_TXT_FILE_NAME = 'robots.txt';

    public function __construct(
        private readonly BrandOverrideModuleState $brandOverrideModuleState,
        private readonly DomainSettingsHelperInterface $domainSettingsHelper,
        private readonly ModuleValueFactory $moduleValueFactory,
        private readonly string $robotsTxtPath,
        private readonly string $projectDir
    )
    {
    }

    public static function getModuleName(): string
    {
        return RobotsTxtModule::getModuleName();
    }

    /**
     * @inheritDoc
     */
    public function create(array $projectModuleConfig): RobotsTxtSettings
    {
        $brandModuleConfig = $this->getBrandModuleConfig($projectModuleConfig);

        return new RobotsTxtSettings(
            fileName: $this->getRobotsTxtFileName($brandModuleConfig),
        );
    }

    /**
     * @inheritDoc
     */
    public function getModuleValues(array $projectModuleConfig): array
    {
        $brandModuleConfig = $this->getBrandModuleConfig($projectModuleConfig);

        return [
            $this->moduleValueFactory->create(
                moduleName: self::getModuleName(),
                property  : 'has_custom_robots_txt',
                title     : 'Custom robots.txt',
                value     : $brandModuleConfig !== [],
            ),
        ];
    }

    /**
     * @param mixed[] $projectModuleConfig
     *
     * @return mixed[]
     */
    private function getBrandModuleConfig(array $projectModuleConfig): array
    {
        return $this->brandOverrideModuleState->getModuleConfigOverride(
            self::getModuleName(),
        ) ?? $projectModuleConfig;
    }

    /**
     * @param mixed[] $brandModuleConfig
     */
    private function getRobotsTxtFileName(array $brandModuleConfig): string
    {
        $host = $this->domainSettingsHelper->getSettings()->host;

        foreach ($brandModuleConfig as $robotsTxtConfig) {
            $hosts = $robotsTxtConfig[RobotsTxtModule::KEY_HOSTS];

            if ($hosts === [] || in_array($host, $hosts, true)) {
                return $this->getFullRobotsTxtPath(
                    $robotsTxtConfig[RobotsTxtModule::KEY_NAME],
                );
            }
        }

        return $this->robotsTxtPath;
    }

    private function getFullRobotsTxtPath(string $fileName): string
    {
        return sprintf(
            '%s/config/robots/%s',
            $this->projectDir,
            $fileName,
        );
    }
}
