<?php

declare(strict_types=1);

namespace App\Generic\Date;

use App\Debug\Request\DebugRequestInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

class PublishedDateHelper
{
    private const int MAX_DAYS = 90;

    public function __construct(
        private readonly DebugRequestInterface $debugRequest,
        private readonly DateTimeFactory $dateTimeFactory
    )
    {
    }

    public function getPublishedDate(?\DateTime $publishedDate, string $query): \DateTime
    {
        if ($this->debugRequest->showFixedStats()) {
            return $this->dateTimeFactory->create('2023-02-01');
        }

        if ($publishedDate !== null) {
            return $publishedDate;
        }

        $fakeDays = min(mb_strlen($query), self::MAX_DAYS);

        return $this->dateTimeFactory->create(sprintf('-%u days', $fakeDays));
    }
}
