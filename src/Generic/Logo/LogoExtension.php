<?php

declare(strict_types=1);

namespace App\Generic\Logo;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class LogoExtension extends AbstractExtension
{
    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'render_logo_class',
                $this->renderLogoClass(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    public function renderLogoClass(
        ?LogoStyleFilter $logoStyleFilter
    ): string
    {
        $cssClasses = ['logo'];

        if ($logoStyleFilter !== null) {
            $cssClasses[] = sprintf('logo--filter-%s', $logoStyleFilter->value);
        }

        return implode(' ', $cssClasses);
    }
}
