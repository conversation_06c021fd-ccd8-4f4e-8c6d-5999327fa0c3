<?php

declare(strict_types=1);

namespace App\Generic\Routing;

use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

final class RouteCheckerCollection
{
    public const string  ROUTE_CHECKER_ALIAS_PREFIX    = 'route_checker_';
    private const string ROUTE_CHECKER_CONDITION_REGEX = '~^service\(\'(route_checker_[a-z_]+)\'\)\.check\(\)$~';

    /** @var array<string, RouteCheckerInterface> */
    private array $routeCheckers;

    /** @var array<string, string> */
    private array $mapping;

    /**
     * @param iterable<string, RouteCheckerInterface> $routeCheckers
     */
    public function __construct(
        iterable $routeCheckers,
        private readonly SerializedFileInterface $routeToCheckerMappingFile
    )
    {
        $this->routeCheckers = iterator_to_array($routeCheckers);
    }

    public function getByAlias(string $alias): ?RouteCheckerInterface
    {
        return $this->routeCheckers[$alias] ?? null;
    }

    public function getByRoute(string $route): ?RouteCheckerInterface
    {
        $alias = $this->getAliasByRoute($route);

        return $alias !== null
            ? $this->getByAlias($alias)
            : null;
    }

    public function getAliasByRoute(string $route): ?string
    {
        return $this->getMapping()[$route] ?? null;
    }

    public function getAliasByRouteCondition(string $condition): ?string
    {
        if (preg_match(self::ROUTE_CHECKER_CONDITION_REGEX, $condition, $matches) === 1) {
            return $matches[1];
        }

        return null;
    }

    /**
     * @return iterable<string, RouteCheckerInterface>
     */
    public function iterate(): iterable
    {
        foreach ($this->routeCheckers as $alias => $routeChecker) {
            yield $alias => $routeChecker;
        }
    }

    /**
     * @return array<string, string>
     */
    private function getMapping(): array
    {
        if (!isset($this->mapping)) {
            $this->mapping = $this->routeToCheckerMappingFile->getContents();
        }

        return $this->mapping;
    }
}
