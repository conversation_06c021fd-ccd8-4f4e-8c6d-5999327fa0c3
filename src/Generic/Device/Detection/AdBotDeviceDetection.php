<?php

declare(strict_types=1);

namespace App\Generic\Device\Detection;

use App\AdBot\Request\AdBotRequestInterface;
use App\Generic\Device\Device;
use App\Http\Request\Info\RequestInfoInterface;

class AdBotDeviceDetection
{
    // @see Google: https://developers.google.com/search/docs/advanced/crawling/overview-google-crawlers
    // @see Bing: https://www.bing.com/webmasters/help/which-crawlers-does-bing-use-8c184ec0
    private const array MOBILE_DEVICE_DETECT_TERMS = [
        'mobile',
        'iphone',
    ];

    public function __construct(
        private readonly AdBotRequestInterface $adBotRequest,
        private readonly RequestInfoInterface $requestInfo
    )
    {
    }

    public function getDevice(): ?Device
    {
        if (!$this->adBotRequest->isAdBot()) {
            return null;
        }

        // Basic check of terms on user agent to detect device
        $userAgent = strtolower($this->requestInfo->getUserAgent());

        foreach (self::MOBILE_DEVICE_DETECT_TERMS as $term) {
            if (str_contains($userAgent, $term)) {
                return Device::MOBILE;
            }
        }

        return Device::DESKTOP;
    }
}
