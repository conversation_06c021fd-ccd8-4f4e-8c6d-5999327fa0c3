<?php

declare(strict_types=1);

namespace App\Generic\Device\Detection;

use App\Http\Request\Info\RequestInfoInterface;
use Detection\MobileDetect;

readonly class MobileDetectFactory
{
    public function __construct(private RequestInfoInterface $requestInfo)
    {
    }

    public function create(): MobileDetect
    {
        // This factory is used so the correct user agent can be injected instead of relying on _SERVER vars,
        // which the library does by default. This does not work reliably in tests.
        $mobileDetect = new MobileDetect();

        $mobileDetect->setUserAgent($this->requestInfo->getUserAgent());

        return $mobileDetect;
    }
}
