<?php

declare(strict_types=1);

namespace App\ImageSearch\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;
use App\ImageSearch\Filter\ImageColorFilter;
use App\ImageSearch\Filter\ImagePeriodFilter;
use App\ImageSearch\Filter\ImageSizeFilter;
use App\ImageSearch\Filter\ImageTypeFilter;

final class ImageSearchRequest implements ImageSearchRequestInterface
{
    private string $colorFilter;

    private string $periodFilter;

    private string $imageTypeFilter;

    private string $imageSizeFilter;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getPeriod(): ?string
    {
        if (!isset($this->periodFilter)) {
            $this->periodFilter = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_PERIOD,
                ImagePeriodFilter::SUPPORTED_VALUES,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->periodFilter);
    }

    public function getColor(): ?string
    {
        if (!isset($this->colorFilter)) {
            $this->colorFilter = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_COLOR,
                ImageColorFilter::SUPPORTED_VALUES,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->colorFilter);
    }

    public function getImageType(): ?string
    {
        if (!isset($this->imageTypeFilter)) {
            $this->imageTypeFilter = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_IMAGE_TYPE,
                ImageTypeFilter::SUPPORTED_VALUES,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->imageTypeFilter);
    }

    public function getImageSize(): ?string
    {
        if (!isset($this->imageSizeFilter)) {
            $this->imageSizeFilter = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_IMAGE_SIZE,
                ImageSizeFilter::SUPPORTED_VALUES,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->imageSizeFilter);
    }

    /**
     * @inheritDoc
     */
    public function getValues(): array
    {
        return array_filter($this->toArray(), static fn ($value) => $value !== null);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_COLOR      => $this->getColor(),
            self::PARAMETER_PERIOD     => $this->getPeriod(),
            self::PARAMETER_IMAGE_TYPE => $this->getImageType(),
            self::PARAMETER_IMAGE_SIZE => $this->getImageSize(),
        ];
    }
}
