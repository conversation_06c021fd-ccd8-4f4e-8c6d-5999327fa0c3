<?php

declare(strict_types=1);

namespace App\NewsSearch\Request;

use App\Http\Request\RequestInterface;

interface NewsSearchRequestInterface extends RequestInterface
{
    public const string PARAMETER_CATEGORY = 'category';
    public const string PARAMETER_PERIOD   = 'period';

    public function getCategory(): ?string;

    public function getPeriod(): ?string;

    /**
     * @return array<string, string>
     */
    public function getValues(): array;
}
