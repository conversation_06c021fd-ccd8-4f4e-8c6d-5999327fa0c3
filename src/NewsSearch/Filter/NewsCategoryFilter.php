<?php

declare(strict_types=1);

namespace App\NewsSearch\Filter;

use App\JsonTemplate\Component\SearchFilter\SearchFilter;
use App\JsonTemplate\Component\SearchFilter\SearchFilterFactory;
use App\NewsSearch\Request\NewsSearchRequestInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class NewsCategoryFilter
{
    private const string VALUE_BUSINESS      = 'business';
    private const string VALUE_ENTERTAINMENT = 'entertainment';
    private const string VALUE_POLITICS      = 'politics';
    private const string VALUE_SPORTS        = 'sports';
    private const string VALUE_WORLD         = 'world';

    public const array SUPPORTED_VALUES = [
        self::VALUE_BUSINESS,
        self::VALUE_ENTERTAINMENT,
        self::VALUE_POLITICS,
        self::VALUE_SPORTS,
        self::VALUE_WORLD,
    ];

    public function __construct(
        private readonly NewsSearchRequestInterface $newsSearchRequest,
        private readonly SearchFilterFactory $searchFilterFactory,
        private readonly TranslatorInterface $translator
    )
    {
    }

    /**
     * @return array<string, string>
     */
    private function getOptions(): array
    {
        $options = [
            self::VALUE_BUSINESS      => $this->translator->trans('news_search.category.item.business'),
            self::VALUE_ENTERTAINMENT => $this->translator->trans('news_search.category.item.entertainment'),
            self::VALUE_POLITICS      => $this->translator->trans('news_search.category.item.politics'),
            self::VALUE_SPORTS        => $this->translator->trans('news_search.category.item.sports'),
            self::VALUE_WORLD         => $this->translator->trans('news_search.category.item.world'),
        ];

        asort($options);

        return $options;
    }

    public function getSearchFilter(): SearchFilter
    {
        $currentFilterValues = $this->newsSearchRequest->getValues();

        return $this->searchFilterFactory->create(
            $this->translator->trans('news_search.category.label'),
            'route_news_search',
            $currentFilterValues,
            NewsSearchRequestInterface::PARAMETER_CATEGORY,
            $this->getOptions(),
        );
    }
}
