<?php

declare(strict_types=1);

namespace App\NewsSearch\Filter;

use App\JsonTemplate\Component\SearchFilter\SearchFilter;
use App\JsonTemplate\Component\SearchFilter\SearchFilterFactory;
use App\NewsSearch\Request\NewsSearchRequestInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class NewsPeriodFilter
{
    private const string VALUE_DAY   = 'day';
    private const string VALUE_WEEK  = 'week';
    private const string VALUE_MONTH = 'month';

    public const array SUPPORTED_VALUES = [
        self::VALUE_DAY,
        self::VALUE_WEEK,
        self::VALUE_MONTH,
    ];

    public function __construct(
        private readonly NewsSearchRequestInterface $newsSearchRequest,
        private readonly SearchFilterFactory $searchFilterFactory,
        private readonly TranslatorInterface $translator
    )
    {
    }

    /**
     * @return array<string, string>
     */
    private function getOptions(): array
    {
        return [
            self::VALUE_DAY   => $this->translator->trans('news_search.period.item.day'),
            self::VALUE_WEEK  => $this->translator->trans('news_search.period.item.week'),
            self::VALUE_MONTH => $this->translator->trans('news_search.period.item.month'),
        ];
    }

    public function getSearchFilter(): SearchFilter
    {
        $currentFilterValues = $this->newsSearchRequest->getValues();

        return $this->searchFilterFactory->create(
            $this->translator->trans('news_search.period.label'),
            'route_news_search',
            $currentFilterValues,
            NewsSearchRequestInterface::PARAMETER_PERIOD,
            $this->getOptions(),
        );
    }
}
