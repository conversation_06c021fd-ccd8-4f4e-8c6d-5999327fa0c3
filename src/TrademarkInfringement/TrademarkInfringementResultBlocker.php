<?php

declare(strict_types=1);

namespace App\TrademarkInfringement;

use App\Project\Config\ProjectConfigRegistryInterface;

class TrademarkInfringementResultBlocker
{
    private bool $blockResults;

    public function __construct(
        private readonly TrademarkInfringementRuleMatcher $trademarkInfringementRuleMatcher,
        private readonly ProjectConfigRegistryInterface $projectConfigRegistry
    )
    {
    }

    public function blockResults(): bool
    {
        if (!isset($this->blockResults)) {
            $this->blockResults = false;

            $trademarkInfringementRules = $this->projectConfigRegistry
                ->getProjectConfig()
                ->getTrademarkInfringementConfig()
                ->getChildren('rules');

            foreach ($trademarkInfringementRules as $ruleReader) {
                if ($this->trademarkInfringementRuleMatcher->matchesRule($ruleReader)) {
                    $this->blockResults = true;

                    break;
                }
            }
        }

        return $this->blockResults;
    }
}
