<?php

declare(strict_types=1);

namespace App\Project\Config;

use Visymo\ArrayReader\ArrayReader;

final readonly class ProjectConfig
{
    public function __construct(
        private ArrayReader $config
    )
    {
    }

    public function getContentPageConfig(): ArrayReader
    {
        return $this->config->getChild('content_page');
    }

    public function getTrademarkInfringementConfig(): ArrayReader
    {
        return $this->config->getChild('trademark_infringement');
    }
}
