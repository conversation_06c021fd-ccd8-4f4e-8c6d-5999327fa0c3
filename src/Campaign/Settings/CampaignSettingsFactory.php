<?php

declare(strict_types=1);

namespace App\Campaign\Settings;

readonly class CampaignSettingsFactory
{
    public function __construct(
        private GoogleAdSenseFactory $googleAdSenseFactory
    )
    {
    }

    /**
     * @param mixed[] $campaignConfig
     */
    public function create(array $campaignConfig): CampaignSettings
    {
        return new CampaignSettings(
            name         : $campaignConfig[CampaignSettings::KEY_NAME],
            domain       : $campaignConfig[CampaignSettings::KEY_DOMAIN] ?? null,
            googleAdSense: $this->getGoogleAdSense($campaignConfig),
        );
    }

    /**
     * @param mixed[] $campaignConfig
     */
    private function getGoogleAdSense(array $campaignConfig): ?GoogleAdSense
    {
        if (!array_key_exists(CampaignSettings::KEY_GOOGLE_ADSENSE, $campaignConfig)) {
            return null;
        }

        return $this->googleAdSenseFactory->create($campaignConfig[CampaignSettings::KEY_GOOGLE_ADSENSE]);
    }
}
