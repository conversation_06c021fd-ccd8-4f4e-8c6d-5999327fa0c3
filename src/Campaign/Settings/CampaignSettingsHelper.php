<?php

declare(strict_types=1);

namespace App\Campaign\Settings;

use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;

class CampaignSettingsHelper
{
    private ?CampaignSettings $campaignSettings = null;

    private bool $isInitialized = false;

    public function __construct(
        private readonly WebsiteConfigurationHelper $websiteConfigurationHelper,
        private readonly CampaignSettingsFactory $campaignSettingsFactory,
        private readonly ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper
    )
    {
    }

    public function getSettings(): ?CampaignSettings
    {
        if ($this->isInitialized) {
            return $this->campaignSettings;
        }

        $this->isInitialized = true;
        $accountId = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->accountId;
        $campaignName = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->campaignName;

        if ($accountId === null || $campaignName === null) {
            return $this->campaignSettings;
        }

        $campaignConfig = $this->websiteConfigurationHelper->getConfiguration()->getCampaignConfig(
            accountId   : $accountId,
            campaignName: $campaignName,
        );

        if ($campaignConfig !== null) {
            $this->campaignSettings = $this->campaignSettingsFactory->create($campaignConfig);
        }

        return $this->campaignSettings;
    }
}
