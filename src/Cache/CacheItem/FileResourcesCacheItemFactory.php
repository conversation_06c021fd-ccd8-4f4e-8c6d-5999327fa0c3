<?php

declare(strict_types=1);

namespace App\Cache\CacheItem;

use Symfony\Component\Config\ConfigCache;
use Symfony\Component\Config\Resource\FileResource;
use Symfony\Component\HttpKernel\KernelInterface;
use Visymo\Serializer\NativePhpIncludeFileSerializer;

final readonly class FileResourcesCacheItemFactory
{
    private const string CACHE_FILE_TEMPLATE = '%s/config-cache/%s.php';

    public function __construct(
        private KernelInterface $kernel,
        private NativePhpIncludeFileSerializer $nativePhpIncludeFileSerializer
    )
    {
    }

    /**
     * @param string[] $resources
     */
    public function create(string $key, array $resources): ResourcesCacheItem
    {
        $cacheFileName = $this->getCacheFileName($key);
        $configCache = new ConfigCache($cacheFileName, $this->kernel->isDebug());
        $fileResources = array_map(static fn (string $resource): FileResource => new FileResource($resource), $resources);

        return new ResourcesCacheItem(
            $configCache,
            $this->nativePhpIncludeFileSerializer,
            $fileResources,
        );
    }

    private function getCacheFileName(string $key): string
    {
        return sprintf(self::CACHE_FILE_TEMPLATE, $this->kernel->getCacheDir(), $key);
    }
}
