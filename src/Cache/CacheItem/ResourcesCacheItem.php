<?php

declare(strict_types=1);

namespace App\Cache\CacheItem;

use Symfony\Component\Config\ConfigCache;
use Symfony\Component\Config\Resource\ResourceInterface;
use Visymo\Serializer\NativePhpIncludeFileSerializer;

final readonly class ResourcesCacheItem
{
    /**
     * @param ResourceInterface[] $resources
     */
    public function __construct(
        private ConfigCache $configCache,
        private NativePhpIncludeFileSerializer $nativePhpIncludeFileSerializer,
        private array $resources
    )
    {
    }

    public function isFresh(): bool
    {
        return $this->configCache->isFresh();
    }

    /**
     * @return mixed[]
     */
    public function read(): array
    {
        return include $this->configCache->getPath();
    }

    /**
     * @param mixed[] $data
     */
    public function write(array $data): void
    {
        $content = $this->nativePhpIncludeFileSerializer->serialize($data);
        $this->configCache->write($content, $this->resources);
    }
}
