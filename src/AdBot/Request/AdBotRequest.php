<?php

declare(strict_types=1);

namespace App\AdBot\Request;

use App\AdBot\Bot\AdBot;
use App\Debug\Request\DebugRequestInterface;
use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Request\Manager\RequestManagerInterface;

final class AdBotRequest implements AdBotRequestInterface
{
    private bool $isAdBot;

    private ?AdBot $adBot = null;

    private bool $adBotInitialized = false;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestInfoInterface $requestInfo,
        private readonly DebugRequestInterface $debugRequest
    )
    {
    }

    public function isAdBot(): bool
    {
        if (!isset($this->isAdBot)) {
            // DebugRequest could only return true for office requests
            $this->isAdBot = $this->debugRequest->isAdBot()
                             || $this->requestManager->headersBag()->getBool(self::HEADER_X_LOADBALANCER_IS_AD_BOT);
        }

        return $this->isAdBot;
    }

    public function getAdBot(): ?AdBot
    {
        if (!$this->isAdBot()) {
            return null;
        }

        if (!$this->adBotInitialized) {
            $this->adBotInitialized = true;
            $this->adBot = AdBot::tryFromUserAgent($this->requestInfo->getUserAgent());
        }

        return $this->adBot;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_IS_AD_BOT => $this->isAdBot(),
            self::KEY_AD_BOT    => $this->getAdBot()?->value,
        ];
    }
}
