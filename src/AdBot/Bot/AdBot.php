<?php

declare(strict_types=1);

namespace App\AdBot\Bot;

enum AdBot: string
{
    case GOOGLE    = 'google';
    case MICROSOFT = 'microsoft';

    public static function tryFromUserAgent(string $userAgent): ?self
    {
        if (str_contains($userAgent, 'AdsBot-Google')) {
            return self::GOOGLE;
        }

        if (str_contains($userAgent, 'adidxbot')) {
            return self::MICROSOFT;
        }

        return null;
    }
}
