<?php

declare(strict_types=1);

namespace App\Template\DelayedContainer\StatisticsProvider;

use App\Statistics\Provider\AbstractStatisticsProvider;

class DelayedContainerStatisticsProvider extends AbstractStatisticsProvider
{
    public function __construct(
        DelayedContainerStatisticsResolver $delayedContainerStatisticsResolver
    )
    {
        parent::__construct(
            $delayedContainerStatisticsResolver,
        );
    }

    public static function getContextKey(): string
    {
        return 'delayed_container';
    }

    public static function getPayloadKey(): string
    {
        return 'dc';
    }
}
