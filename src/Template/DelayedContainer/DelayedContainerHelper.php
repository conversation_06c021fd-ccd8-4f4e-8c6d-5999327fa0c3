<?php

declare(strict_types=1);

namespace App\Template\DelayedContainer;

use App\BingAds\Helper\BingAdsHelper;
use App\Generic\Device\Device;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\Http\Request\Info\RequestInfoInterface;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\Tracking\Helper\TrafficHelper;
use App\Tracking\Model\TrafficSource;

final readonly class DelayedContainerHelper
{
    public function __construct(
        private GoogleCsaRegistry $googleCsaRegistry,
        private BingAdsHelper $bingAdsHelper,
        private TrafficHelper $trafficHelper,
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private RequestInfoInterface $requestInfo
    )
    {
    }

    public function isDelayedContainerRequired(): bool
    {
        if (!$this->trafficHelper->isPaidTraffic()) {
            return false;
        }

        $hasGoogleCsaUnits = (bool)$this->googleCsaRegistry->getGoogleCsa()?->hasUnits();

        if ($hasGoogleCsaUnits) {
            return true;
        }

        if ($this->bingAdsHelper->hasUnits()) {
            return true;
        }

        return false;
    }

    public function showDelayedContainerLoader(): bool
    {
        if (!$this->isDelayedContainerRequired()) {
            return false;
        }

        $activeTrackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();

        if ($activeTrackingEntry->device !== Device::MOBILE) {
            return false;
        }

        // Prevent TikTok traffic from showing the loader due to A/B tests
        if ($activeTrackingEntry->trafficSource === TrafficSource::TIKTOK) {
            return false;
        }

        return $this->requestInfo->isRoute('route_display_search_related') ||
               $this->requestInfo->isRoute('route_display_search_related_web');
    }
}
