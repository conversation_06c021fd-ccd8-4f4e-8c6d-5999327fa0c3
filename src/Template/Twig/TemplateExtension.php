<?php

declare(strict_types=1);

namespace App\Template\Twig;

use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\Template\Event\RenderTemplateHtmlClassEvent;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class TemplateExtension extends AbstractExtension
{
    /** @var string[] */
    private array $templateHtmlClasses = [];

    public function __construct(
        private readonly EventDispatcherInterface $eventDispatcher
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('register_template_html_classes', $this->registerTemplateHtmlClasses(...)),
            new TwigFunction('render_template_html_class', $this->renderTemplateHtmlClass(...)),
            new TwigFunction('render_template_headers', $this->renderTemplateHeaders(...), ['is_safe' => ['html']]),
            new TwigFunction('render_template_footers', $this->renderTemplateFooters(...), ['is_safe' => ['html']]),
        ];
    }

    /**
     * @param string[] $classes
     */
    public function registerTemplateHtmlClasses(array $classes): void
    {
        foreach ($classes as $class) {
            $this->templateHtmlClasses[] = $class;
        }
    }

    public function renderTemplateHtmlClass(): string
    {
        $event = new RenderTemplateHtmlClassEvent();
        $this->eventDispatcher->dispatch($event, RenderTemplateHtmlClassEvent::NAME);

        $items = [
            ...$this->templateHtmlClasses,
            ...$event->getItems(),
        ];

        return implode(' ', $items);
    }

    public function renderTemplateHeaders(): string
    {
        $event = new RenderTemplateHeadersEvent();
        $this->eventDispatcher->dispatch($event, RenderTemplateHeadersEvent::NAME);

        return implode('', $event->getItems());
    }

    public function renderTemplateFooters(): string
    {
        $event = new RenderTemplateFootersEvent();
        $this->eventDispatcher->dispatch($event, RenderTemplateFootersEvent::NAME);

        return implode('', $event->getItems());
    }
}
