<?php

declare(strict_types=1);

namespace App\Template\Twig;

use App\Debug\Request\DebugRequestInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

class DateTimeExtension extends AbstractExtension
{
    public function __construct(
        private readonly DebugRequestInterface $debugRequest,
        private readonly DateTimeFactory $dateTimeFactory
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'current_year',
                $this->getCurrentYear(...),
                [
                    'is_safe' => ['html'],
                ],
            ),
        ];
    }

    public function getCurrentYear(): int
    {
        if ($this->debugRequest->showFixedStats()) {
            return 2023;
        }

        return (int)$this->dateTimeFactory->createNow()->format('Y');
    }
}
