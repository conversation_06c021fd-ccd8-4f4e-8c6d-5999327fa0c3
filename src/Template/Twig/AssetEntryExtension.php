<?php

declare(strict_types=1);

namespace App\Template\Twig;

use App\Assets\AssetsHelper;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class AssetEntryExtension extends AbstractExtension
{
    public function __construct(
        private readonly LocaleSettingsHelperInterface $localeSettingsHelper,
        private readonly AssetsHelper $assetsHelper
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('entry_style', $this->entryStyle(...), ['is_safe' => ['html']]),
            new TwigFunction('brand_entry_style', $this->brandEntryStyle(...), ['is_safe' => ['html']]),
            new TwigFunction('entry_javascript', $this->entryJavaScript(...), ['is_safe' => ['html']]),
        ];
    }

    public function entryStyle(string $entryStyleName): string
    {
        $locale = $this->localeSettingsHelper->getSettings()->locale;
        $fileContents = $this->assetsHelper->getEntryCssFileContents(
            entryStyleName: $entryStyleName,
            rightToLeft   : $locale->language->isRightToLeft,
        );

        return (string)$this->assetsHelper->renderStyleContent($fileContents);
    }

    public function brandEntryStyle(string $entryStyleName): string
    {
        $locale = $this->localeSettingsHelper->getSettings()->locale;
        $fileContents = $this->assetsHelper->getBrandEntryCssFileContents(
            entryStyleName: $entryStyleName,
            rightToLeft   : $locale->language->isRightToLeft,
        );

        return (string)$this->assetsHelper->renderStyleContent($fileContents);
    }

    public function entryJavaScript(
        string $entryJavaScriptName,
        bool $scriptTags = true,
        bool $required = false
    ): string
    {
        $fileContents = $required
            ? $this->assetsHelper->getRequiredJavaScriptEntryFileContents($entryJavaScriptName)
            : $this->assetsHelper->getJavaScriptEntryFileContents($entryJavaScriptName);

        if ($scriptTags) {
            return (string)$this->assetsHelper->renderJavaScriptContent($fileContents);
        }

        return (string)$fileContents;
    }
}
