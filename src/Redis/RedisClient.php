<?php

declare(strict_types=1);

namespace App\Redis;

/**
 * @mixin \Redis|\RedisCluster
 */
class RedisClient
{
    private const float TIMEOUT      = 2.0;
    private const float READ_TIMEOUT = 2.0;

    private \Redis|\RedisCluster|null $client = null;

    public function __construct(private readonly string $dsn, private readonly bool $clusterEnabled)
    {
    }

    protected function initClient(): void
    {
        if ($this->clusterEnabled) {
            $this->initClusterClient();
        } else {
            $this->initStandaloneClient();
        }
    }

    protected function initClusterClient(): void
    {
        try {
            $seeds = array_map(
                static function ($dsn): string {
                    $dsnParsed = parse_url($dsn);

                    /** @phpstan-ignore-next-line */
                    return sprintf('%s:%u', $dsnParsed['host'], $dsnParsed['port']);
                },
                explode(',', $this->dsn),
            );

            $this->client = new \RedisCluster(null, $seeds, self::TIMEOUT, self::READ_TIMEOUT);
        } catch (\Throwable $exception) {
            throw RedisClientException::previousWithDsn($exception, $this->dsn);
        }
    }

    protected function initStandaloneClient(): void
    {
        try {
            $dsnParsed = parse_url($this->dsn);

            $this->client = new \Redis();

            $this->client->connect(
                $dsnParsed['host'] ?? '',
                $dsnParsed['port'] ?? 0,
                self::TIMEOUT,
                null,
                0,
                self::READ_TIMEOUT,
            );

            $this->client->select(0);
        } catch (\Throwable $exception) {
            throw RedisClientException::previousWithDsn($exception, $this->dsn);
        }
    }

    /**
     * @param mixed[] $arguments
     *
     * @return bool|int|float|string|mixed[]
     */
    public function __call(string $name, array $arguments): bool|int|float|string|array
    {
        if ($this->client === null) {
            $this->initClient();
        }

        /** @phpstan-ignore-next-line */
        return $this->client->{$name}(...$arguments);
    }
}
