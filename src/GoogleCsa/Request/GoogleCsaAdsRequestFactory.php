<?php

declare(strict_types=1);

namespace App\GoogleCsa\Request;

use App\GoogleCsa\StyleId\GoogleCsaStyleIdParameterInterface;
use App\JsonTemplate\View\ViewInterface;
use App\Monetization\Settings\MonetizationSettings;
use App\Search\Request\SearchRequestInterface;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;

final readonly class GoogleCsaAdsRequestFactory
{
    public function __construct(
        private GoogleCsaStyleIdParameterInterface $googleCsaStyleIdParameter,
        private SearchRequestInterface $searchRequest,
        private WebsiteSettingsHelper $websiteSettingsHelper,
        private MonetizationSettings $monetizationSettings
    )
    {
    }

    public function createFromView(ViewInterface $view): ?GoogleCsaAdsRequest
    {
        if (!$this->monetizationSettings->adsEnabled) {
            return null;
        }

        $googleCsaViewDataRequest = $view->getDataRequest()->googleCsa();

        if ($googleCsaViewDataRequest->getTopAdAmount() === null
            && $googleCsaViewDataRequest->getBottomAdAmount() === null
        ) {
            return null;
        }

        $isGoogleAfsOnline = $this->websiteSettingsHelper->getSettings()
            ->getGoogleAdSense()
            ->getContractType()
            ->isOnline();

        return new GoogleCsaAdsRequest(
            numRepeated     : (int)$googleCsaViewDataRequest->getNumRepeated(),
            page            : $this->searchRequest->getPage(),
            styleId         : $this->googleCsaStyleIdParameter->getStyleId(),
            topAmount       : $googleCsaViewDataRequest->getTopAdAmount(),
            topContainer    : $googleCsaViewDataRequest->getTopAdContainer(),
            bottomAmount    : $googleCsaViewDataRequest->getBottomAdAmount(),
            bottomContainer : $googleCsaViewDataRequest->getBottomAdContainer(),
            addClickTrackUrl: !$isGoogleAfsOnline,
        );
    }
}
