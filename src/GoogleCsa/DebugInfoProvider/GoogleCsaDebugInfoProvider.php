<?php

declare(strict_types=1);

namespace App\GoogleCsa\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;
use App\GoogleCsa\Registry\GoogleCsaRegistry;

class GoogleCsaDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_GOOGLE_CSA_REQUEST = 'google csa request';

    public function __construct(
        private readonly GoogleCsaRegistry $googleCsaRegistry
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        $googleCsa = $this->googleCsaRegistry->getGoogleCsa();

        if ($googleCsa === null) {
            return [];
        }

        $debugInfo = [
            'meta' => [
                'csa_type'                => $googleCsa->getType()->value,
                'ads_amount'              => $googleCsa->ads()->getTotalAdsAmount(),
                'unique_ads_amount'       => $googleCsa->ads()->getTotalUniqueAdsAmount(),
                'related_searches_amount' => $googleCsa->relatedSearch()->getTotalRelatedSearchAmount(),
                'js_loaded_callback'      => $googleCsa->getJsLoadedCallback(),
            ],
        ];
        $debugInfo = [
            ...$debugInfo,
            ...$googleCsa->toArray(),
        ];

        return [
            new DebugInfo(
                self::KEY_GOOGLE_CSA_REQUEST,
                [$debugInfo],
            ),
        ];
    }

    public static function getDefaultPriority(): int
    {
        return 80;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_GOOGLE_CSA_REQUEST,
        ];
    }
}
