<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration\Cache;

use App\WebsiteSettings\Configuration\Cache\Accounts\AccountsChunkBuilderFactory;
use App\WebsiteSettings\Configuration\Cache\Accounts\AccountsChunkSerializer;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

readonly class CachedWebsiteConfigurationFileWriter
{
    public function __construct(
        private CachedWebsiteConfigurationFileFactory $cachedWebsiteConfigurationFileFactory,
        private AccountsChunkBuilderFactory $accountsChunkBuilderFactory,
        private AccountsChunkSerializer $accountsChunkSerializer,
        private CachedWebsiteConfigurationFileCleaner $cachedWebsiteConfigurationFileCleaner
    )
    {
    }

    /**
     * @param mixed[] $config
     */
    public function write(string $brandSlug, array $config, bool $useOpCache = true): void
    {
        // Import to local file
        $localConfigurationFile = $this->cachedWebsiteConfigurationFileFactory->createLocalFile(
            brandSlug : $brandSlug,
            useOpcache: $useOpCache,
        );

        $newChunkFileVersion = $this->getChunkVersion($localConfigurationFile);

        // Build and write account chunks
        $accountsChunkBuilders = $this->accountsChunkBuilderFactory->createCollection(
            accounts      : $config[WebsiteConfiguration::KEY_ACCOUNTS],
            accountsConfig: $config[WebsiteConfiguration::KEY_ACCOUNT_CONFIG],
        );
        $accountsChunksIndex = [];
        $excludeFilesFromCleanup = [];

        foreach ($accountsChunkBuilders as $accountsChunkBuilderIndex => $accountsChunkBuilder) {
            $localAccountsChunkFile = $this->cachedWebsiteConfigurationFileFactory->createLocalAccountsChunkFile(
                brandSlug       : $brandSlug,
                chunkFileVersion: $newChunkFileVersion,
                chunkIndex      : $accountsChunkBuilderIndex,
                useOpcache      : $useOpCache,
            );
            $localAccountsChunkFile->writeContent(
                [
                    WebsiteConfiguration::KEY_ACCOUNTS       => $accountsChunkBuilder->getAccounts(),
                    WebsiteConfiguration::KEY_ACCOUNT_CONFIG => $accountsChunkBuilder->getAccountsConfigData(),
                ],
            );

            $accountsChunk = $accountsChunkBuilder->buildChunk(
                $localAccountsChunkFile->getFileName(),
            );

            $accountsChunksIndex[] = $this->accountsChunkSerializer->serialize($accountsChunk);
            $excludeFilesFromCleanup[] = $accountsChunk->filename;
        }

        $config[CachedWebsiteConfigurationKey::CHUNK_FILE_VERSION] = $newChunkFileVersion;
        $config[WebsiteConfiguration::KEY_ACCOUNTS] = [];
        $config[CachedWebsiteConfigurationKey::ACCOUNTS_CHUNKED] = $accountsChunksIndex;

        // Write new main config
        // This will also clear opcache and instantly activate new config
        $localConfigurationFile->writeContent($config);

        // Clean older config
        $this->cachedWebsiteConfigurationFileCleaner->clean($brandSlug, $excludeFilesFromCleanup, $useOpCache);
    }

    private function getChunkVersion(SerializedFileInterface $localConfigurationFile): int
    {
        // Determine current/new chunk file version
        // Current local file might not exist. It might exist but not have a version yet.
        $currentConfig = $localConfigurationFile->exists()
            ? $localConfigurationFile->getContents()
            : [];

        $chunkVersion = (int)($currentConfig[CachedWebsiteConfigurationKey::CHUNK_FILE_VERSION] ?? 0);
        $chunkVersion++;

        return $chunkVersion;
    }
}
