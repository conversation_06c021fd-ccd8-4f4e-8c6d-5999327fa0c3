<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration\Cache\Accounts;

class AccountsChunkSerializer
{
    public const string KEY_ACCOUNT_ID_FIRST    = 'account_id_first';
    public const string KEY_ACCOUNT_ID_LAST     = 'account_id_last';
    public const string KEY_AMOUNT_OF_ACCOUNTS  = 'amount_of_accounts';
    public const string KEY_AMOUNT_OF_CAMPAIGNS = 'amount_of_campaigns';
    public const string KEY_FILENAME            = 'filename';

    /**
     * @return array<string, mixed>
     */
    public function serialize(AccountsChunk $accountsChunk): array
    {
        return [
            self::KEY_ACCOUNT_ID_FIRST    => $accountsChunk->accountIdFirst,
            self::KEY_ACCOUNT_ID_LAST     => $accountsChunk->accountIdLast,
            self::KEY_AMOUNT_OF_ACCOUNTS  => $accountsChunk->amountOfAccounts,
            self::KEY_AMOUNT_OF_CAMPAIGNS => $accountsChunk->amountOfCampaigns,
            self::KEY_FILENAME            => $accountsChunk->filename,
        ];
    }

    /**
     * @param array<string, mixed> $data
     */
    public function deserialize(array $data): AccountsChunk
    {
        return new AccountsChunk(
            $data[self::KEY_ACCOUNT_ID_FIRST],
            $data[self::KEY_ACCOUNT_ID_LAST],
            $data[self::KEY_AMOUNT_OF_ACCOUNTS],
            $data[self::KEY_AMOUNT_OF_CAMPAIGNS],
            $data[self::KEY_FILENAME],
        );
    }
}
