<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings;

use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Settings\BingAds\BingAdsSettingsFactory;
use App\WebsiteSettings\Settings\ConversionLog\ConversionLogSettingsFactory;
use App\WebsiteSettings\Settings\ConversionTracking\ConversionTrackingSettingsFactory;
use App\WebsiteSettings\Settings\GoogleAdsConversionTracking\GoogleAdsConversionTrackingSettingsFactory;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettingsFactory;
use App\WebsiteSettings\Settings\MicrosoftAdsConversionTracking\MicrosoftAdsConversionTrackingSettingsFactory;

final readonly class WebsiteSettingsFactory
{
    public function __construct(
        private ConversionLogSettingsFactory $conversionLogSettingsFactory,
        private ConversionTrackingSettingsFactory $conversionTrackingSettingsFactory,
        private BingAdsSettingsFactory $bingAdsSettingsFactory,
        private GoogleAdSenseSettingsFactory $googleAdSenseSettingsFactory,
        private GoogleAdsConversionTrackingSettingsFactory $googleAdsConversionTrackingSettingsFactory,
        private MicrosoftAdsConversionTrackingSettingsFactory $microsoftAdsConversionTrackingSettingsFactory
    )
    {
    }

    public function create(
        WebsiteConfiguration $websiteConfiguration,
        string $domain,
        ?int $accountId
    ): WebsiteSettings
    {
        $brandConfig = $websiteConfiguration->getBrandConfig();
        $domainConfig = $websiteConfiguration->getDomainConfig($domain);

        $accountConfig = $accountId !== null ? $websiteConfiguration->getAccountConfig($accountId) : null;

        $googleAdSenseSettings = $this->googleAdSenseSettingsFactory->create(
            $brandConfig,
            $domainConfig,
            $accountConfig,
        );

        return new WebsiteSettings(
            $this->conversionLogSettingsFactory->create(
                $brandConfig,
                $domainConfig,
                $accountConfig,
            ),
            $this->conversionTrackingSettingsFactory->create(
                $accountConfig,
            ),
            $this->bingAdsSettingsFactory->create(
                $brandConfig,
                $domainConfig,
                $accountConfig,
            ),
            $googleAdSenseSettings,
            $this->googleAdsConversionTrackingSettingsFactory->create(
                $brandConfig,
                $domainConfig,
                $accountConfig,
            ),
            $this->microsoftAdsConversionTrackingSettingsFactory->create(
                $brandConfig,
                $domainConfig,
                $accountConfig,
            ),
        );
    }
}
