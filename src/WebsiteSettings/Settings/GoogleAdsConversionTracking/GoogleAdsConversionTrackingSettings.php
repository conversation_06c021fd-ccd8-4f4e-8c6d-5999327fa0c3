<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\GoogleAdsConversionTracking;

use App\WebsiteSettings\Settings\Module\AbstractModuleSettings;

class GoogleAdsConversionTrackingSettings extends AbstractModuleSettings
{
    public const string KEY_CONVERSION_TRACKING_ID    = 'conversion_tracking_id';
    public const string KEY_CONVERSION_TRACKING_LABEL = 'conversion_tracking_label';

    public function __construct(
        bool $enabled,
        protected int $conversionTrackingId,
        protected ?string $conversionTrackingLabel
    )
    {
        $this->enabled = $enabled;
    }

    public function getConversionTrackingId(): int
    {
        return $this->conversionTrackingId;
    }

    public function getConversionTrackingLabel(): ?string
    {
        return $this->conversionTrackingLabel;
    }

    /**
     * @inheritDoc
     */
    protected function propertiesToArray(): array
    {
        return [
            self::KEY_CONVERSION_TRACKING_ID    => $this->getConversionTrackingId(),
            self::KEY_CONVERSION_TRACKING_LABEL => $this->getConversionTrackingLabel(),
        ];
    }
}
