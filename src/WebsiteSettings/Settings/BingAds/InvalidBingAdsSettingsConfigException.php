<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\BingAds;

use App\WebsiteSettings\Settings\Exception\InvalidWebsiteSettingsConfigException;

class InvalidBingAdsSettingsConfigException extends InvalidWebsiteSettingsConfigException
{
    public static function create(\Throwable $previous): self
    {
        return new self(
            sprintf('Invalid Bing Ads settings config given: %s', $previous->getMessage()),
            0,
            $previous,
        );
    }
}
