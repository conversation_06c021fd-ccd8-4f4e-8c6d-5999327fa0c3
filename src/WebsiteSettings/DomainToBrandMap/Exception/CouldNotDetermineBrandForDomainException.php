<?php

declare(strict_types=1);

namespace App\WebsiteSettings\DomainToBrandMap\Exception;

class CouldNotDetermineBrandForDomainException extends \RuntimeException
{
    public static function create(string $domain, ?\Exception $previous = null): self
    {
        return new self(
            sprintf(
                'Could not determine brand for domain "%s"',
                $domain,
            ),
            0,
            $previous,
        );
    }
}
