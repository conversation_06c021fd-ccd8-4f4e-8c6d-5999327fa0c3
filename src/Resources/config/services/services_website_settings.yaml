# Symfony best practices for a bundle dictates that all services are defined manually instead of using autowiring.
# However, this introduces a lot of extra work. Since this bundle is only used internally, we've chosen to use
# autowiring.
#
# See: https://symfony.com/doc/current/bundles/best_practices.html#services
parameters:
    brand_config_json_schema_file_path: '%brand_website.resources_dir%/config/schemas/brand_config.schema.json'
    clean_old_brand_websites_config: false
    project_config_json_schema_file_path: '%brand_website.resources_dir%/config/schemas/project_config.schema.json'

services:
    _defaults:
        autowire: true
        autoconfigure: true
        bind:
            $websiteConfigurationJsonSchemaValidator: '@brand_website.website_settings.website_configuration_json_schema_validator'
            $importBrandConfigFileIterator: '@brand_website.website_settings.import_brand_config_file_iterator'
            $domainToBrandMapSerializedFile: '@brand_website.website_settings.domain_to_brand_map.serialized_file'
            $projectConfigPhpFile: '@brand_website.project_config.php_file'

    App\WebsiteSettings\:
        resource: '../../../WebsiteSettings'

    _instanceof:
        App\WebsiteSettings\Settings\Module\AdProviderSettingsFactoryInterface:
            tags: [ 'brand_website.website_settings.ad_provider_settings_factory' ]

    # Local domain-to-brand config map
    brand_website.website_settings.domain_to_brand_map.serialized_file:
        class: Visymo\Filesystem\Opcache\OpcachedNativePhpIncludeFile
        factory: [ '@Visymo\Filesystem\Opcache\OpcachedNativePhpIncludeFileFactory', 'create' ]
        arguments:
            $filePath: '%config_local_path%/domain-to-brand-map.php'

    # Manual entries to enable autocompletion when used as twig globals
    App\WebsiteSettings\Settings\WebsiteSettingsHelper: ~

    # Website configuration JSON schema validator
    brand_website.website_settings.website_configuration_json_schema_validator:
        class: Visymo\Shared\Domain\Validator\JsonSchemaValidator
        factory: [ '@Visymo\Shared\Infrastructure\Bridge\Opis\OpisJsonSchemaValidatorFactory', 'create' ]
        arguments:
            $schemaFilePath: '%brand_config_json_schema_file_path%'

    # Import brand config files
    brand_website.website_settings.import_brand_config_file_iterator:
        class: Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIterator
        arguments:
            $globPattern: '%config_path%/artemis/brand-config/*.json'
            $serializedFileFactory: '@Visymo\Filesystem\SerializedFile\Type\NativePrettyJsonFileFactory'

    # Local brand config files
    brand_website.website_settings.local_brand_config_file_iterator:
        class: Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIterator
        arguments:
            $globPattern: '%config_local_path%/artemis/brand-config/*.php'
            $serializedFileFactory: '@Visymo\Filesystem\Opcache\OpcachedNativePhpIncludeFileFactory'

    brand_website.website_settings.native_local_brand_config_file_iterator:
        class: Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIterator
        arguments:
            $globPattern: '%config_local_path%/artemis/brand-config/*.php'
            $serializedFileFactory: '@Visymo\Filesystem\SerializedFile\Type\NativePhpIncludeFileFactory'

    App\WebsiteSettings\Console\ImportConfigConsole:
        arguments:
            $cleanOldConfigs: '%clean_old_brand_websites_config%'
            $serializedFileFactory: '@Visymo\Filesystem\SerializedFile\SerializedFileFactory'

    App\WebsiteSettings\Configuration\WebsiteConfigurationFileRepository:
        arguments:
            $localBrandConfigFileIterator: '@brand_website.website_settings.local_brand_config_file_iterator'
            $nativeLocalBrandConfigFileIterator: '@brand_website.website_settings.native_local_brand_config_file_iterator'

    Visymo\Filesystem\SerializedFile\SerializedFileFactory:
        arguments:
            $arraySerializer: '@Visymo\Serializer\NativeJsonArraySerializer'

    # Project config
    brand_website.project_config.json_schema_validator:
        class: Visymo\Shared\Domain\Validator\JsonSchemaValidator
        factory: [ '@Visymo\Shared\Infrastructure\Bridge\Opis\OpisJsonSchemaValidatorFactory', 'create' ]
        arguments:
            $schemaFilePath: '%project_config_json_schema_file_path%'

    brand_website.project_config.json_file:
        class: Visymo\Filesystem\SerializedFile\SerializedFileInterface
        factory: [ '@Visymo\Filesystem\SerializedFile\Type\NativePrettyJsonFileFactory', 'create' ]
        arguments:
            $filePath: '%config_path%/artemis/project/brand-websites.json'

    brand_website.project_config.php_file:
        class: Visymo\Filesystem\SerializedFile\SerializedFileInterface
        factory: [ '@Visymo\Filesystem\Opcache\OpcachedNativePhpIncludeFileFactory', 'create' ]
        arguments:
            $filePath: '%config_local_path%/artemis/project/brand-websites.php'

    App\Project\Config\ProjectConfigRegistry: ~
    App\Project\Config\ProjectConfigImporter:
        arguments:
            $projectConfigJsonSchemaValidator: '@brand_website.project_config.json_schema_validator'
            $projectConfigJsonFile: '@brand_website.project_config.json_file'

    App\Debug\Helper\DebugProjectConfigFileHelper:
        arguments:
            $projectConfigJsonFile: '@brand_website.project_config.json_file'
