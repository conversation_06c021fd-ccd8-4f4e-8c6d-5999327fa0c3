brand_website.conversion_tracking:
    resource: ../../../ConversionTracking/Controller/
    type: attribute

brand_website.home:
    resource: ../../../Home/Controller/HomeController.php
    type: attribute

brand_website.image_search:
    resource: ../../../ImageSearch/Controller/
    type: attribute
    condition: "service('route_checker_image_search').check()"

brand_website.preferences:
    resource: ../../../Preferences/Controller/
    type: attribute

brand_website.news_search:
    resource: ../../../NewsSearch/Controller/
    type: attribute
    condition: "service('route_checker_news_search').check()"

brand_website.robots_txt:
    resource: ../../../RobotsTxt/Controller/
    type: attribute

brand_website.assets:
    resource: ../../../Assets/Controller/
    type: attribute

brand_website.keyword_ideas_search:
    resource: ../../../KeywordIdeasSearch/Controller/KeywordIdeasSearchController.php
    type: attribute

brand_website.auto_suggest:
    resource: ../../../AutoSuggest/Controller/
    type: attribute
    condition: "service('route_checker_auto_suggest').check()"

brand_website.health:
    resource: ../../../Health/Controller/
    type: attribute


# Legacy routes (https://jira.vinden.nl/browse/SERP-649)
legacy:
    resource: ./routes.legacy.yaml
    name_prefix: 'legacy_'
    controller: Symfony\Bundle\FrameworkBundle\Controller\RedirectController::redirectAction
    defaults:
        route: route_search
        # permanent redirection (code 301)
        permanent: true
        # keep the original query string parameters when redirecting
        keepQueryParams: true
