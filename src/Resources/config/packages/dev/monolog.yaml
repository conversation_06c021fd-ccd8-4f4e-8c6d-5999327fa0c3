monolog:
    handlers:
        main:
            type: rotating_file
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: info
            max_files: 5
            channels: [ "!event", "!request", "!translation", "!conversion", "!conversion_log", "!statistics", "!javascript_error", "!javascript_related_terms_view" ]

        console:
            type: console
            channels: [ "!event", "!doctrine", "!conversion", "!conversion_log", "!statistics", "!javascript_error", "!javascript_related_terms_view" ]
            formatter: monolog_extensions.console_formatter
