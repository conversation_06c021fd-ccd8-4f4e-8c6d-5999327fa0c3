<?php

declare(strict_types=1);

return [
    'page' => [
        'disableTextAdExtensions'   => ['logo'],

    ],
    'unit' => [
        'adStyle' => [
            'textAd'        => [
                'attributionBorderWidth'     => 0,
                'attributionFontSize'        => 14,
                'attributionPosition'        => 1,
                'boldTitle'                  => false,
                'boldUrl'                    => false,
                'borderColorForAd'           => '#DDDDDD',
                'borderRadiusForAd'          => 8,
                'descriptionColor'           => '#5F6368',
                'descriptionFontSize'        => 14,
                'descriptionLineHeight'      => 25,
                'domainLinkAboveDescription' => true,
                'fontFamily'                 => 'Arial',
                'horizontalSpacing'          => 0,
                'paddingBottomForAd'         => 12,
                'paddingLeftForAd'           => 16,
                'paddingRightForAd'          => 16,
                'paddingTopForAd'            => 12,
                'titleColor'                 => '#1558D6',
                'titleFontSize'              => 20,
                'titleLineHeight'            => 25,
                'topAttributionColor'        => '#A0A0B0',
                'urlColor'                   => '#5F6368',
                'urlFontSize'                => 14,
                'urlLineHeight'              => 25,
                'verticalSpacing'            => 10,
            ],
            'productAd'     => [],
            'textExtension' => [
                'enhancedSiteLinks' => [
                    'titleFontSize'         => 20,
                    'titleLineHeight'       => 25,
                    'descriptionFontSize'   => 14,
                    'descriptionLineHeight' => 22,
                ],
                'siteLinks'         => [
                    'fontSize'     => 16,
                    'layoutOption' => 1,
                    'lineHeight'   => 20,
                    'paddingLeft'  => 12,
                ],
            ],
        ],
    ],
];
