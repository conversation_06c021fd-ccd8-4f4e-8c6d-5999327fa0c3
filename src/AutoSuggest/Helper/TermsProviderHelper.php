<?php

declare(strict_types=1);

namespace App\AutoSuggest\Helper;

use App\AutoSuggest\Exception\NoTermsProviderEnabledException;
use App\AutoSuggest\Provider\BingTermsProvider;
use App\AutoSuggest\Provider\TermsProviderInterface;
use App\Search\Request\SearchRequestInterface;

readonly class TermsProviderHelper
{
    /**
     * @param \Traversable<TermsProviderInterface> $termProviders
     */
    public function __construct(
        private SearchRequestInterface $searchRequest,
        private iterable $termProviders
    )
    {
    }

    public function getTermsProvider(): TermsProviderInterface
    {
        /** @var TermsProviderInterface $termProvider */
        foreach ($this->termProviders as $termProvider) {
            if ($termProvider->isEnabled()) {
                return $termProvider;
            }
        }

        throw new NoTermsProviderEnabledException('No terms provider enabled');
    }

    public function getTermsProviderByRoute(): ?TermsProviderInterface
    {
        if ($this->searchRequest->isDisplaySearch() || $this->searchRequest->isDisplaySearchAdvertised()) {
            return $this->getTermsProviderByType(BingTermsProvider::TYPE);
        }

        return null;
    }

    private function getTermsProviderByType(string $type): ?TermsProviderInterface
    {
        foreach ($this->termProviders as $termProvider) {
            if ($termProvider->getType() === $type) {
                return $termProvider;
            }
        }

        return null;
    }
}
