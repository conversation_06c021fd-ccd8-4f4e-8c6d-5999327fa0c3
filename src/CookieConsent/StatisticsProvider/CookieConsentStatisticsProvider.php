<?php

declare(strict_types=1);

namespace App\CookieConsent\StatisticsProvider;

use App\Statistics\Provider\AbstractStatisticsProvider;

class CookieConsentStatisticsProvider extends AbstractStatisticsProvider
{
    public function __construct(
        CookieConsentStatisticsResolver $cookieConsentStatisticsResolver
    )
    {
        parent::__construct(
            $cookieConsentStatisticsResolver,
        );
    }

    public static function getContextKey(): string
    {
        return 'cookie_consent';
    }

    public static function getPayloadKey(): string
    {
        return 'cc';
    }
}
