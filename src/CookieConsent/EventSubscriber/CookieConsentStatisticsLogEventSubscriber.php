<?php

declare(strict_types=1);

namespace App\CookieConsent\EventSubscriber;

use App\CookieConsent\Helper\CookieConsentHelper;
use App\CookieConsent\StatisticsProvider\CookieConsentStatisticsProvider;
use App\OneTrust\Helper\OneTrustHelper;
use App\Statistics\Provider\Event\StatisticsLogCreateEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class CookieConsentStatisticsLogEventSubscriber implements EventSubscriberInterface
{
    private const string STATISTICS_KEY_CONSENT           = 'consent';
    private const string STATISTICS_KEY_REQUEST_TIMESTAMP = 'request_timestamp';

    public function __construct(
        private readonly CookieConsentHelper $cookieConsentHelper,
        private readonly OneTrustHelper $oneTrustHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            StatisticsLogCreateEvent::NAME => ['onStatisticsLogCreate'],
        ];
    }

    public function onStatisticsLogCreate(StatisticsLogCreateEvent $event): void
    {
        if (!$this->cookieConsentHelper->consent()) {
            return;
        }

        if ($this->oneTrustHelper->isEnabled()) {
            return;
        }

        $statistics = [
            CookieConsentStatisticsProvider::getContextKey() => [
                self::STATISTICS_KEY_CONSENT           => true,
                self::STATISTICS_KEY_REQUEST_TIMESTAMP => date('c'),
            ],
        ];

        $event->addStatistics(
            $statistics,
        );
    }
}
