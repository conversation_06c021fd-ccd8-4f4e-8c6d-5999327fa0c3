<?php

declare(strict_types=1);

namespace App\SharedStyle;

use App\SharedStyle\Exception\SharedStyleAssetNotFoundException;
use Visymo\ArrayReader\ArrayReader;
use Visymo\ArrayReader\ArrayReaderExceptionInterface;

final readonly class SharedStyle
{
    public function __construct(
        private ArrayReader $sharedStyleArrayReader
    )
    {
    }

    /**
     * @throws SharedStyleAssetNotFoundException
     */
    public function getAsset(string $filePath): string
    {
        $filePathParts = explode('/', ltrim($filePath, '/'));
        $fileName = array_pop($filePathParts);

        try {
            $pathArrayReader = $this->sharedStyleArrayReader;

            foreach ($filePathParts as $filePathPart) {
                $pathArrayReader = $pathArrayReader->getChild($filePathPart);
            }

            return $pathArrayReader->getString($fileName);
        } catch (ArrayReaderExceptionInterface $exception) {
            throw SharedStyleAssetNotFoundException::create($filePath, $exception);
        }
    }
}
