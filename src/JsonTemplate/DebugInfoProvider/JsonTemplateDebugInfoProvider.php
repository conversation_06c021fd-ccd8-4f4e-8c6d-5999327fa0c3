<?php

declare(strict_types=1);

namespace App\JsonTemplate\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Helper\DebugHelper;
use App\Debug\Info\DebugInfo;

class JsonTemplateDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_JSON_TEMPLATE_FILE = 'json template file';

    private const string KEY_JSON_TEMPLATE                        = 'json template';
    private const string KEY_JSON_TEMPLATE_VIEW_DATA_REQUIREMENTS = 'json template view data requirements';

    public function __construct(
        private readonly DebugHelper $debugHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        return array_filter(
            [
                $this->getJsonTemplateDebugInfo(),
                $this->getJsonTemplateFileDebugInfo(),
                $this->getJsonTemplateViewDataRequirementsDebugInfo(),
            ],
            static fn ($value) => $value !== null,
        );
    }

    private function getJsonTemplateDebugInfo(): ?DebugInfo
    {
        $jsonTemplateParsedConfig = $this->debugHelper->getJsonTemplateParsedConfig();

        if ($jsonTemplateParsedConfig === null) {
            return null;
        }

        return new DebugInfo(
            self::KEY_JSON_TEMPLATE,
            [
                $jsonTemplateParsedConfig,
            ],
        );
    }

    private function getJsonTemplateFileDebugInfo(): ?DebugInfo
    {
        $jsonTemplateLocation = $this->debugHelper->getJsonTemplateLocation();

        if ($jsonTemplateLocation === null) {
            return null;
        }

        return new DebugInfo(
            self::KEY_JSON_TEMPLATE_FILE,
            [
                $jsonTemplateLocation,
            ],
        );
    }

    private function getJsonTemplateViewDataRequirementsDebugInfo(): ?DebugInfo
    {
        $view = $this->debugHelper->getView();

        if ($view === null) {
            return null;
        }

        $usedViewDataRequirements = $view->getDataRequest()->getRequirements();

        return new DebugInfo(
            self::KEY_JSON_TEMPLATE_VIEW_DATA_REQUIREMENTS,
            [
                $usedViewDataRequirements,
            ],
        );
    }

    public static function getDefaultPriority(): int
    {
        return 100;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_JSON_TEMPLATE,
            self::KEY_JSON_TEMPLATE_FILE,
            self::KEY_JSON_TEMPLATE_VIEW_DATA_REQUIREMENTS,
        ];
    }
}
