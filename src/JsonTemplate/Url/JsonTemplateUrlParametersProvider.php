<?php

declare(strict_types=1);

namespace App\JsonTemplate\Url;

use App\Http\Url\AbstractCachedPersistentUrlParametersProvider;
use App\Http\Url\PersistentUrlParametersPageType;
use App\JsonTemplate\Request\JsonTemplateRequestInterface;
use App\JsonTemplate\View\JsonTemplateViewRegistry;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestInterface;

class JsonTemplateUrlParametersProvider extends AbstractCachedPersistentUrlParametersProvider
{
    public function __construct(
        private readonly JsonTemplateViewRegistry $jsonTemplateViewRegistry,
        private readonly JsonTemplateRequestInterface $jsonTemplateRequest,
        private readonly SearchRequestInterface $searchRequest,
        private readonly RouteRegistry $routeRegistry
    )
    {
    }

    /**
     * @inheritDoc
     */
    protected function createPersistentUrlParameters(PersistentUrlParametersPageType $pageType): array
    {
        $persistentUrlParameters = [];
        $templateVariant = $this->jsonTemplateViewRegistry->getView()?->getJsonTemplate()->variant;

        if ($this->routeRegistry->getSearchRoute() === 'route_display_search_related_web'
            && ($this->searchRequest->isDisplaySearchRelated() || $this->searchRequest->isDisplaySearchRelatedWeb())
        ) {
            $templateVariant = $this->jsonTemplateRequest->getTemplateVariant();
        }

        if ($templateVariant !== null) {
            $persistentUrlParameters = [
                JsonTemplateRequestInterface::PARAMETER_TEMPLATE_VARIANT => $templateVariant,
            ];
        }

        return match ($pageType) {
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            PersistentUrlParametersPageType::DEFAULT,
            PersistentUrlParametersPageType::NEW_SEARCH,
            PersistentUrlParametersPageType::RELATED_TERMS => $persistentUrlParameters,
        };
    }
}
