<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

class OrganicViewDataRequest implements SearchApiViewDataRequestInterface, SearchApiViewPageSizeDataRequestInterface
{
    private ?int $pageSize = null;

    /** @var string[]|null */
    private ?array $enabledSources = null;

    public static function getType(): string
    {
        return 'organic';
    }

    /**
     * @inheritDoc
     */
    public function mergeWith(array $registries): self
    {
        // merge pageSize without casting null values to int 0
        $pageSizes = [];

        foreach ($registries as $registry) {
            if ($registry->getPageSize() !== null) {
                $pageSizes[] = $registry->getPageSize();
            }
        }

        if ($pageSizes !== []) {
            $this->increasePageSize(max($pageSizes));
        }

        return $this;
    }

    public function isEnabled(): bool
    {
        return $this->getPageSize() !== null;
    }

    public function getPageSize(): ?int
    {
        return $this->pageSize;
    }

    public function increasePageSize(int $amount): self
    {
        $this->pageSize ??= 0;
        $this->pageSize += $amount;

        return $this;
    }

    public function setPageSize(int $pageSize): void
    {
        $this->pageSize = $pageSize;
    }

    /**
     * @return string[]|null
     */
    public function getEnabledSources(): ?array
    {
        return $this->enabledSources;
    }

    /**
     * @param string[]|null $enabledSources
     *
     * @return $this
     */
    public function setEnabledSources(?array $enabledSources): self
    {
        $this->enabledSources = $enabledSources;

        return $this;
    }

    public function canUseSameSearchApiRequest(SearchApiViewDataRequestInterface $viewDataRequest): bool
    {
        if (!$viewDataRequest instanceof self) {
            return false;
        }

        if ($this->getEnabledSources() !== $viewDataRequest->getEnabledSources()) {
            return false;
        }

        return true;
    }
}
