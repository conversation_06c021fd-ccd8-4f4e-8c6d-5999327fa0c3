<?php

declare(strict_types=1);

namespace App\JsonTemplate\View;

use App\Component\Generic\Container\ContainerComponent;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Template\JsonTemplate;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use Symfony\Component\HttpFoundation\Response;

interface ViewInterface
{
    public function getTwigTemplate(): string;

    public function getJsonTemplate(): JsonTemplate;

    public function getDataRequest(): ViewDataRequest;

    public function getDataRegistry(): ViewDataRegistry;

    public function getContainer(): ContainerComponent;

    /**
     * @return ComponentInterface[]
     */
    public function getComponents(): array;

    public function getResponse(): Response;
}
