<?php

declare(strict_types=1);

namespace App\JsonTemplate\View;

use App\Assets\AssetsHelper;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentRendererLocator;
use App\JsonTemplate\Component\ComponentRenderRegistry;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;
use App\JsonTemplate\Event\ComponentRenderedEvent;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Twig\Environment;

final readonly class JsonTemplateViewRenderer
{
    public function __construct(
        private ComponentRendererLocator $componentRendererLocator,
        private ComponentRenderRegistry $componentRenderRegistry,
        private LocaleSettingsHelperInterface $localeSettingsHelper,
        private AssetsHelper $assetsHelper,
        private Environment $twig,
        private EventDispatcherInterface $eventDispatcher
    )
    {
    }

    /**
     * @param ComponentInterface[] $components
     */
    public function renderComponents(array $components, ViewInterface $view): string
    {
        // To make sure that the Twig output buffer is used, all components need to be rendered
        // in a Twig template.
        // Not doing this will trigger issues with components
        return $this->twig->render(
            '@theme/user_interface/components.html.twig',
            [
                'components' => $components,
                'view'       => $view,
            ],
        );
    }

    public function renderComponent(ComponentInterface $component, ViewInterface $view): string
    {
        $this->eventDispatcher->dispatch(
            new ComponentRenderedEvent(
                component: $component,
                view     : $view
            ),
            ComponentRenderedEvent::NAME
        );

        return $this->componentRendererLocator->getRenderer($component)->render($component, $view);
    }

    public function renderViewHeaders(ViewInterface $view): string
    {
        // headers can be rendered without twig, since they will not have to support buffering
        $output = '';

        foreach ($view->getComponents() as $component) {
            $renderer = $this->componentRendererLocator->getRenderer($component);
            $output .= $renderer->renderHeaders($component, $view);
        }

        return $output;
    }

    /**
     * @param ComponentInterface[] $components
     */
    public function renderComponentsHeaders(array $components, ViewInterface $view): string
    {
        // headers can be rendered without twig, since they will not have to support buffering
        $output = '';

        foreach ($components as $component) {
            $isHeaderRendered = $this->componentRenderRegistry->isComponentHeaderRendered($component);

            // Render once for each component type, except parent components,
            // otherwise the same parent component with different children would render headers only once
            if ($isHeaderRendered && !$component instanceof ParentComponentInterface) {
                continue;
            }

            $this->componentRenderRegistry->registerComponentHeaderRender($component);

            $renderer = $this->componentRendererLocator->getRenderer($component);
            $output .= $renderer->renderHeaders($component, $view);
        }

        return $output;
    }

    public function renderViewFooters(ViewInterface $view): string
    {
        // footers can be rendered without twig, since they will not have to support buffering
        $output = '';

        foreach ($view->getComponents() as $component) {
            $renderer = $this->componentRendererLocator->getRenderer($component);
            $output .= $renderer->renderFooters($component, $view);
        }

        return $output;
    }

    /**
     * @param ComponentInterface[] $components
     */
    public function renderComponentsFooters(array $components, ViewInterface $view): string
    {
        // footers can be rendered without twig, since they will not have to support buffering
        $output = '';

        foreach ($components as $component) {
            $renderer = $this->componentRendererLocator->getRenderer($component);
            $output .= $renderer->renderFooters($component, $view);
        }

        return $output;
    }

    /**
     * @param string[] $componentStyleNames
     */
    public function renderComponentStyles(array $componentStyleNames): string
    {
        $styles = [];

        foreach ($componentStyleNames as $componentStyleName) {
            if ($this->componentRenderRegistry->isComponentStyleRendered($componentStyleName)) {
                continue;
            }

            $this->componentRenderRegistry->registerComponentStyleRender($componentStyleName);

            $content = $this->assetsHelper->getComponentCssFileContents(
                $componentStyleName,
                $this->localeSettingsHelper->getSettings()->locale->language->isRightToLeft,
            );
            $content = $this->assetsHelper->renderStyleContent($content);

            if ($content === null) {
                continue;
            }

            $styles[] = $content;
        }

        return implode(PHP_EOL, $styles);
    }

    /**
     * @param string[] $componentJavaScriptNames
     */
    public function renderComponentJavaScripts(array $componentJavaScriptNames): string
    {
        $javaScripts = [];

        foreach ($componentJavaScriptNames as $componentJavaScriptName) {
            if ($this->componentRenderRegistry->isComponentJavaScriptRendered($componentJavaScriptName)) {
                continue;
            }

            $this->componentRenderRegistry->registerComponentJavaScriptRender($componentJavaScriptName);

            $content = $this->assetsHelper->getComponentJavascriptFileContents(
                $componentJavaScriptName,
            );
            $content = $this->assetsHelper->renderJavaScriptContent($content);

            if ($content === null) {
                continue;
            }

            $javaScripts[] = $content;
        }

        return implode(PHP_EOL, $javaScripts);
    }
}
