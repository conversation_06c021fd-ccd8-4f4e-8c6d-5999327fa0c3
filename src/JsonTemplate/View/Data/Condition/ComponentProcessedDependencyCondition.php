<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\Data\Condition;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\View\Data\ViewDataRegistry;

final readonly class ComponentProcessedDependencyCondition implements ViewDataConditionInterface
{
    public function __construct(
        public ComponentInterface $component
    )
    {
    }

    public function check(ViewDataRegistry $viewDataRegistry): bool
    {
        return true;
    }

    /**
     * @param array<string, true> $processedComponentIds
     */
    public function isComponentProcessed(array $processedComponentIds): bool
    {
        return $processedComponentIds[$this->component->getId()] ?? false;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'type'      => 'component_processed_dependency',
            'component' => $this->component->getId(),
        ];
    }

    public function toText(): string
    {
        return sprintf('components %s processed', $this->component->getId());
    }
}
