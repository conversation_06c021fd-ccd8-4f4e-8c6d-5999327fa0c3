<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\Data\Condition;

use App\Component\Generic\Results\ResultsAmountRegistry;
use App\Component\Generic\Results\ResultsType;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;
use App\JsonTemplate\View\Data\ViewDataRegistry;

final readonly class AmountOfOrganicResultsCondition implements ViewDataConditionInterface
{
    public function __construct(
        private ResultsAmountRegistry $resultsAmountRegistry,
        private ?int $min,
        private ?int $max,
        private bool $expectedResult,
        private ?OrganicResultsComponentInterface $resultsComponent = null
    )
    {
    }

    public function check(ViewDataRegistry $viewDataRegistry): bool
    {
        $amount = $this->getAmountRegisteredAsOrganic();

        return $this->isAmountInExpectedRange($amount) === $this->expectedResult;
    }

    private function isAmountInExpectedRange(int $amountRendered): bool
    {
        if ($this->min !== null && $amountRendered < $this->min) {
            return false;
        }

        if ($this->max !== null && $amountRendered > $this->max) {
            return false;
        }

        return true;
    }

    private function getAmountRegisteredAsOrganic(): int
    {
        if ($this->resultsComponent !== null) {
            $results = $this->resultsAmountRegistry->getResultsByComponent($this->resultsComponent);

            return count($results);
        }

        return $this->resultsAmountRegistry->getRegisteredAmountByResultsType(ResultsType::ORGANIC);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'type'            => 'amount_of_organic_results',
            'amount'          => $this->getAmountRegisteredAsOrganic(),
            'min'             => $this->min,
            'max'             => $this->max,
            'expected_result' => $this->expectedResult,
        ];
    }

    public function toText(): string
    {
        $amount = $this->getAmountRegisteredAsOrganic();
        $expectedText = $this->expectedResult ? '' : 'NOT ';

        if ($this->min !== null && $this->max !== null) {
            return sprintf(
                '%u %s>= %u and <= %u',
                $amount,
                $expectedText,
                $this->min,
                $this->max,
            );
        }

        if ($this->min !== null) {
            return sprintf('%u %s>= %u', $amount, $expectedText, $this->min);
        }

        return sprintf('%u %s<= %u', $amount, $expectedText, $this->max);
    }
}
