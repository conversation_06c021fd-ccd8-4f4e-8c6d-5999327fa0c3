<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\Data\Condition;

use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;

readonly class SearchResponseHasResultsCondition implements ViewDataConditionInterface
{
    public function __construct(
        public ViewDataProperty $viewDataProperty,
        public bool $expectedValue
    )
    {
    }

    public function check(ViewDataRegistry $viewDataRegistry): bool
    {
        if (!$viewDataRegistry->has(ViewDataProperty::SEARCH_RESPONSES)) {
            return false;
        }

        $searchResponses = $viewDataRegistry->getSearchResponses();

        return $searchResponses->hasResults($this->viewDataProperty) === $this->expectedValue;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'type'               => 'search_response_has_results',
            'view_data_property' => $this->viewDataProperty->value,
            'expected_value'     => $this->expectedValue,
        ];
    }

    public function toText(): string
    {
        if ($this->expectedValue) {
            return sprintf('results found for %s', $this->viewDataProperty->value);
        }

        return sprintf('no results found for %s', $this->viewDataProperty->value);
    }
}
