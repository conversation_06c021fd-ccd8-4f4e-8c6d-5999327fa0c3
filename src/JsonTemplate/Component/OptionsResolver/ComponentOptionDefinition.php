<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\OptionsResolver;

use App\Http\Url\UrlValidationRouter;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Visymo\Shared\Infrastructure\Bridge\Symfony\OptionsResolver\SymfonyOptionDefinitionBridge;

class ComponentOptionDefinition extends SymfonyOptionDefinitionBridge implements ComponentOptionDefinitionInterface
{
    public function __construct(
        private readonly string $field,
        private readonly ComponentResolverDecoratorInterface $componentResolver,
        private readonly OptionsResolver $optionsResolver,
        private readonly UrlValidationRouter $urlValidationRouter
    )
    {
        parent::__construct($field, $optionsResolver);
    }

    public function setNestedResolver(callable $callback): self
    {
        $this->optionsResolver->setDefault(
            $this->field,
            function (OptionsResolver $optionsResolver) use ($callback): void {
                $callback(
                    new ComponentOptionsResolver(
                        componentResolver  : $this->componentResolver,
                        urlValidationRouter: $this->urlValidationRouter,
                        optionsResolver    : $optionsResolver,
                    ),
                );
            },
        );

        return $this;
    }
}
