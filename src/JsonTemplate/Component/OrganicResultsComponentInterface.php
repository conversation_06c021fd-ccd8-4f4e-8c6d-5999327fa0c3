<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

interface OrganicResultsComponentInterface extends ComponentInterface
{
    public const string KEY_AMOUNT                     = 'amount';
    public const string KEY_RESULT_AMOUNT_OPTIMIZATION = 'result_amount_optimization';

    public function getAmount(): int;

    public function decreaseAmount(int $amount): int;

    /**
     * Optimize the amount of results to be rendered based on amount of (pre)loaded ads
     */
    public function resultAmountOptimization(): bool;
}
