<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\ViewInterface;

interface ComponentRendererInterface
{
    /**
     * @throws UnsupportedComponentException
     */
    public function build(
        ComponentInterface $component,
        ViewInterface $view,
        ViewDataConditionCollection $conditions
    ): void;

    /**
     * @throws UnsupportedComponentException
     */
    public function render(ComponentInterface $component, ViewInterface $view): string;

    /**
     * @throws UnsupportedComponentException
     */
    public function renderHeaders(ComponentInterface $component, ViewInterface $view): string;

    /**
     * Render any needed footer scripts for this component
     * Be aware, that when an error occurs during processing of a component, the footer scripts might not be rendered.
     *
     * @throws UnsupportedComponentException
     */
    public function renderFooters(ComponentInterface $component, ViewInterface $view): string;
}
