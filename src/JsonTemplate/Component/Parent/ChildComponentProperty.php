<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\Parent;

final readonly class ChildComponentProperty
{
    private function __construct(
        public string $property,
        private bool $containsMultipleComponents
    )
    {
    }

    public static function createForOneComponent(string $property): self
    {
        return new self($property, false);
    }

    public static function createForMultipleComponents(string $property): self
    {
        return new self($property, true);
    }

    public function containsOneComponent(): bool
    {
        return !$this->containsMultipleComponents;
    }

    public function containsMultipleComponents(): bool
    {
        return $this->containsMultipleComponents;
    }
}
