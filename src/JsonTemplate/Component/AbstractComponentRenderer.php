<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;

abstract class AbstractComponent<PERSON><PERSON><PERSON> implements ComponentRendererInterface
{
    public function build(
        ComponentInterface $component,
        ViewInterface $view,
        ViewDataConditionCollection $conditions
    ): void
    {
        $componentViewDataRequest = $view->getDataRequest()
            ->createForComponent($component)
            ->setRequestConditions($conditions);

        $this->buildRequest($component, $componentViewDataRequest, $conditions);
        $this->registerDataRequirements($component, $componentViewDataRequest);

        $componentViewDataRequest->finalize($view->getDataRegistry());
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        // can be empty by default
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        // can be empty by default
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        return '';
    }

    public function renderHeaders(ComponentInterface $component, ViewInterface $view): string
    {
        return '';
    }

    public function renderFooters(ComponentInterface $component, ViewInterface $view): string
    {
        return '';
    }
}
