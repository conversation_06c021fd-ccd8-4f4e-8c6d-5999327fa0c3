<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

use Symfony\Component\DependencyInjection\ServiceLocator;

readonly class ComponentResolverLocator
{
    /**
     * @param ServiceLocator<mixed> $serviceLocator
     */
    public function __construct(
        private ServiceLocator $serviceLocator
    )
    {
    }

    /**
     * @return string[]
     */
    public function getSupportedTypes(): array
    {
        return array_keys($this->serviceLocator->getProvidedServices());
    }

    /**
     * @return \Generator<ComponentResolverInterface>
     */
    public function getResolvers(): \Generator
    {
        foreach ($this->getSupportedTypes() as $componentType) {
            yield $this->serviceLocator->get($componentType);
        }
    }

    public function hasComponentResolver(string $componentType): bool
    {
        return $this->serviceLocator->has($componentType);
    }

    public function getResolver(string $componentType): ComponentResolverInterface
    {
        return $this->serviceLocator->get($componentType);
    }
}
