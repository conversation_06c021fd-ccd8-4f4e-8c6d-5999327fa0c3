<?php

declare(strict_types=1);

namespace App\PageviewConversion\Routing;

use App\Generic\Routing\RouteCheckerInterface;
use App\PageviewConversion\Settings\PageviewConversionSettings;
use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: self::ALIAS)]
final readonly class PageviewConversionRouteChecker implements RouteCheckerInterface
{
    private const string ALIAS = 'route_checker_pageview_conversion';

    public function __construct(
        private PageviewConversionSettings $pageviewConversionSettings
    )
    {
    }

    public static function getAlias(): string
    {
        return self::ALIAS;
    }

    public function check(): bool
    {
        return $this->pageviewConversionSettings->enabled;
    }
}
