<?php

declare(strict_types=1);

namespace App\PageviewConversion\EventSubscriber;

use App\PageviewConversion\Settings\PageviewConversionSettings;
use App\PageviewConversion\Url\PageviewConversionUrlGenerator;
use App\Search\Request\SearchRequestInterface;
use App\Template\Event\RenderTemplateFootersEvent;
use Twig\Environment;
use Visymo\Shared\Domain\Event\EventSubscriberInterface;

readonly class InjectPageviewConversionEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private Environment $twig,
        private SearchRequestInterface $searchRequest,
        private PageviewConversionUrlGenerator $pageviewConversionUrlGenerator,
        private PageviewConversionSettings $pageviewConversionSettings
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            RenderTemplateFootersEvent::NAME => 'injectPageviewConversion',
        ];
    }

    public function injectPageviewConversion(RenderTemplateFootersEvent $event): void
    {
        if (!$this->pageviewConversionSettings->enabledForRequest) {
            return;
        }

        $pageviewConversionUrl = $this->searchRequest->isLandingPage()
            ? $this->pageviewConversionUrlGenerator->generateLanding()
            : $this->pageviewConversionUrlGenerator->generateLandingRelated();

        $event->addItem(
            $this->twig->render(
                '@theme/pageview_conversion/pageview_conversion_scripts.html.twig',
                [
                    'pageview_conversion_url' => $pageviewConversionUrl,
                ],
            ),
        );
    }
}
