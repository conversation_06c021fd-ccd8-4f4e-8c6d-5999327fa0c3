<?php

declare(strict_types=1);

namespace App\PageviewConversion\Url;

use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;

readonly class PageviewConversionUrlGenerator
{
    public function __construct(
        protected PersistentUrlParametersRouter $persistentUrlParametersRouter
    )
    {
    }

    public function generateLanding(): string
    {
        return $this->persistentUrlParametersRouter->generate(
            'route_pageview_conversion_landing',
            [],
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            true,
        );
    }

    public function generateLandingRelated(): string
    {
        return $this->persistentUrlParametersRouter->generate(
            'route_pageview_conversion_landing_related',
            [],
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            true,
        );
    }
}
