<?php

declare(strict_types=1);

namespace App\Statistics\Twig;

use App\Http\Request\GenericRequestInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class StatisticsExtension extends AbstractExtension
{
    public function __construct(
        private readonly GenericRequestInterface $genericRequest
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('statistics_pageview_id', $this->getPageviewId(...), ['is_safe' => ['html']]),
            new TwigFunction('statistics_visit_id', $this->getVisitId(...), ['is_safe' => ['html']]),
        ];
    }

    public function getPageviewId(): ?string
    {
        return $this->genericRequest->getPageviewId();
    }

    public function getVisitId(): ?string
    {
        return $this->genericRequest->getVisitId();
    }
}
