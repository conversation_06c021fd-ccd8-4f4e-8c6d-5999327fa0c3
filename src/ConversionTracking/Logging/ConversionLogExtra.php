<?php

declare(strict_types=1);

namespace App\ConversionTracking\Logging;

class ConversionLogExtra
{
    public const string CONVERSION_RELATED_TYPE = 'related_type';

    public const string PAGE_TERM = 'term';

    public const string AD_TYPE                  = 'ad_type';
    public const string AD_BLOCK_NUMBER          = 'block';
    public const string AD_NUMBER                = 'ad';
    public const string AD_UNIQUE_AD_CLICK_COUNT = 'unique_ad_click_count';
    public const string AD_UNIT_PATH             = 'ad_unit_path';

    public const string TRACKING_OFFLINE_CONVERSION = 'offline_conversion';
    public const string TRACKING_ONLINE_CONVERSION  = 'online_conversion';
}
