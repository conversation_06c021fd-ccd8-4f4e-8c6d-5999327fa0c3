<?php

declare(strict_types=1);

namespace App\ConversionTracking\Logging;

use App\Ads\AdProvider;
use App\Brand\Settings\BrandSettingsHelper;
use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Request\ConversionTrackingRequestInterface;
use App\Domain\Settings\DomainSettingsHelperInterface;
use App\GeoIp\GeoIp2CountryHelper;
use App\Http\Request\GenericRequestInterface;
use App\Http\Request\Info\RequestInfoInterface;
use App\JsonTemplate\Request\JsonTemplateRequestInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\RelatedTerms\Request\RelatedTermsRequestInterface;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\AdditionalChannel\Repository\AdditionalChannelsRepository;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\Tracking\Helper\TrafficHelper;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Psr\Log\LoggerInterface;
use UAParser\Parser;

final readonly class ConversionLogger
{
    public function __construct(
        private WebsiteSettingsHelper $websiteSettingsHelper,
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private RequestInfoInterface $requestInfo,
        private GenericRequestInterface $genericRequest,
        private SearchRequestInterface $searchRequest,
        private RelatedTermsRequestInterface $relatedTermsRequest,
        private ConversionTrackingRequestInterface $conversionTrackingRequest,
        private GeoIp2CountryHelper $geoIpCountryHelper,
        private AdditionalChannelsRepository $additionalChannelsRepository,
        private JsonTemplateRequestInterface $jsonTemplateRequest,
        private LoggerInterface $conversionLogChannel,
        private LoggerInterface $logger,
        private TrafficHelper $trafficHelper,
        private BrandSettingsHelper $brandSettingsHelper,
        private DomainSettingsHelperInterface $domainSettingsHelper,
        private LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    public function logConversion(
        Conversion $conversion,
        ?ConversionTrackingResultInterface $conversionTrackingResult = null
    ): void
    {
        $websiteSettings = $this->websiteSettingsHelper->getSettings();
        $trackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();
        $activeSplitTest = $trackingEntry->activeSplitTest;
        $adStyleId = match ($conversion->eventType) {
            ConversionEventType::CLICK_AD,
            ConversionEventType::CLICK_RELATED => $this->conversionTrackingRequest->getAdStyleId(),
            default                            => null,
        };
        $adClientId = match ($conversion->eventType) {
            ConversionEventType::CLICK_AD,
            ConversionEventType::CLICK_RELATED => $this->conversionTrackingRequest->getAdClientId(),
            default                            => null,
        };

        $clickId = $trackingEntry->clickId;

        $googleAdSense = $websiteSettings->getGoogleAdSense();
        $contractType = $googleAdSense->isEnabled()
            ? $googleAdSense->getContractType()->getConversionLogValue()
            : null;

        $brandSettings = $this->brandSettingsHelper->getSettings();

        $context = [
            ConversionLog::CONVERSION_EVENT_TYPE    => $conversion->eventType->value,
            ConversionLog::CONVERSION_ORDER_ID      => $conversion->trackingOrder->getId(),
            ConversionLog::CONVERSION_ORDER_ID_TYPE => $conversion->trackingOrder->getType(),

            ConversionLog::PAGE_BRAND_SLUG              => $brandSettings->getSlug(),
            ConversionLog::PAGE_PARTNER_SLUG            => $brandSettings->getPartnerSlug(),
            ConversionLog::PAGE_QUERY                   => $this->searchRequest->getQuery(),
            ConversionLog::PAGE_ALTERNATE_RELATED_QUERY => $this->relatedTermsRequest->getAlternateRelatedQuery(),
            ConversionLog::PAGE_PAGEVIEW_ID             => $this->conversionTrackingRequest->getOriginalPageviewId(),
            ConversionLog::PAGE_VISIT_ID                => $this->genericRequest->getVisitId(),
            ConversionLog::PAGE_CONVERSION_ROUTE        => $trackingEntry->conversionRoute,

            ConversionLog::SEA_LANDER_QUERY             => $trackingEntry->query,
            ConversionLog::SEA_CAMPAIGN_ID              => $trackingEntry->campaignId,
            ConversionLog::SEA_CAMPAIGN_NAME            => $trackingEntry->campaignName,
            ConversionLog::SEA_DEVICE                   => $trackingEntry->device->getShortValue(),
            ConversionLog::SEA_TRAFFIC_SOURCE           => $trackingEntry->trafficSource?->value,
            ConversionLog::SEA_NETWORK                  => $trackingEntry->network?->value,
            ConversionLog::SEA_ACCOUNT_ID               => $trackingEntry->accountId,
            ConversionLog::SEA_AD_GROUP_ID              => $trackingEntry->adGroupId,
            ConversionLog::SEA_KEYWORD_ID               => $trackingEntry->keywordId,
            ConversionLog::SEA_COMBINED_CLICK_ID        => $clickId?->value,
            ConversionLog::SEA_COMBINED_CLICK_ID_SOURCE => $clickId?->source->value,
            ConversionLog::PARAMETER_SECONDARY_CLICK_ID => $trackingEntry->genericSecondaryClickId,
            ConversionLog::SEA_PUBLISHER                => $trackingEntry->publisher,
            ConversionLog::SEA_GOOGLE_LOCATION_ID       => $trackingEntry->googleLocationId,
            ConversionLog::SEA_ADDITIONAL_CHANNELS      => $this->additionalChannelsRepository->getAdditionalChannels(),
            ConversionLog::SEA_TRAFFIC_TYPE             => $this->trafficHelper->getTrafficType()->value,
            ConversionLog::SEA_CONTRACT_TYPE            => $contractType,
            ConversionLog::SEA_CUSTOM_ID                => $trackingEntry->customId,

            ConversionLog::AD_STYLE_ID  => $adStyleId,
            ConversionLog::AD_CLIENT_ID => $adClientId,

            ConversionLog::DOMAIN => $this->domainSettingsHelper->getSettings()->host,
            ConversionLog::LOCALE => $this->localeSettingsHelper->getSettings()->locale->code,

            ConversionLog::SPLIT_TEST_ID      => $activeSplitTest?->getSplitTestId(),
            ConversionLog::SPLIT_TEST_VARIANT => $activeSplitTest?->getSplitTestVariant(),

            ConversionLog::JSON_TEMPLATE_VARIANT => $this->jsonTemplateRequest->getTemplateVariant(),

            ConversionLog::VISITOR_ACCEPT_LANGUAGE  => $this->requestInfo->getAcceptLanguage(),
            ConversionLog::VISITOR_USER_IP          => $this->requestInfo->getUserIp(),
            ConversionLog::VISITOR_COUNTRY_CODE     => $this->geoIpCountryHelper->getVisitorCountryCode(),
            ConversionLog::VISITOR_HTTP_USER_AGENT  => $this->getUserAgentInfo(),
            ConversionLog::VISITOR_HTTP_HOST_CLIENT => $this->conversionTrackingRequest->getHttpHostClient(),
            ConversionLog::VISITOR_TLS_VERSION      => $this->requestInfo->getTlsVersion(),

            ...$conversion->extraLogData,
        ];

        if ($conversionTrackingResult !== null) {
            $context = [...$context, ...$conversionTrackingResult->getExtraLogData()];
        }

        if ($conversion->eventType === ConversionEventType::CLICK_AD) {
            $adType = $conversion->extraLogData[ConversionLog::AD_TYPE];

            if ($adType !== null && !$this->isAdTypeApproved($adType)) {
                $this->logger->warning('Ad type is not approved', $context);
            }
        }

        $this->conversionLogChannel->info('conversion_log', $context);
    }

    private function isAdTypeApproved(string $adType): bool
    {
        $websiteSettings = $this->websiteSettingsHelper->getSettings();
        $adsenseApproval = $websiteSettings->getGoogleAdSense()->isApproval();
        $bingAdsApproval = $websiteSettings->getBingAds()->isApproval();

        if ($adType === AdProvider::TYPE_GOOGLE && !$adsenseApproval) {
            return false;
        }

        if ($adType === AdProvider::TYPE_BING && !$bingAdsApproval) {
            return false;
        }

        return true;
    }

    /**
     * @return mixed[]
     */
    private function getUserAgentInfo(): array
    {
        $userAgent = $this->requestInfo->getUserAgent();
        $userAgentInfo = [
            'full' => $userAgent,
        ];

        try {
            $parsedUserAgent = Parser::create()->parse($userAgent);

            $userAgentInfo = array_merge(
                $userAgentInfo,
                array_filter(
                    [
                        'os_family'       => $parsedUserAgent->os->family,
                        'os_version'      => $parsedUserAgent->os->toVersion(),
                        'browser_family'  => $parsedUserAgent->ua->family,
                        'browser_version' => $parsedUserAgent->ua->toVersion(),
                        'device_family'   => $parsedUserAgent->device->family,
                        'device_brand'    => $parsedUserAgent->device->brand,
                        'device_model'    => $parsedUserAgent->device->model,
                    ],
                    static fn ($value) => $value !== '' && $value !== null,
                ),
            );
        } catch (\Exception) {
            // ignore exception, ua parsing is optional
        }

        return $userAgentInfo;
    }
}
