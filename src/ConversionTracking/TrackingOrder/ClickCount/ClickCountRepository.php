<?php

declare(strict_types=1);

namespace App\ConversionTracking\TrackingOrder\ClickCount;

use App\ConversionTracking\TrackingOrder\TrackingOrderInterface;
use App\Redis\RedisClient;
use Psr\Log\LoggerInterface;

class ClickCountRepository
{
    private const int CLICK_COUNT_TTL = 7200; // 2 hours

    public function __construct(
        private readonly RedisClient $redisClient,
        private readonly ClickCountKeyFactory $clickCountKeyFactory,
        private readonly LoggerInterface $logger
    )
    {
    }

    public function getClickCount(TrackingOrderInterface $trackingOrder): int
    {
        try {
            $clickCountKey = $this->clickCountKeyFactory->create($trackingOrder);

            if ($clickCountKey === null) {
                return 0;
            }

            $value = $this->redisClient->get($clickCountKey);

            if ($value === false) {
                return 0;
            }

            return (int)$value;
        } catch (\Throwable $exception) {
            $this->logger->error(
                sprintf('Caught %s while retrieving tracking order click count', $exception::class),
                [
                    'exception'      => $exception,
                    'tracking_order' => $trackingOrder->toArray(),
                ],
            );
        }

        return 0;
    }

    public function increaseClickCount(TrackingOrderInterface $trackingOrder): int
    {
        try {
            $clickCountKey = $this->clickCountKeyFactory->create($trackingOrder);

            if ($clickCountKey === null) {
                return 1;
            }

            // Set and increase click count
            $clickCount = $this->redisClient->incr($clickCountKey);

            // set expire ttl
            $this->redisClient->expire($clickCountKey, self::CLICK_COUNT_TTL);

            return $clickCount;
        } catch (\Throwable $exception) {
            $this->logger->error(
                sprintf('Caught %s while increasing tracking order click count', $exception::class),
                [
                    'exception'      => $exception,
                    'tracking_order' => $trackingOrder->toArray(),
                ],
            );
        }

        return 1;
    }
}
