<?php

declare(strict_types=1);

namespace App\ConversionTracking\Controller;

use App\AdBot\Request\AdBotRequestInterface;
use App\ConversionTracking\Endpoint\VisymoRelatedTerms\VisymoRelatedTermsHandlerInterface;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingResponseFactory;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Generic\Response\UncachedPixelResponse;
use App\SplitTest\Request\SplitTestRequestFlag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class VisymoRelatedTermsConversionTrackingController extends AbstractController
{
    public function __construct(
        private readonly AdBotRequestInterface $adBotRequest,
        private readonly ConversionTrackingResponseFactory $conversionTrackingResponseFactory,
        private readonly FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
    }

    #[Route(
        // Path shortened to avoid ad blockers
        path    : '/tp/vrt',
        name    : 'route_conversion_tracking_visymo_related_terms',
        defaults: [
            SplitTestRequestFlag::ALWAYS_MATCH_ROUTE => true,
        ],
        methods : ['GET', 'POST']
    )]
    public function visymoRelatedTerms(VisymoRelatedTermsHandlerInterface $visymoRelatedTermsHandler): Response
    {
        if ($this->adBotRequest->isAdBot() || $this->friendlyBotRequest->isFriendlyBot()) {
            return new UncachedPixelResponse();
        }

        return $this->conversionTrackingResponseFactory->create(
            $visymoRelatedTermsHandler->handle(),
        );
    }
}
