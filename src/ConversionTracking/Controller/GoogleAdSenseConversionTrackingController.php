<?php

declare(strict_types=1);

namespace App\ConversionTracking\Controller;

use App\AdBot\Request\AdBotRequestInterface;
use App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseHandlerInterface;
use App\ConversionTracking\Helper\AdClickCounterHelper;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingResponseFactory;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Generic\Response\UncachedPixelResponse;
use App\SplitTest\Request\SplitTestRequestFlag;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class GoogleAdSenseConversionTrackingController extends AbstractController
{
    public function __construct(
        private readonly AdBotRequestInterface $adBotRequest,
        private readonly AdClickCounterHelper $adClickCounterHelper,
        private readonly ConversionTrackingResponseFactory $conversionTrackingResponseFactory,
        private readonly WebsiteSettingsHelper $websiteSettingsHelper,
        private readonly FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
    }

    #[Route(
        // Path shortened to avoid ad blockers
        path    : '/tp/ga',
        name    : 'route_conversion_tracking_google_adsense',
        defaults: [
            SplitTestRequestFlag::ALWAYS_MATCH_ROUTE => true,
        ],
        methods : ['GET', 'POST']
    )]
    public function googleAdSense(
        GoogleAdSenseHandlerInterface $googleAdSenseHandler
    ): Response
    {
        // Prevent double conversion logging when ga tracking is called for online contracts
        $isGoogleAfsOnline = $this->websiteSettingsHelper->getSettings()->getGoogleAdSense()
            ->getContractType()->isOnline();

        if ($isGoogleAfsOnline) {
            throw $this->createNotFoundException();
        }

        // Endpoint should never be called for ad bots
        if ($this->adBotRequest->isAdBot() || $this->friendlyBotRequest->isFriendlyBot()) {
            return new UncachedPixelResponse();
        }

        // Increase generic ad click counter
        $this->adClickCounterHelper->increaseGenericAdClickCount();

        // Handle Google AdSense conversion
        return $this->conversionTrackingResponseFactory->create(
            $googleAdSenseHandler->handle(),
        );
    }
}
