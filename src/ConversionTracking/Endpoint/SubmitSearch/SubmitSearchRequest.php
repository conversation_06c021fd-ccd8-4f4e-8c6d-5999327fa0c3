<?php

declare(strict_types=1);

namespace App\ConversionTracking\Endpoint\SubmitSearch;

use App\Http\Request\Manager\RequestManagerInterface;

final readonly class SubmitSearchRequest implements SubmitSearchRequestInterface
{
    public function __construct(
        private RequestManagerInterface $requestManager
    )
    {
    }

    public function hasPreventConversionLoggingFlag(): bool
    {
        return $this->requestManager->flagBag()->getBool(SubmitSearchRequestFlag::PREVENT_CONVERSION_LOGGING);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            SubmitSearchRequestFlag::PREVENT_CONVERSION_LOGGING => $this->hasPreventConversionLoggingFlag(),
        ];
    }
}
