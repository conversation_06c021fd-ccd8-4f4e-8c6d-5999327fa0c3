<?php

declare(strict_types=1);

namespace App\ConversionTracking\Endpoint\GoogleRelatedTerms;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class GoogleRelatedTermsRequest implements GoogleRelatedTermsRequestInterface
{
    private string $supportsTracking;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function supportsTracking(): ?bool
    {
        return $this->requestPropertyNormalizer->getBool($this->getSupportsTracking());
    }

    private function getSupportsTracking(): ?string
    {
        if (!isset($this->supportsTracking)) {
            $this->supportsTracking = $this->requestManager->queryBag()->getString(self::PARAMETER_SUPPORTS_TRACKING);
        }

        return $this->requestPropertyNormalizer->getString($this->supportsTracking);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_SUPPORTS_TRACKING => $this->getSupportsTracking(),
        ];
    }
}
