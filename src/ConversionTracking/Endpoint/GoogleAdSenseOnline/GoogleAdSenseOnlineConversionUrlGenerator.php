<?php

declare(strict_types=1);

namespace App\ConversionTracking\Endpoint\GoogleAdSenseOnline;

use App\ConversionTracking\Request\ConversionTrackingRequestInterface;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;

readonly class GoogleAdSenseOnlineConversionUrlGenerator
{
    public function __construct(
        protected PersistentUrlParametersRouter $persistentUrlParametersRouter
    )
    {
    }

    public function generate(string $adClientId): string
    {
        return $this->persistentUrlParametersRouter->generate(
            'route_conversion_tracking_google_adsense_online',
            [
                ConversionTrackingRequestInterface::PARAMETER_AD_CLIENT_ID => $adClientId,
            ],
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            true,
        );
    }
}
