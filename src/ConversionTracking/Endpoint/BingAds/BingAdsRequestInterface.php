<?php

declare(strict_types=1);

namespace App\ConversionTracking\Endpoint\BingAds;

use App\Http\Request\RequestInterface;
use Visymo\BingAds\AdUnit\Enum\AdTypesFilter;
use Visymo\BingAds\AdUnit\Enum\Position;

interface BingAdsRequestInterface extends RequestInterface
{
    public const string PARAMETER_BLOCK    = 'block';
    public const string PARAMETER_POSITION = 'position';
    public const string PARAMETER_POSITON  = 'positon'; // Typo at Bing Ads
    public const string PARAMETER_RANK     = 'rank';
    public const string PARAMETER_AD_TYPE  = 'adtype';

    /**
     * Bing Ads block number
     * Starts with 1
     */
    public function getBlock(): ?int;

    /**
     * Bing Ads container position
     *
     * @see Position
     */
    public function getPosition(): ?string;

    /**
     * Bing Ads ad rank in block
     * Starts with 1
     */
    public function getRank(): ?int;

    /**
     * Bing Ads ad type
     *
     * @see AdTypesFilter
     */
    public function getAdType(): ?string;
}
