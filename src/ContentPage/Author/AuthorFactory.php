<?php

declare(strict_types=1);

namespace App\ContentPage\Author;

final readonly class AuthorFactory
{
    public function create(
        string $slug,
        string $name
    ): Author
    {
        return new Author(
            slug: $slug,
            name: $name,
        );
    }

    public function createEditorialTeam(): Author
    {
        return $this->create(
            Author::EDITORIAL_TEAM_SLUG,
            Author::EDITORIAL_TEAM_NAME,
        );
    }
}
