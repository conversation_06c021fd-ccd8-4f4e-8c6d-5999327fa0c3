<?php

declare(strict_types=1);

namespace App\ContentPage\Url;

use App\ContentPage\Request\ContentPageRequestInterface;
use App\Http\Url\AbstractCachedPersistentUrlParametersProvider;
use App\Http\Url\PersistentUrlParametersPageType;
use App\JsonTemplate\View\JsonTemplateViewRegistry;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

class ContentPageUrlParametersProvider extends AbstractCachedPersistentUrlParametersProvider
{
    public function __construct(
        private readonly ContentPageRequestInterface $contentPageRequest,
        private readonly JsonTemplateViewRegistry $jsonTemplateViewRegistry
    )
    {
    }

    /**
     * @inheritDoc
     */
    protected function createPersistentUrlParameters(PersistentUrlParametersPageType $pageType): array
    {
        return match ($pageType) {
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            PersistentUrlParametersPageType::NEW_SEARCH    => [],
            PersistentUrlParametersPageType::DEFAULT       => $this->createForDefault(),
            PersistentUrlParametersPageType::RELATED_TERMS => $this->createForRelatedTerms(),
        };
    }

    /**
     * @return array<string, string>
     */
    private function createForDefault(): array
    {
        $publicId = $this->contentPageRequest->getPublicId();

        if ($publicId === null) {
            return [];
        }

        return [
            ContentPageRequestInterface::PARAMETER_CONTENT_PAGE_PUBLIC_ID => (string)$publicId,
        ];
    }

    /**
     * @return array<string, string>
     */
    private function createForRelatedTerms(): array
    {
        $contentPage = $this->getContentPage();
        $publicId = $contentPage->publicId ?? $this->contentPageRequest->getPreviousPublicId();

        if ($publicId === null) {
            return [];
        }

        $urlParameters = [
            ContentPageRequestInterface::PARAMETER_PREVIOUS_CONTENT_PAGE_PUBLIC_ID => (string)$publicId,
        ];

        if ($contentPage?->collectionSlug === 'default2') {
            $urlParameters[ContentPageRequestInterface::PARAMETER_PREVIOUS_CONTENT_PAGE_FALLBACK_COLLECTION] = '1';
        }

        return $urlParameters;
    }

    private function getContentPage(): ?ContentPage
    {
        return $this->jsonTemplateViewRegistry->getView()?->getDataRegistry()->getContentPage()->page;
    }
}
