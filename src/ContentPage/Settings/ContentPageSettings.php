<?php

declare(strict_types=1);

namespace App\ContentPage\Settings;

use App\ContentPage\Author\Author;
use App\ModuleSettings\ModuleSettingsInterface;

readonly class ContentPageSettings implements ModuleSettingsInterface
{
    public function __construct(
        public string $collection,
        public Author $author,
        public bool $useBrandForOrganicResults
    )
    {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'collection'                    => $this->collection,
            'author'                        => $this->author->name,
            'use_brand_for_organic_results' => $this->useBrandForOrganicResults,
        ];
    }
}
