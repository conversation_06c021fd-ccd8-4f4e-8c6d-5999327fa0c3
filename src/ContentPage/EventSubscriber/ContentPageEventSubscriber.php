<?php

declare(strict_types=1);

namespace App\ContentPage\EventSubscriber;

use App\ContentPage\Request\ContentPageRequestInterface;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class ContentPageEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private ContentPageRequestInterface $contentPageRequest
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            JsonTemplateViewCreatedEvent::NAME => ['onJsonTemplateViewCreated'],
        ];
    }

    public function onJsonTemplateViewCreated(JsonTemplateViewCreatedEvent $event): void
    {
        $publicId = $this->contentPageRequest->getPublicId() ?? $this->contentPageRequest->getPreviousPublicId();

        if ($publicId !== null) {
            $event->view->getDataRequest()
                ->contentPage()
                ->setPublicId($publicId);
        }
    }
}
