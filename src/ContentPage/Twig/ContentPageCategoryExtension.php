<?php

declare(strict_types=1);

namespace App\ContentPage\Twig;

use App\ContentPageHome\Request\ContentPageCategoryRequestInterface;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageCategory;

class ContentPageCategoryExtension extends AbstractExtension
{
    public function __construct(
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'generate_category_url',
                $this->generateCategoryUrl(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    private function generateCategoryUrl(ContentPageCategory $contentPageCategory): string
    {
        return $this->persistentUrlParametersRouter->generate(
            'route_content_page_category',
            [
                ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_PUBLIC_ID => $contentPageCategory->publicId,
                ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_SLUG      => $contentPageCategory->slug,
            ],
            PersistentUrlParametersPageType::NEW_SEARCH,
        );
    }
}
