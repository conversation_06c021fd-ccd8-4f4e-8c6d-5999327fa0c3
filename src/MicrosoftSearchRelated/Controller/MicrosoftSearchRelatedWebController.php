<?php

declare(strict_types=1);

namespace App\MicrosoftSearchRelated\Controller;

use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestFlag;
use App\Search\SearchType;
use App\Statistics\Helper\StatisticsRequestFlag;
use App\Tracking\Entry\Request\TrackingEntryRequestFlag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class MicrosoftSearchRelatedWebController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer,
        private readonly RouteRegistry $routeRegistry
    )
    {
    }

    #[Route(
        path    : '/msrw',
        name    : 'route_microsoft_search_related_web',
        defaults: [
            StatisticsRequestFlag::LOG_ENABLED                => true,
            TrackingEntryRequestFlag::USE_AS_CONVERSION_ROUTE => true,
            SearchRequestFlag::TYPE                           => SearchType::DISPLAY->value,
        ],
        methods : ['GET']
    )]
    public function search(): Response
    {
        $this->routeRegistry->setCurrentRouteAsSearchRoute();

        return $this->jsonTemplateRenderer->renderForSearchByDevice(
            '@themeJson/microsoft_search_related_web/microsoft_search_related_web_mobile.json',
            '@themeJson/microsoft_search_related_web/microsoft_search_related_web_desktop.json',
        );
    }
}
