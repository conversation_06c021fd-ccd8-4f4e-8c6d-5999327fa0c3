<?php

declare(strict_types=1);

namespace App\Error\EventSubscriber;

use App\BingAds\Helper\BingAdsHelper;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final class ErrorEventSubscriber implements EventSubscriberInterface
{
    private const string ADS_ERROR_CHANNEL = 'error_500';

    public function __construct(
        private readonly GoogleCsaRegistry $googleCsaRegistry,
        private readonly BingAdsHelper $bingAdsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            // Should be handled as last subscriber
            JsonTemplateHandledEvent::NAME => ['onJsonTemplateHandled', -1000],
        ];
    }

    public function onJsonTemplateHandled(JsonTemplateHandledEvent $event): void
    {
        if ($event->view->getResponse()->getStatusCode() >= 400) {
            $this->googleCsaRegistry->getGoogleCsa()?->addChannel(self::ADS_ERROR_CHANNEL);
            $this->bingAdsHelper->getBingAds()?->getPageOptions()->addTracingTag(self::ADS_ERROR_CHANNEL);
        }
    }
}
