<?php

declare(strict_types=1);

namespace App\PageHeadTags\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;
use App\PageHeadTags\Tags\PageHeadTagsHelper;

class PageHeadTagsDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_PAGE_HEAD_TAGS = 'page head tags';

    public function __construct(
        private readonly PageHeadTagsHelper $pageHeadTagsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        return [
            new DebugInfo(
                self::KEY_PAGE_HEAD_TAGS,
                [
                    $this->pageHeadTagsHelper->getPageHeadTags()->toArray(),
                ],
            ),
        ];
    }

    public static function getDefaultPriority(): int
    {
        return 10;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_PAGE_HEAD_TAGS,
        ];
    }
}
