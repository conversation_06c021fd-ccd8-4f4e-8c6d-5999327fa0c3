<?php

declare(strict_types=1);

namespace App\PageHeadTags\Factory;

use App\Brand\Settings\BrandSettingsHelper;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\Tags\PageHeadTags;
use App\Search\Request\SearchRequestInterface;

final readonly class ContentSearchAdvertisedPageHeadTagsFactory implements PageHeadTagsFactoryInterface
{
    public function __construct(
        private SearchRequestInterface $searchRequest,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function getType(): string
    {
        return 'content_search_advertised';
    }

    public function createFromView(ViewInterface $view): ?PageHeadTags
    {
        if ($this->searchRequest->getQuery() === null) {
            return null;
        }

        $queryTitleCase = mb_convert_case($this->searchRequest->getQuery(), MB_CASE_TITLE, 'UTF-8');
        $title = sprintf(
            '%s - %s',
            $queryTitleCase,
            $this->brandSettingsHelper->getSettings()->getName(),
        );

        return new PageHeadTags(
            title: $title,
        );
    }
}
