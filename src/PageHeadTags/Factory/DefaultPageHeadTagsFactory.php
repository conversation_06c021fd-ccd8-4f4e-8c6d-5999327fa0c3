<?php

declare(strict_types=1);

namespace App\PageHeadTags\Factory;

use App\Brand\Settings\BrandSettingsHelper;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\Tags\PageHeadTags;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Request\SeaRequestInterface;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use Symfony\Contracts\Translation\TranslatorInterface;

final readonly class DefaultPageHeadTagsFactory implements PageHeadTagsFactoryInterface
{
    public const string TYPE = 'default';

    public function __construct(
        private SearchRequestInterface $searchRequest,
        private SeaRequestInterface $seaRequest,
        private TranslatorInterface $translator,
        private BrandSettingsHelper $brandSettingsHelper,
        private TrademarkInfringementResultBlocker $trademarkInfringementResultBlocker
    )
    {
    }

    public function getType(): string
    {
        return self::TYPE;
    }

    public function createFromView(ViewInterface $view): PageHeadTags
    {
        return $this->create();
    }

    public function create(): PageHeadTags
    {
        if ($this->trademarkInfringementResultBlocker->blockResults()) {
            $query = null;
        } else {
            $query = $this->seaRequest->getUserQuery() ?? $this->searchRequest->getQuery();
        }

        $metaDescription = $query !== null
            ? $this->translator->trans('meta.description', ['%query%' => $query])
            : null;

        return new PageHeadTags(
            title          : $this->brandSettingsHelper->getSettings()->getName(),
            metaDescription: $metaDescription,
        );
    }
}
