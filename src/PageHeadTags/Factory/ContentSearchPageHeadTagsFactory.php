<?php

declare(strict_types=1);

namespace App\PageHeadTags\Factory;

use App\Brand\Settings\BrandSettingsHelper;
use App\ContentPage\OpenGraph\ContentPageToOpenGraphGenerator;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\Tags\PageHeadTags;
use App\Sitemap\Settings\SitemapSettings;

final readonly class ContentSearchPageHeadTagsFactory implements PageHeadTagsFactoryInterface
{
    public function __construct(
        private SitemapSettings $sitemapSettings,
        private BrandSettingsHelper $brandSettingsHelper,
        private ContentPageToOpenGraphGenerator $contentPageToOpenGraphGenerator
    )
    {
    }

    public function getType(): string
    {
        return 'content_search';
    }

    public function createFromView(ViewInterface $view): ?PageHeadTags
    {
        $contentPage = $view->getDataRegistry()
            ->getContentPage()
            ->page;

        if ($contentPage === null) {
            return null;
        }

        $openGraph = $this->contentPageToOpenGraphGenerator->generate(
            $contentPage,
            'route_content_search',
        );

        return new PageHeadTags(
            title          : $this->addBrandNameAsSuffixToTitle($contentPage->title),
            metaDescription: $contentPage->meta?->description,
            metaKeywords   : $contentPage->meta?->keywords,
            metaRobots     : $this->sitemapSettings->enabled ? 'index' : 'noindex',
            openGraph      : $openGraph,
        );
    }

    private function addBrandNameAsSuffixToTitle(string $title): string
    {
        return sprintf('%s - %s', $title, $this->brandSettingsHelper->getSettings()->getName());
    }
}
