<?php

declare(strict_types=1);

namespace App\PageHeadTags\Factory;

use App\Brand\Settings\BrandSettingsHelper;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\Tags\PageHeadTags;
use Symfony\Contracts\Translation\TranslatorInterface;

final readonly class AdvancedSearchPageHeadTagsFactory implements PageHeadTagsFactoryInterface
{
    public function __construct(
        private BrandSettingsHelper $brandSettingsHelper,
        private TranslatorInterface $translator
    )
    {
    }

    public function getType(): string
    {
        return 'advanced_search';
    }

    public function createFromView(ViewInterface $view): PageHeadTags
    {
        $title = sprintf(
            '%s %s',
            $this->brandSettingsHelper->getSettings()->getName(),
            $this->translator->trans('advanced_search.title'),
        );

        return new PageHeadTags(
            title: $title,
        );
    }
}
