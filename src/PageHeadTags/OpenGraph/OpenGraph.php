<?php

declare(strict_types=1);

namespace App\PageHeadTags\OpenGraph;

final class OpenGraph
{
    /** @var OpenGraphProperty[] */
    private array $properties;

    /**
     * @param OpenGraphImage[] $images
     */
    public function __construct(
        public readonly string $type,
        public readonly string $siteName,
        public readonly string $locale,
        public readonly string $url,
        public readonly string $title,
        public readonly ?string $description,
        public readonly array $images
    )
    {
    }

    /**
     * @return OpenGraphProperty[]
     */
    public function toProperties(): array
    {
        if (!isset($this->properties)) {
            $properties = [
                new OpenGraphProperty(OpenGraphPropertyType::TYPE, $this->type),
                new OpenGraphProperty(OpenGraphPropertyType::SITE_NAME, $this->siteName),
                new OpenGraphProperty(OpenGraphPropertyType::LOCALE, $this->locale),
                new OpenGraphProperty(OpenGraphPropertyType::URL, $this->url),
                new OpenGraphProperty(OpenGraphPropertyType::TITLE, $this->title),
                new OpenGraphProperty(OpenGraphPropertyType::DESCRIPTION, (string)$this->description),
            ];
            $properties = array_filter(
                $properties,
                static fn (OpenGraphProperty $property) => $property->content !== '',
            );

            foreach ($this->images as $image) {
                $properties = array_merge($properties, $image->toProperties());
            }

            $this->properties = $properties;
        }

        return $this->properties;
    }

    /**
     * @return array<array<string, string>>
     */
    public function toArray(): array
    {
        return array_map(
            static fn (OpenGraphProperty $property) => $property->toArray(),
            $this->toProperties(),
        );
    }
}
