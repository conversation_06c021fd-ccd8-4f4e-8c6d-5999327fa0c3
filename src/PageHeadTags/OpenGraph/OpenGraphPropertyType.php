<?php

declare(strict_types=1);

namespace App\PageHeadTags\OpenGraph;

enum OpenGraphPropertyType: string
{
    case DESCRIPTION  = 'og:description';
    case IMAGE        = 'og:image';
    case IMAGE_ALT    = 'og:image:alt';
    case IMAGE_HEIGHT = 'og:image:height';
    case IMAGE_TYPE   = 'og:image:type';
    case IMAGE_WIDTH  = 'og:image:width';
    case LOCALE       = 'og:locale';
    case SITE_NAME    = 'og:site_name';
    case TITLE        = 'og:title';
    case TYPE         = 'og:type';
    case URL          = 'og:url';
}
