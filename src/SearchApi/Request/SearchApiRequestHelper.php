<?php

declare(strict_types=1);

namespace App\SearchApi\Request;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ContentPagesViewDataRequest;
use App\JsonTemplate\View\DataRequest\ContentPageViewDataRequest;
use App\JsonTemplate\View\DataRequest\SearchApiViewDataRequestInterface;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\Component\SearchApiComponentRegistry;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPages\Response\ContentPagesResponseContext;

final readonly class SearchApiRequestHelper
{
    public function __construct(
        private SearchApiComponentRegistry $componentProcessedManager
    )
    {
    }

    public function createViewDataRequestForSearchApiRequests(
        SearchApiRequestCollection $searchApiRequestCollection,
        ViewInterface $view
    ): ?ViewDataRequest
    {
        $ignoreQuery = null;

        /** @var array<value-of<ViewDataProperty>, SearchApiViewDataRequestInterface> $requests */
        $requests = [];
        $components = [];
        $viewDataProperties = $searchApiRequestCollection->getViewDataProperties();

        foreach ($viewDataProperties as $viewDataProperty) {
            $searchApiRequests = $searchApiRequestCollection->getRequestsForViewDataProperty(
                $viewDataProperty,
            );

            foreach ($searchApiRequests as $index => $searchApiRequest) {
                $isReadyToSend = $searchApiRequest->isReadyToSend(
                    viewDataRegistry     : $view->getDataRegistry(),
                    processedComponentIds: $this->componentProcessedManager->getProcessedComponentIds(),
                );

                if (!$isReadyToSend) {
                    continue;
                }

                if ($ignoreQuery !== null && $ignoreQuery !== $searchApiRequest->ignoreQuery) {
                    continue;
                }

                if (!$this->resolveDependencies($searchApiRequestCollection, $searchApiRequest, $view)) {
                    continue;
                }

                $searchApiRequestCollection->remove($viewDataProperty, $index);

                $ignoreQuery = $searchApiRequest->ignoreQuery;
                $searchApiRequest = $this->combineSearchApiRequestsForViewDataProperty(
                    viewDataProperty          : $viewDataProperty,
                    searchApiRequestCollection: $searchApiRequestCollection,
                    originalSearchApiRequest  : $searchApiRequest,
                );
                $requests[$viewDataProperty->value] = $searchApiRequest->searchApiViewDataRequest;
                $components = [
                    ...$components,
                    ...$searchApiRequest->components,
                ];

                break;
            }
        }

        if ($requests === []) {
            return null;
        }

        return $this->create($requests, $components, $view, $ignoreQuery ?? false);
    }

    /**
     * @param array<value-of<ViewDataProperty>, SearchApiViewDataRequestInterface> $requests
     * @param ComponentInterface[]                                                 $components
     */
    private function create(
        array $requests,
        array $components,
        ViewInterface $view,
        bool $ignoreQuery
    ): ViewDataRequest
    {
        $viewDataRequest = new ViewDataRequest(
            components: $components,
        );

        $viewDataRequest->overwriteDataRequests($requests);

        if (!$ignoreQuery) {
            $viewDataRequest->setQuery(
                $view->getDataRequest()->getQuery(),
            );
        }

        $viewDataRequest->finalize($view->getDataRegistry());

        return $viewDataRequest;
    }

    private function combineSearchApiRequestsForViewDataProperty(
        ViewDataProperty $viewDataProperty,
        SearchApiRequestCollection $searchApiRequestCollection,
        SearchApiRequest $originalSearchApiRequest
    ): SearchApiRequest
    {
        $searchApiRequests = $searchApiRequestCollection->getRequestsForViewDataProperty(
            $viewDataProperty,
        );

        foreach ($searchApiRequests as $index => $searchApiRequest) {
            if ($searchApiRequest->shouldUseSameSearchApiRequest($originalSearchApiRequest)) {
                $originalSearchApiRequest->searchApiViewDataRequest->mergeWith([$searchApiRequest->searchApiViewDataRequest]);
                $originalSearchApiRequest->addComponents($searchApiRequest->components);

                $searchApiRequestCollection->remove($viewDataProperty, $index);
            }
        }

        return $originalSearchApiRequest;
    }

    private function resolveDependencies(
        SearchApiRequestCollection $searchApiRequestCollection,
        SearchApiRequest $searchApiRequest,
        ViewInterface $view
    ): bool
    {
        switch ($searchApiRequest->viewDataProperty) {
            case ViewDataProperty::CONTENT_PAGES:
                $searchApiViewDataRequest = $searchApiRequest->searchApiViewDataRequest;

                $contentPageFilterResult = $this->filterContentPage($searchApiRequestCollection, $view, $searchApiViewDataRequest);

                if (!$contentPageFilterResult) {
                    return false;
                }

                $this->filterContentPages($view, $searchApiViewDataRequest);

                return true;
            default:
                return true;
        }
    }

    private function filterContentPage(
        SearchApiRequestCollection $searchApiRequestCollection,
        ViewInterface $view,
        SearchApiViewDataRequestInterface $searchApiViewDataRequest
    ): bool
    {
        $hasRequest = $searchApiRequestCollection->hasRequestsForViewDataProperty(ViewDataProperty::CONTENT_PAGE);

        if (!$searchApiViewDataRequest instanceof ContentPagesViewDataRequest) {
            return !$hasRequest;
        }

        $contentPageRequests = $searchApiRequestCollection->getRequestsForViewDataProperty(ViewDataProperty::CONTENT_PAGE);

        foreach ($contentPageRequests as $contentPageRequest) {
            if (!$contentPageRequest->searchApiViewDataRequest instanceof ContentPageViewDataRequest) {
                continue;
            }

            if ($contentPageRequest->searchApiViewDataRequest->getPublicId() !== null) {
                return true; // publicId is already set as excluded in the request
            }
        }

        if (!$view->getDataRegistry()->has(ViewDataProperty::CONTENT_PAGE)) {
            return !$hasRequest;
        }

        $contentPage = $view->getDataRegistry()->getContentPage()->page;
        $publicId = $contentPage?->publicId;

        if ($publicId !== null) {
            $searchApiViewDataRequest->addExcludedPublicIds([$publicId]);

            return true;
        }

        return !$hasRequest;
    }

    private function filterContentPages(
        ViewInterface $view,
        SearchApiViewDataRequestInterface $searchApiViewDataRequest
    ): void
    {
        if (!$view->getDataRegistry()->has(ViewDataProperty::SEARCH_RESPONSES)) {
            return;
        }

        /** @var ContentPagesResponseContext[] $contentPageResponses */
        $contentPageResponses = $view->getDataRegistry()
            ->getSearchResponses()
            ->getSearchResponseContexts(ViewDataProperty::CONTENT_PAGES);
        $publicIds = [];

        foreach ($contentPageResponses as $contentPageResponse) {
            $contentPageResults = $contentPageResponse->getResults();

            foreach ($contentPageResults as $contentPageResult) {
                $publicIds[] = $contentPageResult->publicId;
            }
        }

        if ($publicIds !== [] && $searchApiViewDataRequest instanceof ContentPagesViewDataRequest) {
            $searchApiViewDataRequest->addExcludedPublicIds($publicIds);
        }
    }
}
