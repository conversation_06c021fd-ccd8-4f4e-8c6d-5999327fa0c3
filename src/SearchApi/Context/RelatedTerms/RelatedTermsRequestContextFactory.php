<?php

declare(strict_types=1);

namespace App\SearchApi\Context\RelatedTerms;

use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\RelatedTerms\Request\RelatedTermsRequestInterface;
use App\Search\Request\SearchRequestInterface;
use App\SearchApi\Context\RequestContextFactoryInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\RelatedTerms\Request\RelatedTermsRequestContext;

readonly class RelatedTermsRequestContextFactory implements RequestContextFactoryInterface
{
    public function __construct(
        private SearchRequestInterface $searchRequest,
        private RelatedTermsRequestInterface $relatedTermsRequest
    )
    {
    }

    private function create(int $amount, ?string $query = null): RelatedTermsRequestContext
    {
        if ($this->searchRequest->isLandingPage()) {
            $query = $this->relatedTermsRequest->getAlternateRelatedQuery() ?? $query;
        }

        return RelatedTermsRequestContext::create(
            amount: $amount,
            query : $query,
        );
    }

    public function createFromViewDataRequest(ViewDataRequest $viewDataRequest): ?RelatedTermsRequestContext
    {
        $relatedTermsViewDataRequest = $viewDataRequest->relatedTerms();

        if (!$relatedTermsViewDataRequest->isEnabled()) {
            return null;
        }

        $amount = (int)$relatedTermsViewDataRequest->getAmount();

        if ($amount < 1) {
            return null;
        }

        return $this->create(
            $amount,
            $relatedTermsViewDataRequest->getQuery(),
        );
    }
}
