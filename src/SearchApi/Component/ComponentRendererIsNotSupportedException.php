<?php

declare(strict_types=1);

namespace App\SearchApi\Component;

use App\JsonTemplate\Component\ComponentRendererInterface;

class ComponentRendererIsNotSupportedException extends \RuntimeException
{
    public static function create(ComponentRendererInterface $componentRenderer, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf(
                'Component renderer "%s" is not supported.',
                $componentRenderer::class,
            ),
            0,
            $previous,
        );
    }
}
