<?php

declare(strict_types=1);

namespace App\DisplaySearch\Controller;

use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestFlag;
use App\Search\SearchType;
use App\Statistics\Helper\StatisticsRequestFlag;
use App\Tracking\Entry\Request\TrackingEntryRequestFlag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class DisplaySearchController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer,
        private readonly RouteRegistry $routeRegistry
    )
    {
    }

    #[Route(
        path    : '/ds',
        name    : 'route_display_search',
        defaults: [
            SearchRequestFlag::IS_LANDING_PAGE                => true,
            StatisticsRequestFlag::LOG_ENABLED                => true,
            TrackingEntryRequestFlag::USE_AS_CONVERSION_ROUTE => true,
            SearchRequestFlag::TYPE                           => SearchType::DISPLAY->value,
        ],
        methods : ['GET']
    )]
    public function search(): Response
    {
        $this->routeRegistry->setSearchRoute('route_display_search_advertised');

        return $this->jsonTemplateRenderer->renderForSearchByDevice(
            '@themeJson/display_search/display_search_mobile.json',
            '@themeJson/display_search/display_search_desktop.json',
        );
    }
}
