<?php

declare(strict_types=1);

namespace App\MicrosoftSearch\Settings;

use App\Debug\Request\DebugRequestInterface;
use App\MicrosoftSearch\MicrosoftSearchModule;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class MicrosoftSearchSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private DebugRequestInterface $debugRequest
    )
    {
    }

    public static function getModuleName(): string
    {
        return MicrosoftSearchModule::getModuleName();
    }

    public function create(): MicrosoftSearchSettings
    {
        if ($this->debugRequest->enableModule()) {
            return new MicrosoftSearchSettings(
                enabled: true,
            );
        }

        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['microsoft_search'] ?? [];

        return new MicrosoftSearchSettings(
            enabled: $this->isModuleEnabled($moduleConfig),
        );
    }
}
