<?php

declare(strict_types=1);

namespace App\Ads;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class AdsModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'ads';
    }

    public function buildConfig(NodeBuilder $rootNodeChildren): void
    {
        $moduleNodeChildren = $rootNodeChildren
            ->arrayNode(self::getModuleName())
            ->addDefaultsIfNotSet()
            ->children();

        $moduleNodeChildren
            ->booleanNode('ad_test')
            ->info('Enable ad test for all ads')
            ->defaultTrue();
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $adsConfig = $this->getModuleConfig($config);

        $container->setParameter('brand_website.ads.ad_test', $adsConfig['ad_test']);
    }
}
