<?php

declare(strict_types=1);

namespace App\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

interface DependencyInjectionModuleInterface
{
    public static function getModuleName(): string;

    public function buildConfig(NodeBuilder $rootNodeChildren): void;

    /**
     * @param array<string, mixed> $config
     */
    public function prependContainer(ContainerBuilder $container, array $config): void;

    /**
     * @param array<string, mixed> $config
     */
    public function buildContainer(ContainerBuilder $container, array $config): void;
}
