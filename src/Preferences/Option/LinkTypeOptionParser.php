<?php

declare(strict_types=1);

namespace App\Preferences\Option;

use App\Preferences\Helper\PreferencesHelper;

class LinkTypeOptionParser
{
    public const string LINK_TYPE_DIRECT       = 'direct';
    public const string LINK_TYPE_TARGET_BLANK = 'targetBlank';

    public function __construct(private readonly PreferencesHelper $preferencesHelper)
    {
    }

    public function parseLinkModeOption(?string $linkModeOption = null): string
    {
        $sameWindowPreference = $this->preferencesHelper->getSameWindow();
        $preference = null;

        if ($sameWindowPreference === true) {
            $preference = self::LINK_TYPE_DIRECT;
        } elseif ($sameWindowPreference === false) {
            $preference = self::LINK_TYPE_TARGET_BLANK;
        }

        switch ($linkModeOption) {
            case LinkTypeOption::DEFAULT_DIRECT:
                return $preference ?? self::LINK_TYPE_DIRECT;
            case LinkTypeOption::DEFAULT_TARGET_BLANK:
                return $preference ?? self::LINK_TYPE_TARGET_BLANK;
            case LinkTypeOption::FORCED_DIRECT:
                return self::LINK_TYPE_DIRECT;
            case LinkTypeOption::FORCED_TARGET_BLANK:
            case null:
                return self::LINK_TYPE_TARGET_BLANK;
            default:
                // omitted intentionally
        }

        throw new \RuntimeException(sprintf('invalid link mode "%s" received', $linkModeOption));
    }
}
