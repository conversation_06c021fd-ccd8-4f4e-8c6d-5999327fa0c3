<?php

declare(strict_types=1);

namespace App\Domain\Settings;

class DomainSettingsFactory
{
    /**
     * @param mixed[] $domainConfig
     */
    public function create(array $domainConfig): DomainSettings
    {
        try {
            return new DomainSettings(
                $domainConfig[DomainSettings::KEY_HOST],
                $domainConfig[DomainSettings::KEY_JAVASCRIPT_RELATED_TERMS_ENABLED] ?? false,
            );
        } catch (\Throwable $exception) {
            throw InvalidDomainSettingsConfigException::create($exception);
        }
    }
}
