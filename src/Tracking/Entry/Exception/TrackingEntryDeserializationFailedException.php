<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Exception;

class TrackingEntryDeserializationFailedException extends TrackingEntryException
{
    public static function create(string $data, string $reason, ?\Throwable $previous = null): self
    {
        return new self(sprintf('Could not deserialize tracking entry from data "%s". Reason: %s', $data, $reason), 0, $previous);
    }

    /**
     * @param string[] $data
     */
    public static function createForArray(array $data, string $reason, ?\Throwable $previous = null): self
    {
        return self::create(
            sprintf('(array) %s', http_build_query($data)),
            $reason,
            $previous,
        );
    }
}
