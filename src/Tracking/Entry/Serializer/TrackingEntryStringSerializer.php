<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Serializer;

use App\Tracking\Entry\Exception\TrackingEntryDeserializationFailedException;
use App\Tracking\Entry\Exception\TrackingEntrySerializationFailedException;
use App\Tracking\Entry\TrackingEntry;
use Visymo\Shared\Domain\Codec\Base64UrlCodec;

readonly class TrackingEntryStringSerializer
{
    public function __construct(
        private TrackingEntryArraySerializer $trackingEntryArraySerializer,
        private Base64UrlCodec $base64UrlCodec
    )
    {
    }

    public function serialize(TrackingEntry $trackingEntry): string
    {
        try {
            $trackingEntryData = $this->trackingEntryArraySerializer->serialize($trackingEntry);
            $trackingEntryData = http_build_query($trackingEntryData);
            $trackingEntryData = @gzdeflate($trackingEntryData);

            if ($trackingEntryData === false) {
                throw new \RuntimeException('gzdeflate returned false');
            }

            return $this->base64UrlCodec->encode($trackingEntryData);
        } catch (\Throwable $exception) {
            throw TrackingEntrySerializationFailedException::create('unexpected exception during serialize', $exception);
        }
    }

    /**
     * @throws TrackingEntryDeserializationFailedException
     */
    public function deserialize(string $serializedTrackingEntry): TrackingEntry
    {
        try {
            $trackingEntryData = $this->base64UrlCodec->decode($serializedTrackingEntry);
            $trackingEntryData = @gzinflate($trackingEntryData);

            if ($trackingEntryData === false) {
                throw new \RuntimeException('gzinflate returned false');
            }

            parse_str($trackingEntryData, $parsedTrackingEntryData);

            return $this->trackingEntryArraySerializer->deserialize($parsedTrackingEntryData);
        } catch (\Throwable $exception) {
            $reason = sprintf('unexpected exception during deserialize: %s', $exception->getMessage());

            throw TrackingEntryDeserializationFailedException::create($serializedTrackingEntry, $reason);
        }
    }
}
