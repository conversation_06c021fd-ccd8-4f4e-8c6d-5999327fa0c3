<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class TrackingEntryRequest implements TrackingEntryRequestInterface
{
    private string $serializedTrackingEntry;

    private bool $useRacAsQueryFallbackFlag;

    private bool $useAsConversionRouteFlag;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getSerializedTrackingEntry(): ?string
    {
        if (!isset($this->serializedTrackingEntry)) {
            $this->serializedTrackingEntry = $this->requestManager->queryBag()->getString(
                self::PARAMETER_SERIALIZED_TRACKING_ENTRY,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->serializedTrackingEntry);
    }

    public function hasUseRacAsQueryFallbackFlag(): bool
    {
        if (!isset($this->useRacAsQueryFallbackFlag)) {
            $this->useRacAsQueryFallbackFlag = $this->requestManager->flagBag()->getBool(
                TrackingEntryRequestFlag::USE_RAC_AS_QUERY_FALLBACK,
            );
        }

        return $this->useRacAsQueryFallbackFlag;
    }

    public function hasUseAsConversionRouteFlag(): bool
    {
        if (!isset($this->useAsConversionRouteFlag)) {
            $this->useAsConversionRouteFlag = $this->requestManager->flagBag()->getBool(
                TrackingEntryRequestFlag::USE_AS_CONVERSION_ROUTE,
            );
        }

        return $this->useAsConversionRouteFlag;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_SERIALIZED_TRACKING_ENTRY           => $this->getSerializedTrackingEntry(),
            TrackingEntryRequestFlag::USE_RAC_AS_QUERY_FALLBACK => $this->hasUseRacAsQueryFallbackFlag(),
            TrackingEntryRequestFlag::USE_AS_CONVERSION_ROUTE   => $this->hasUseAsConversionRouteFlag(),
        ];
    }
}
