<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Factory;

use App\Tracking\Entry\Exception\TrackingEntryNotCreatedException;
use App\Tracking\Entry\Request\TrackingEntryRequestInterface;
use App\Tracking\Entry\Serializer\TrackingEntryStringSerializer;
use App\Tracking\Entry\TrackingEntry;

class SerializedTrackingEntryFactory implements TrackingEntryFactoryInterface
{
    private TrackingEntry $trackingEntry;

    public function __construct(
        private readonly TrackingEntryRequestInterface $trackingEntryRequest,
        private readonly TrackingEntryStringSerializer $trackingEntryStringSerializer
    )
    {
    }

    public function create(): TrackingEntry
    {
        if (isset($this->trackingEntry)) {
            return $this->trackingEntry;
        }

        $serializedTrackingEntry = $this->trackingEntryRequest->getSerializedTrackingEntry();

        if ($serializedTrackingEntry === null) {
            // Thrown each time a tracking entry is requested but not available
            throw TrackingEntryNotCreatedException::create($this, 'No serialized tracking entry available');
        }

        $this->trackingEntry = $this->trackingEntryStringSerializer->deserialize($serializedTrackingEntry);

        return $this->trackingEntry;
    }
}
