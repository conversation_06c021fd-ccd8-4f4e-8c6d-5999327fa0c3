<?php

declare(strict_types=1);

namespace App\Tracking\Model\ClickId\Factory;

use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;

class ClickIdFactory
{
    public function create(string $clickValue, string $source): ?ClickId
    {
        $clickIdSource = ClickIdSource::tryFrom($source);

        if ($clickIdSource === null || $clickValue === '') {
            return null;
        }

        $clickId = new ClickId($clickValue, $clickIdSource);

        if ($clickId->isValid() === false) {
            return null;
        }

        return $clickId;
    }

    /**
     * @param array<string, string> $data
     */
    public function fromArray(array $data): ?ClickId
    {
        if (!isset($data[ClickId::VALUE], $data[ClickId::SOURCE])) {
            return null;
        }

        $clickIdSource = ClickIdSource::tryFrom($data[ClickId::SOURCE]);

        if ($clickIdSource === null) {
            return null;
        }

        return new ClickId($data[ClickId::VALUE], $clickIdSource);
    }
}
