<?php

declare(strict_types=1);

namespace App\Tracking\Request;

use App\Generic\Device\Device;
use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;
use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\ClickId\Factory\ClickIdFactory;
use App\Tracking\Model\Network;

final class SeaRequest implements SeaRequestInterface
{
    private int $campaignId;

    private string $campaignName;

    private string $device;

    private string $network;

    private int $accountId;

    private int $adGroupId;

    private int $keywordId;

    private ?ClickId $clickId = null;

    private bool $isClickIdProcessed = false;

    private string $genericSecondaryClickId;

    private string $googleLocationId;

    private string $referrerAdCreative;

    /** @var string[] */
    private array $additionalChannel;

    private string $userQuery;

    private string $publisher;

    private int $styleId;

    private ?bool $isVbbSpamblock;

    private string $customId;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer,
        private readonly ClickIdFactory $clickIdFactory
    )
    {
    }

    public function getCampaignId(): ?int
    {
        if (!isset($this->campaignId)) {
            $this->campaignId = $this->requestManager->queryBag()->getUnsignedInt(self::PARAMETER_CAMPAIGN_ID);
        }

        return $this->requestPropertyNormalizer->getInt($this->campaignId);
    }

    public function getCampaignName(): ?string
    {
        if (!isset($this->campaignName)) {
            $this->campaignName = $this->requestManager->queryBag()->getString(self::PARAMETER_CAMPAIGN_NAME, true);
        }

        return $this->requestPropertyNormalizer->getString($this->campaignName);
    }

    public function getDevice(): ?Device
    {
        if (!isset($this->device)) {
            $this->device = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_DEVICE,
                Device::getSupportedShortValues(),
            );
        }

        return Device::tryFromShortValue($this->device);
    }

    public function getNetwork(): ?Network
    {
        if (!isset($this->network)) {
            $this->network = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_NETWORK,
                array_column(Network::cases(), 'value'),
            );
        }

        return Network::tryFrom($this->network);
    }

    public function getAccountId(): ?int
    {
        if (!isset($this->accountId)) {
            $this->accountId = $this->requestManager->queryBag()->getUnsignedInt(self::PARAMETER_ACCOUNT_ID);
        }

        return $this->requestPropertyNormalizer->getInt($this->accountId);
    }

    public function getAdGroupId(): ?int
    {
        if (!isset($this->adGroupId)) {
            $this->adGroupId = $this->requestManager->queryBag()->getUnsignedInt(self::PARAMETER_AD_GROUP_ID);
        }

        return $this->requestPropertyNormalizer->getInt($this->adGroupId);
    }

    public function getKeywordId(): ?int
    {
        if (!isset($this->keywordId)) {
            $value = $this->requestManager->queryBag()->getString(self::PARAMETER_KEYWORD_ID);

            // We only need the service keyword ID
            if ((bool)preg_match('~kwd-(\d+)~', $value, $matches)) {
                $this->keywordId = (int)$matches[1];
            } else {
                $this->keywordId = 0;
            }
        }

        return $this->requestPropertyNormalizer->getInt($this->keywordId);
    }

    public function getClickId(): ?ClickId
    {
        if (!isset($this->clickId) && !$this->isClickIdProcessed) {
            $queryBag = $this->requestManager->queryBag();
            $priorityClickId = null;

            foreach ($queryBag->getParameters() as $queryParameter) {
                if (ClickIdSource::tryFrom($queryParameter) === null) {
                    continue;
                }

                $clickId = $this->clickIdFactory->create($queryBag->getString($queryParameter), $queryParameter);

                if ($clickId === null) {
                    continue;
                }

                if ($priorityClickId === null) {
                    $priorityClickId = $clickId;

                    continue;
                }

                if ($clickId->source->getPriority() > $priorityClickId->source->getPriority()) {
                    $priorityClickId = $clickId;
                }
            }

            $this->clickId = $priorityClickId;
            $this->isClickIdProcessed = true;
        }

        return $this->clickId;
    }

    public function getGoogleLocationId(): ?string
    {
        if (!isset($this->googleLocationId)) {
            $this->googleLocationId = $this->requestManager->queryBag()->getString(
                self::PARAMETER_GOOGLE_LOCATION_ID,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->googleLocationId);
    }

    public function getGenericSecondaryClickId(): ?string
    {
        if (!isset($this->genericSecondaryClickId)) {
            $this->genericSecondaryClickId = $this->requestManager->queryBag()->getString(self::PARAMETER_GENERIC_SECONDARY_CLICK_ID);
        }

        return $this->requestPropertyNormalizer->getString($this->genericSecondaryClickId);
    }

    public function getReferrerAdCreative(): ?string
    {
        if (!isset($this->referrerAdCreative)) {
            $this->referrerAdCreative = $this->requestManager->queryBag()->getString(self::PARAMETER_REFERRER_AD_CREATIVE);
        }

        return $this->requestPropertyNormalizer->getString($this->referrerAdCreative);
    }

    /**
     * @inheritDoc
     */
    public function getAdditionalChannels(): ?array
    {
        if (!isset($this->additionalChannel)) {
            $additionalChannelsRequestValue = $this->requestManager->queryBag()->getString(
                self::PARAMETER_ADDITIONAL_CHANNEL,
            );
            $additionalChannels = [];

            foreach (explode(',', $additionalChannelsRequestValue) as $channel) {
                $channel = trim($channel);

                if (preg_match('~^[a-z_0-9]+$~', $channel) === 1) {
                    $additionalChannels[] = $channel;
                }
            }

            $this->additionalChannel = array_unique($additionalChannels);
        }

        if ($this->additionalChannel === []) {
            return null;
        }

        return $this->additionalChannel;
    }

    public function getUserQuery(): ?string
    {
        if (!isset($this->userQuery)) {
            $userQuery = $this->requestManager->queryBag()->getString(self::PARAMETER_USER_QUERY);

            $this->userQuery = $userQuery === self::IGNORED_USER_QUERY ? '' : $userQuery;
        }

        return $this->requestPropertyNormalizer->getString($this->userQuery);
    }

    public function getPublisher(): ?string
    {
        if (!isset($this->publisher)) {
            $this->publisher = $this->requestManager->queryBag()->getString(self::PARAMETER_PUBLISHER, true);
        }

        return $this->requestPropertyNormalizer->getString($this->publisher);
    }

    public function getStyleId(): ?int
    {
        if (!isset($this->styleId)) {
            $this->styleId = $this->requestManager->headersBag()->getInt(self::HEADER_X_LOADBALANCER_STYLE_ID);
        }

        return $this->requestPropertyNormalizer->getInt($this->styleId);
    }

    public function isVbbSpamblock(): ?bool
    {
        if (!isset($this->isVbbSpamblock)) {
            $this->isVbbSpamblock = $this->requestManager->headersBag()->getNullableBool(self::HEADER_X_LOADBALANCER_VBB_SPAMBLOCK);
        }

        return $this->isVbbSpamblock;
    }

    public function getCustomId(): ?string
    {
        if (!isset($this->customId)) {
            $this->customId = $this->requestManager->queryBag()->getString(self::PARAMETER_CUSTOM_ID);
        }

        return $this->requestPropertyNormalizer->getString($this->customId);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        $resultsArray = [
            self::PARAMETER_CAMPAIGN_ID                => $this->getCampaignId(),
            self::PARAMETER_CAMPAIGN_NAME              => $this->getCampaignName(),
            self::PARAMETER_DEVICE                     => $this->getDevice()?->getShortValue(),
            self::PARAMETER_NETWORK                    => $this->getNetwork()?->value,
            self::PARAMETER_ACCOUNT_ID                 => $this->getAccountId(),
            self::PARAMETER_AD_GROUP_ID                => $this->getAdGroupId(),
            self::PARAMETER_KEYWORD_ID                 => $this->getKeywordId(),
            self::PARAMETER_GOOGLE_LOCATION_ID         => $this->getGoogleLocationId(),
            self::PARAMETER_REFERRER_AD_CREATIVE       => $this->getReferrerAdCreative(),
            self::PARAMETER_ADDITIONAL_CHANNEL         => $this->getAdditionalChannels(),
            self::PARAMETER_PUBLISHER                  => $this->getPublisher(),
            self::PARAMETER_GENERIC_SECONDARY_CLICK_ID => $this->getGenericSecondaryClickId(),
            self::HEADER_X_LOADBALANCER_STYLE_ID       => $this->getStyleId(),
            self::HEADER_X_LOADBALANCER_VBB_SPAMBLOCK  => $this->isVbbSpamblock(),
            self::PARAMETER_CUSTOM_ID                  => $this->getCustomId(),
        ];

        $clickId = $this->getClickId();

        if ($clickId !== null) {
            return array_merge($resultsArray, [$clickId->source->value => $clickId->value]);
        }

        return $resultsArray;
    }

    /**
     * @inheritDoc
     */
    public function getUrlParameters(): array
    {
        return array_filter($this->toArray(), static fn ($value) => $value !== null);
    }
}
