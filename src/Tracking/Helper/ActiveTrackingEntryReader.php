<?php

declare(strict_types=1);

namespace App\Tracking\Helper;

use App\AdBot\Request\AdBotRequestInterface;
use App\Http\Request\Main\MainRequestNotAvailableException;
use App\Tracking\Entry\Exception\TrackingEntryDeserializationFailedException;
use App\Tracking\Entry\Exception\TrackingEntryExpiredException;
use App\Tracking\Entry\Exception\TrackingEntryNotCreatedException;
use App\Tracking\Entry\Factory\CommandLineTrackingEntryFactory;
use App\Tracking\Entry\Factory\FallbackTrackingEntryFactory;
use App\Tracking\Entry\Factory\RequestTrackingEntryFactory;
use App\Tracking\Entry\Factory\SerializedTrackingEntryFactory;
use App\Tracking\Entry\Mutator\TrackingEntryMutatorInterface;
use App\Tracking\Entry\TrackingEntry;
use Psr\Log\LoggerInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

readonly class ActiveTrackingEntryReader
{
    /**
     * @param \Traversable<TrackingEntryMutatorInterface> $trackingEntryMutators
     */
    public function __construct(
        private AdBotRequestInterface $adBotRequest,
        private SerializedTrackingEntryFactory $serializedTrackingEntryFactory,
        private RequestTrackingEntryFactory $requestTrackingEntryFactory,
        private CommandLineTrackingEntryFactory $commandLineTrackingEntryFactory,
        private FallbackTrackingEntryFactory $fallbackTrackingEntryFactory,
        private DateTimeFactory $dateTimeFactory,
        private LoggerInterface $logger,
        private iterable $trackingEntryMutators
    )
    {
    }

    public function determineActiveTrackingEntry(): TrackingEntry
    {
        $trackingEntry = $this->getTrackingEntry();

        foreach ($this->trackingEntryMutators as $trackingEntryMutator) {
            $trackingEntry = $trackingEntryMutator->mutate($trackingEntry);
        }

        return $trackingEntry;
    }

    private function getTrackingEntry(): TrackingEntry
    {
        try {
            // always use a fallback tracking entry for ad bots
            if ($this->adBotRequest->isAdBot()) {
                return $this->fallbackTrackingEntryFactory->create();
            }

            // Try to load serialized tracking entry from request
            $trackingEntry = $this->serializedTrackingEntryFactory->create();

            if (!$trackingEntry->isFresh($this->dateTimeFactory->createNow())) {
                // Ignore tracking entries older than TTL days
                throw TrackingEntryExpiredException::create(TrackingEntry::TTL_IN_DAYS);
            }

            return $trackingEntry;
        } catch (MainRequestNotAvailableException) {
            // this happens on CLI, when there is no request available
            return $this->commandLineTrackingEntryFactory->create();
        } catch (TrackingEntryDeserializationFailedException $exception) {
            $this->logger->notice(
                'Failed to deserialize tracking entry from url. Ignoring this entry. ({message})',
                [
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                ],
            );
        } catch (TrackingEntryNotCreatedException|TrackingEntryExpiredException) {
            // Ignore, this is expected
        }

        try {
            // Try to create new tracking entry from current request.
            return $this->requestTrackingEntryFactory->create();
        } catch (TrackingEntryNotCreatedException) {
            // Ignore, this is expected
        }

        // Use fallback tracking entry
        return $this->fallbackTrackingEntryFactory->create();
    }
}
