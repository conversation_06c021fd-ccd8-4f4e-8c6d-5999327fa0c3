<?php

declare(strict_types=1);

namespace App\ModuleSettings\Value;

final class ModuleValueFactory
{
    public function create(
        string $moduleName,
        string $property,
        string $title,
        bool|int|string|ModuleEmptyValue|null $value
    ): ModuleValue
    {
        return new ModuleValue(
            property: sprintf('%s.%s', $moduleName, $property),
            title   : $title,
            value   : $value,
        );
    }
}
