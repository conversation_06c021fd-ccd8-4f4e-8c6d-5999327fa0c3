<?php

declare(strict_types=1);

namespace App\DisplaySearchRelated\Controller;

use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestFlag;
use App\Search\SearchType;
use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Statistics\Helper\StatisticsRequestFlag;
use App\Tracking\Entry\Request\TrackingEntryRequestFlag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class DisplaySearchRelatedController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer,
        private readonly RouteRegistry $routeRegistry,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader
    )
    {
    }

    #[Route(
        path    : '/dsr',
        name    : 'route_display_search_related',
        defaults: [
            SearchRequestFlag::IS_LANDING_PAGE                => true,
            StatisticsRequestFlag::LOG_ENABLED                => true,
            TrackingEntryRequestFlag::USE_AS_CONVERSION_ROUTE => true,
            SearchRequestFlag::TYPE                           => SearchType::DISPLAY->value,
        ],
        methods : ['GET']
    )]
    public function search(): Response
    {
        $this->routeRegistry->setSearchRoute('route_display_search_related_web');

        if ($this->splitTestExtendedReader->isOneOfVariantsActive(['tkab', 'tkab2', 'fbab'])) {
            return $this->jsonTemplateRenderer->renderForSearch(
                jsonTemplateFile: '@themeJson/display_search_related/display_search_related-ab-tiktok.json',
            );
        }

        if ($this->splitTestExtendedReader->isVariantActive('acwfdtl')) {
            return $this->jsonTemplateRenderer->renderForSearch(
                jsonTemplateFile: '@themeJson/display_search_related/display_search_related-ab-desktop.json',
            );
        }

        if ($this->splitTestExtendedReader->isVariantActive('acwfdtd')) {
            return $this->jsonTemplateRenderer->renderForSearch(
                jsonTemplateFile: '@themeJson/display_search_related/display_search_related-dark-ab-desktop.json',
            );
        }

        if ($this->splitTestExtendedReader->isVariantActive('krdd')) {
            return $this->jsonTemplateRenderer->renderForSearch(
                jsonTemplateFile: '@themeJson/display_search_related/display_search_related-krdd-desktop.json',
            );
        }

        if ($this->splitTestExtendedReader->isVariantActive('drkt')) {
            return $this->jsonTemplateRenderer->renderForSearch(
                jsonTemplateFile: '@themeJson/display_search_related/display_search_related-ab-drkt.json',
            );
        }

        return $this->jsonTemplateRenderer->renderForSearch(
            jsonTemplateFile: '@themeJson/display_search_related/display_search_related.json',
        );
    }
}
