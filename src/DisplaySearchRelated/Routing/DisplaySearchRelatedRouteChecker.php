<?php

declare(strict_types=1);

namespace App\DisplaySearchRelated\Routing;

use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettings;
use App\Generic\Routing\RouteCheckerInterface;
use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: self::ALIAS)]
final readonly class DisplaySearchRelatedRouteChecker implements RouteCheckerInterface
{
    private const string ALIAS = 'route_checker_display_search_related';

    public function __construct(
        private DisplaySearchRelatedSettings $displaySearchRelatedSettings
    )
    {
    }

    public static function getAlias(): string
    {
        return self::ALIAS;
    }

    public function check(): bool
    {
        return $this->displaySearchRelatedSettings->enabled;
    }
}
