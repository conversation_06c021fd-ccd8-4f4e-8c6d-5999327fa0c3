<?php

declare(strict_types=1);

namespace App\Assets\DebugInfoProvider;

use App\Assets\DebugAssetsHelper;
use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;

class AssetsDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_ASSETS = 'assets';

    public function __construct(
        private readonly DebugAssetsHelper $debugAssetsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        return [
            new DebugInfo(
                self::KEY_ASSETS,
                [
                    $this->debugAssetsHelper->getDebugInfo(),
                ],
            ),
        ];
    }

    public static function getDefaultPriority(): int
    {
        return 0;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_ASSETS,
        ];
    }
}
