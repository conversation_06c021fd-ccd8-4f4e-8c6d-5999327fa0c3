<?php

declare(strict_types=1);

namespace App\Assets;

use App\Debug\Request\DebugRequestInterface;

class DebugAssetsHelper
{
    /** @var mixed[] */
    private array $debugInfo = [];

    public function __construct(
        private readonly DebugRequestInterface $debugRequest
    )
    {
    }

    /**
     * @return mixed[]
     */
    public function getDebugInfo(): array
    {
        return $this->debugInfo;
    }

    public function addCssDebugInfo(string $buildFilePath): void
    {
        if (!$this->debugRequest->debugInfo()) {
            return;
        }

        $this->debugInfo[$this->getFilePath($buildFilePath)] = 'inline';
    }

    public function addJavaScriptDebugInfo(string $buildFilePath, bool $inline, bool $async): void
    {
        if (!$this->debugRequest->debugInfo()) {
            return;
        }

        if ($inline) {
            $loaded = 'inline';
        } elseif ($async) {
            $loaded = 'async';
        } else {
            $loaded = 'sync';
        }

        $this->debugInfo[$this->getFilePath($buildFilePath)] = $loaded;
    }

    private function getFilePath(string $buildFilePath): string
    {
        if (preg_match('~/public/build(/.+)$~', $buildFilePath, $matches) === 1) {
            return $matches[1];
        }

        return $buildFilePath;
    }
}
