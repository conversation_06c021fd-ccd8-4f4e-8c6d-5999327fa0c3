<?php

declare(strict_types=1);

namespace App\Assets\Controller;

use App\BrandAssets\Exception\BrandAssetsImageNotFoundException;
use App\BrandAssets\File\BrandAssetsImageFileName;
use App\BrandAssets\Settings\BrandAssetsSettings;
use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Response\ResponseCachingHelper;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class AssetsController extends AbstractController
{
    private const string FILE_EXTENSION_ICO = 'ico';
    private const string FILE_EXTENSION_JPG = 'jpg';
    private const string FILE_EXTENSION_PNG = 'png';
    private const string FILE_EXTENSION_SVG = 'svg';

    private const int CACHE_TIME_TO_LIVE_IN_SECONDS = 86400; // 1 day

    public function __construct(
        private readonly WebsiteSettingsHelper $websiteSettingsHelper,
        private readonly ResponseCachingHelper $responseCachingHelper,
        private readonly RequestInfoInterface $requestInfo,
        private readonly BrandAssetsSettings $brandAssetsSettings
    )
    {
    }

    #[Route(
        path   : '/favicon.ico',
        name   : 'route_assets_favicon_ico',
        methods: ['GET']
    )]
    public function faviconIco(): Response
    {
        return $this->getImageResponse(
            $this->brandAssetsSettings->getImage(BrandAssetsImageFileName::FAVICON_ICO, true),
            self::FILE_EXTENSION_ICO,
        );
    }

    #[Route(
        path   : '/apple-touch-icon.png',
        name   : 'route_assets_apple_touch_icon_png',
        methods: ['GET']
    )]
    public function appleTouchIconPng(): Response
    {
        return $this->getImageResponse(
            $this->brandAssetsSettings->getImage(BrandAssetsImageFileName::APPLE_TOUCH_ICON_PNG, true),
            self::FILE_EXTENSION_PNG,
        );
    }

    #[Route(
        path        : '/build/{brandSlug}/images/image/{fileName}.{_format}',
        name        : 'route_brand_asset_images_image',
        requirements: [
            'brandSlug' => '^[a-z0-9]+$',
            'fileName'  => '^[a-z-]+$',
            '_format'   => 'jpg',
        ],
        methods     : ['GET']
    )]
    public function brandAssetImagesHeader(
        string $brandSlug,
        string $fileName,
        string $_format
    ): Response
    {
        if ($brandSlug !== $this->brandAssetsSettings->brandSlug) {
            throw $this->createNotFoundException();
        }

        $imageFileName = BrandAssetsImageFileName::tryFrom(sprintf('%s.%s', $fileName, $_format));

        if ($imageFileName === null) {
            throw $this->createNotFoundException();
        }

        try {
            return $this->getImageResponse(
                $this->brandAssetsSettings->getImage($imageFileName, true),
                $_format,
            );
        } catch (BrandAssetsImageNotFoundException) {
            throw $this->createNotFoundException();
        }
    }

    #[Route(
        path        : '/build/{brandSlug}/images/logo/{fileName}.{_format}',
        name        : 'route_brand_asset_images_logo',
        requirements: [
            'brandSlug' => '^[a-z0-9]+$',
            'fileName'  => '^[a-z-]+$',
            '_format'   => 'png|svg',
        ],
        methods     : ['GET']
    )]
    public function brandAssetImagesLogo(
        string $brandSlug,
        string $fileName,
        string $_format
    ): Response
    {
        if ($brandSlug !== $this->brandAssetsSettings->brandSlug) {
            throw $this->createNotFoundException();
        }

        $imageFileName = BrandAssetsImageFileName::tryFrom(
            sprintf('%s.%s', $fileName, $_format),
        );

        if ($imageFileName === null) {
            throw $this->createNotFoundException();
        }

        return $this->getImageResponse(
            $this->brandAssetsSettings->getImage($imageFileName, true),
            $_format,
        );
    }

    /**
     * Note: Visymo.com has his own ads.txt. If adsTxt changes it should also change there.
     * Reference: SF-811
     */
    #[Route(
        path   : '/ads.txt',
        name   : 'route_assets_ads_txt',
        methods: ['GET']
    )]
    public function adsTxt(): Response
    {
        $contractType = $this->websiteSettingsHelper->getSettings()->getGoogleAdSense()->getContractType();

        $adsTxt = [
            'google.com, pub-9560180491300958, DIRECT, f08c47fec0942fa0',
        ];

        if ($contractType->isOnline()) {
            $adsTxt[] = 'google.com, pub-4811641726270552, DIRECT, f08c47fec0942fa0';
        } else {
            $adsTxt[] = 'google.com, vinden, DIRECT, f08c47fec0942fa0';
        }

        $response = new Response(implode("\n", $adsTxt));
        $response->headers->set('Content-Type', 'text/plain; charset=UTF-8');

        return $response;
    }

    private function getImageResponse(?string $fileContents, string $fileExtension): Response
    {
        if ($fileContents === null) {
            throw $this->createNotFoundException();
        }

        $contentType = match ($fileExtension) {
            self::FILE_EXTENSION_ICO => 'image/x-icon',
            self::FILE_EXTENSION_JPG => 'image/jpeg',
            self::FILE_EXTENSION_PNG => 'image/png',
            self::FILE_EXTENSION_SVG => 'image/svg+xml',
            default                  => throw $this->createNotFoundException()
        };

        if (!$this->requestInfo->hasUrlParameters()) {
            $this->responseCachingHelper->startResponseCaching(self::CACHE_TIME_TO_LIVE_IN_SECONDS);
        }

        $response = new Response($fileContents);
        $response->headers->set('Content-Type', $contentType);

        return $response;
    }
}
