<?php

declare(strict_types=1);

namespace App\ContentPageHome\Routing;

use App\ContentPageHome\Settings\ContentPageHomeSettings;
use App\Generic\Routing\RouteCheckerInterface;
use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: self::ALIAS)]
final readonly class ContentPageHomeRouteChecker implements RouteCheckerInterface
{
    private const string ALIAS = 'route_checker_content_page_home';

    public function __construct(
        private ContentPageHomeSettings $contentPageHomeSettings
    )
    {
    }

    public static function getAlias(): string
    {
        return self::ALIAS;
    }

    public function check(): bool
    {
        return $this->contentPageHomeSettings->enabled;
    }
}
