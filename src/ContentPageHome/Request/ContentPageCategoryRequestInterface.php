<?php

declare(strict_types=1);

namespace App\ContentPageHome\Request;

use App\Http\Request\RequestInterface;

interface ContentPageCategoryRequestInterface extends RequestInterface
{
    public const string ATTRIBUTE_CATEGORY_PUBLIC_ID = 'categoryPublicId';
    public const string ATTRIBUTE_CATEGORY_SLUG      = 'categorySlug';

    public function getPublicId(): ?int;

    public function getSlug(): ?string;
}
