<?php

declare(strict_types=1);

namespace App\ContentPageHome\Settings;

use App\ContentPageHome\Type\ContentPageHomeType;
use App\ModuleSettings\ModuleSettingsInterface;

readonly class ContentPageHomeSettings implements ModuleSettingsInterface
{
    public function __construct(
        public bool $enabled,
        public ?ContentPageHomeType $type,
        public ?string $searchRoute
    )
    {
    }
}
