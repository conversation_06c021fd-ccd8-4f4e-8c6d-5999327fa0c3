<?php

declare(strict_types=1);

namespace App\ContentPageHome\Settings;

use App\ContentPageHome\ContentPageHomeModule;
use App\ContentPageHome\Type\ContentPageHomeType;
use App\Debug\Request\DebugRequestInterface;
use App\Http\Url\UrlValidationRouter;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\ModuleSettings\Exception\InvalidModuleConfigException;
use App\Search\Registry\RouteRegistry;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class ContentPageHomeSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    private const string KEY_TYPE         = 'type';
    private const string KEY_SEARCH_ROUTE = 'search_route';

    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private UrlValidationRouter $urlValidationRouter,
        private DebugRequestInterface $debugRequest,
        private RouteRegistry $routeRegistry
    )
    {
    }

    public static function getModuleName(): string
    {
        return ContentPageHomeModule::getModuleName();
    }

    public function create(): ContentPageHomeSettings
    {
        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['content_page_home'];
        $debugContentPage = ContentPageHomeType::tryFrom((string)$this->debugRequest->getContentPageHomeType());

        if ($debugContentPage !== null) {
            return new ContentPageHomeSettings(
                enabled    : true,
                type       : $debugContentPage,
                searchRoute: $moduleConfig[self::KEY_SEARCH_ROUTE] ?? $this->routeRegistry->getSearchRoute(),
            );
        }

        if (!$this->isModuleEnabled($moduleConfig)) {
            return new ContentPageHomeSettings(
                enabled    : false,
                type       : null,
                searchRoute: null,
            );
        }

        $settings = new ContentPageHomeSettings(
            enabled    : true,
            type       : ContentPageHomeType::from($moduleConfig[self::KEY_TYPE]),
            searchRoute: $moduleConfig[self::KEY_SEARCH_ROUTE],
        );
        $this->validateSettings($settings);

        return $settings;
    }

    private function validateSettings(ContentPageHomeSettings $settings): void
    {
        if (!$settings->enabled) {
            return;
        }

        if ($settings->type === null) {
            throw InvalidModuleConfigException::create(
                self::getModuleName(),
                'Content page home type must be set',
            );
        }

        if ($settings->searchRoute === null) {
            return;
        }

        $routeErrorMessage = $this->urlValidationRouter->validate($settings->searchRoute);

        if ($routeErrorMessage !== null) {
            throw InvalidModuleConfigException::create(
                self::getModuleName(),
                sprintf(
                    '"%s.%s" requires a valid route: %s',
                    self::getModuleName(),
                    self::KEY_SEARCH_ROUTE,
                    $routeErrorMessage,
                ),
            );
        }
    }
}
