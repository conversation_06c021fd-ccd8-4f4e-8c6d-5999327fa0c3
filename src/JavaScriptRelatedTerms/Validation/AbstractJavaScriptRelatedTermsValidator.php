<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Validation;

use App\Brand\Settings\BrandSettingsHelper;
use App\Campaign\Settings\CampaignSettingsHelper;
use App\JavaScriptRelatedTerms\Validation\Exception\ParameterValidationException;
use App\Locale\Request\LocaleRequestInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\Tracking\Request\SeaRequestInterface;
use App\Tracking\Settings\TrackingSettings;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Psr\Log\LoggerInterface;

abstract readonly class AbstractJavaScriptRelatedTermsValidator
{
    public function __construct(
        protected WebsiteSettingsHelper $websiteSettingsHelper,
        protected BrandSettingsHelper $brandSettingsHelper,
        protected LocaleRequestInterface $localeRequest,
        protected SeaRequestInterface $seaRequest,
        protected LoggerInterface $logger,
        private TrackingSettings $trackingSettings,
        private CampaignSettingsHelper $campaignSettingsHelper,
        private LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    /**
     * @return string[]
     *
     * @throws ParameterValidationException
     */
    public function validate(): array
    {
        $warnings = [];

        $brandSettings = $this->brandSettingsHelper->getSettings();
        $campaignSettings = $this->campaignSettingsHelper->getSettings();
        $localeSettings = $this->localeSettingsHelper->getSettings();

        $locale = $this->localeRequest->getLocale();
        $campaignName = $this->seaRequest->getCampaignName();

        if ($locale !== null && $localeSettings->locale->code !== $locale) {
            $this->logger->warning(
                'Invalid {parameter} parameter received for js related terms at {brand}',
                [
                    'parameter'        => LocaleRequestInterface::PARAMETER_LOCALE,
                    'brand'            => $brandSettings->getName(),
                    'locale_parameter' => $locale,
                    'active_locale'    => $localeSettings->locale->code,
                ],
            );

            $warnings[] = sprintf(
                '_vrt validation warning: invalid "%s" parameter, value not supported',
                LocaleRequestInterface::PARAMETER_LOCALE,
            );
        }

        if ($campaignName === null) {
            $this->logger->error(
                'Required parameter "{parameter}" not received for js related terms at {brand}',
                [
                    'parameter' => SeaRequestInterface::PARAMETER_CAMPAIGN_NAME,
                    'brand'     => $brandSettings->getName(),
                ],
            );

            throw ParameterValidationException::createForRequiredParameter(
                SeaRequestInterface::PARAMETER_CAMPAIGN_NAME,
            );
        }

        if ($campaignSettings === null && $this->trackingSettings->campaignNameValidationEnabled) {
            $this->logger->warning(
                'Invalid {parameter} parameter received for js related terms at {brand}',
                [
                    'parameter'     => SeaRequestInterface::PARAMETER_CAMPAIGN_NAME,
                    'brand'         => $brandSettings->getName(),
                    'campaign_name' => $campaignName,
                ],
            );

            $warnings[] = sprintf(
                '_vrt validation warning: invalid "%s" parameter, value not supported',
                SeaRequestInterface::PARAMETER_CAMPAIGN_NAME,
            );
        }

        return $warnings;
    }
}
