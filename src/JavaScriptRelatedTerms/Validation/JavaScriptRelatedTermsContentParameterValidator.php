<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Validation;

use App\JavaScriptRelatedTerms\Validation\Exception\ParameterValidationException;
use App\Tracking\Request\SeaRequestInterface;

final readonly class JavaScriptRelatedTermsContentParameterValidator extends AbstractJavaScriptRelatedTermsValidator
{
    /**
     * @return string[] warning messages
     *
     * @throws ParameterValidationException
     */
    public function validate(): array
    {
        $brandSettings = $this->brandSettingsHelper->getSettings();
        $referrerAdCreative = $this->seaRequest->getReferrerAdCreative();

        if ($referrerAdCreative === null) {
            $this->logger->error(
                'Required parameter "{parameter}" not received for js related terms online at {brand}',
                [
                    'parameter' => SeaRequestInterface::PARAMETER_REFERRER_AD_CREATIVE,
                    'brand'     => $brandSettings->getName(),
                ],
            );

            throw ParameterValidationException::createForRequiredParameter(
                SeaRequestInterface::PARAMETER_REFERRER_AD_CREATIVE,
            );
        }

        return parent::validate();
    }
}
