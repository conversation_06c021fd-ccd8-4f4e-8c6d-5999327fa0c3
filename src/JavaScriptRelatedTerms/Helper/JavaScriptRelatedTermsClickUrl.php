<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Helper;

use App\Domain\Settings\DomainSettingsRepository;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;
use App\Locale\Request\LocaleRequestInterface;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Request\SeaRequestInterface;
use Psr\Log\LoggerInterface;

class JavaScriptRelatedTermsClickUrl
{
    private const array REMOVE_SEARCH_PARAMETERS = [
        SearchRequestInterface::PARAMETER_QUERY,
        SearchRequestInterface::PARAMETER_PAGE,
    ];

    private ?string $domain = null;

    public function __construct(
        private readonly LocaleRequestInterface $localeRequest,
        private readonly SearchRequestInterface $searchRequest,
        private readonly SeaRequestInterface $seaRequest,
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private readonly DomainSettingsRepository $domainSettingsRepository,
        private readonly LoggerInterface $logger
    )
    {
    }

    public function createWithRoute(string $route): string
    {
        $domain = $this->getDomain();

        // Include only URL parameters we recognize
        $urlParameters = [
            ...$this->localeRequest->getUrlParameters(),
            ...$this->searchRequest->getUrlParameters(),
            ...$this->seaRequest->getUrlParameters(),
        ];

        // Remove query and page parameters for related terms search links
        $urlParameters = array_diff_key($urlParameters, array_flip(self::REMOVE_SEARCH_PARAMETERS));

        return $this->persistentUrlParametersRouter->generateForDomain(
            $domain,
            $route,
            $urlParameters,
            PersistentUrlParametersPageType::RELATED_TERMS,
        );
    }

    private function getDomain(): string
    {
        $locale = $this->localeRequest->getLocale();

        if (isset($this->domain)) {
            return $this->domain;
        }

        if ($locale !== null) {
            $this->domain = $this->domainSettingsRepository->getJavaScriptRelatedTermsEnabledDomainByLocale($locale);
        }

        if ($this->domain === null) {
            $this->domain = $this->domainSettingsRepository->getJavaScriptRelatedTermsEnabledDomain();
        }

        // Fallback on first domain we find.
        if ($this->domain === null) {
            $this->logger->warning(
                'No domain found with JavaScript related terms enabled, falling back on first domain',
            );

            $this->domain = $this->domainSettingsRepository->getFirstDomain();
        }

        return $this->domain;
    }

    public function replaceRequestHostWithDomain(string $requestHost, string $url): string
    {
        $domain = $this->getDomain();

        return str_replace($requestHost, $domain, $url);
    }
}
