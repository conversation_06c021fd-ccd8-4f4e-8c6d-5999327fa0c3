<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Helper;

use App\Brand\Settings\BrandSettingsHelper;
use App\Http\Request\Info\RequestInfoInterface;
use App\JavaScriptRelatedTerms\Request\JavaScriptRelatedTermsViewRequestInterface;
use Psr\Log\LoggerInterface;

readonly class JavaScriptRelatedTermsViewLogHelper
{
    public function __construct(
        private BrandSettingsHelper $brandSettingsHelper,
        private JavaScriptRelatedTermsViewRequestInterface $javaScriptRelatedTermsViewRequest,
        private RequestInfoInterface $requestInfo,
        private LoggerInterface $javaScriptRelatedTermsViewLogger
    )
    {
    }

    public function logView(string $endpoint): void
    {
        $url = $this->javaScriptRelatedTermsViewRequest->getUrl();

        if ($url === null) {
            return;
        }

        $context = [
            'brand_slug' => $this->brandSettingsHelper->getSettings()->getSlug(),
            'endpoint'   => $endpoint,
            'ip'         => $this->requestInfo->getUserIp(),
            'query'      => $this->javaScriptRelatedTermsViewRequest->getQuery(),
            'url'        => $url,
        ];

        $this->javaScriptRelatedTermsViewLogger->info('', $context);
    }
}
