<?php

declare(strict_types=1);

namespace App\Account\Settings;

use App\WebsiteSettings\Settings\Exception\InvalidWebsiteSettingsConfigException;

class InvalidAccountSettingsConfigException extends InvalidWebsiteSettingsConfigException
{
    public static function create(\Throwable $previous): self
    {
        return new self(
            sprintf('Invalid account settings config given: %s', $previous->getMessage()),
            0,
            $previous,
        );
    }
}
