<?php

declare(strict_types=1);

namespace App\Account\Settings;

use App\Account\Service\AccountService;

final readonly class AccountSettings
{
    public const string KEY_ID                                         = 'id';
    public const string KEY_NAME                                       = 'name';
    public const string KEY_SERVICE                                    = 'service';
    public const string KEY_PAYMENT_MODE                               = 'payment_mode';
    public const string KEY_CAMPAIGNS                                  = 'campaigns_v2';
    public const string KEY_EXCLUDE_COUNTRIES_FROM_CONVERSION_TRACKING = 'exclude_countries_from_conversion_tracking';

    public const string PAYMENT_MODE_CLICKS      = 'clicks';
    public const string PAYMENT_MODE_CONVERSIONS = 'conversions';

    public const array PAYMENT_MODES = [
        self::PAYMENT_MODE_CLICKS,
        self::PAYMENT_MODE_CONVERSIONS,
    ];

    public ?string $paymentMode;

    /**
     * @param string[] $excludeCountriesFromConversionTracking
     */
    public function __construct(
        public int $id,
        public string $name,
        public AccountService $service,
        ?string $paymentMode,
        public array $excludeCountriesFromConversionTracking
    )
    {
        $this->paymentMode = $paymentMode !== null ? mb_strtolower($paymentMode) : null;
    }
}
