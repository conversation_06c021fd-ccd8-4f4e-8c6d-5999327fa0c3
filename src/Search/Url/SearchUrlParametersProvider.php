<?php

declare(strict_types=1);

namespace App\Search\Url;

use App\Http\Url\AbstractCachedPersistentUrlParametersProvider;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Search\Request\SearchRequestInterface;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;

class SearchUrlParametersProvider extends AbstractCachedPersistentUrlParametersProvider
{
    public function __construct(
        private readonly SearchRequestInterface $searchRequest,
        private readonly TrademarkInfringementResultBlocker $trademarkInfringementResultBlocker
    )
    {
    }

    /**
     * @inheritDoc
     */
    protected function createPersistentUrlParameters(PersistentUrlParametersPageType $pageType): array
    {
        if ($this->searchRequest->getQuery() === null) {
            return [];
        }

        if ($this->trademarkInfringementResultBlocker->blockResults()) {
            return [];
        }

        $addQuery = match ($pageType) {
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            PersistentUrlParametersPageType::DEFAULT       => true,
            PersistentUrlParametersPageType::NEW_SEARCH,
            PersistentUrlParametersPageType::RELATED_TERMS => false,
        };

        if (!$addQuery) {
            return [];
        }

        $persistentUrlParameters = [
            SearchRequestInterface::PARAMETER_QUERY => $this->searchRequest->getQuery(),
        ];

        return match ($pageType) {
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            PersistentUrlParametersPageType::DEFAULT,
            PersistentUrlParametersPageType::NEW_SEARCH,
            PersistentUrlParametersPageType::RELATED_TERMS => $persistentUrlParameters,
        };
    }
}
