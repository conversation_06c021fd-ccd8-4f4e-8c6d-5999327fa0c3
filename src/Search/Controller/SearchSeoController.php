<?php

declare(strict_types=1);

namespace App\Search\Controller;

use App\Search\Request\SearchRequestFlag;
use App\Search\SearchType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class SearchSeoController extends AbstractController
{
    public const string PARAMETER_QUERY = 'query';

    public function __construct(
        private readonly SearchController $searchController
    )
    {
    }

    #[Route(
        path        : '/mk/{query}',
        name        : 'route_search_seo_mk',
        requirements: ['query' => '.+'],
        defaults    : [
            SearchRequestFlag::QUERY_IN_PATH         => true,
            SearchRequestFlag::IS_SEO_PAGE           => true,
            SearchRequestFlag::LIMIT_AMOUNT_OF_PAGES => true,
            SearchRequestFlag::TYPE                  => SearchType::SEARCH->value,
        ],
        methods     : ['GET']
    )]
    public function search(): Response
    {
        return $this->searchController->search();
    }
}
