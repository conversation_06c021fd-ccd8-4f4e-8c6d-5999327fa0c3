<?php

declare(strict_types=1);

namespace App\Search\Settings;

use App\Debug\Request\DebugRequestInterface;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\Search\SearchModule;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class SearchSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    private const string KEY_SEO_ENABLED      = 'seo_enabled';
    private const string KEY_STYLE_ID_DESKTOP = 'style_id_desktop';
    private const string KEY_STYLE_ID_MOBILE  = 'style_id_mobile';

    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private DebugRequestInterface $debugRequest
    )
    {
    }

    public static function getModuleName(): string
    {
        return SearchModule::getModuleName();
    }

    public function create(): SearchSettings
    {
        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['search'];

        if ($this->debugRequest->enableModule()) {
            $styleId = $this->debugRequest->forceStyleId() ?? DebugRequestInterface::DEBUG_GOOGLE_AD_STYLE_ID;

            return new SearchSettings(
                enabled       : true,
                seoEnabled    : (bool)($moduleConfig[self::KEY_SEO_ENABLED] ?? true),
                styleIdDesktop: (int)($moduleConfig[self::KEY_STYLE_ID_DESKTOP] ?? $styleId),
                styleIdMobile : (int)($moduleConfig[self::KEY_STYLE_ID_MOBILE] ?? $styleId),
            );
        }

        if (!$this->isModuleEnabled($moduleConfig)) {
            return new SearchSettings(
                enabled       : false,
                seoEnabled    : false,
                styleIdDesktop: null,
                styleIdMobile : null,
            );
        }

        return new SearchSettings(
            enabled       : true,
            seoEnabled    : (bool)$moduleConfig[self::KEY_SEO_ENABLED],
            styleIdDesktop: (int)$moduleConfig[self::KEY_STYLE_ID_DESKTOP],
            styleIdMobile : (int)$moduleConfig[self::KEY_STYLE_ID_MOBILE],
        );
    }
}
