<?php

declare(strict_types=1);

namespace App\Http\Request\Info;

use App\Http\Request\Main\MainRequestInterface;
use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;
use App\Http\Url\DevelopHostHelper;

final class RequestInfo implements RequestInfoInterface
{
    private const string USER_AGENT_CLIENT_HINTS_HEADER_PREFIX           = 'sec-ch-ua';
    private const int    USER_AGENT_CLIENT_HINTS_MAX_HEADER_LENGTH       = 64;
    private const int    USER_AGENT_CLIENT_HINTS_MAX_HEADER_VALUE_LENGTH = 512;

    private string $url;

    private string $relativeUrl;

    private string $route;

    private string $locale;

    private string $userIp;

    private string $userAgent;

    /** @var array<string, string> */
    private array $userAgentClientHints;

    private string $acceptLanguage;

    private string $host;

    private string $normalisedHost;

    private string $tlsVersion;

    private string $referer;

    private string $pathInfo;

    private string $method;

    private string $scheme;

    private string $protocol;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer,
        private readonly MainRequestInterface $mainRequest,
        private readonly DevelopHostHelper $developHostHelper
    )
    {
    }

    public function getUrl(): string
    {
        if (!isset($this->url)) {
            $this->url = $this->mainRequest->getRequest()->getUri();
        }

        return $this->url;
    }

    public function getRelativeUrl(): string
    {
        if (!isset($this->relativeUrl)) {
            $this->relativeUrl = $this->mainRequest->getRequest()->getRequestUri();
        }

        return $this->relativeUrl;
    }

    public function getRoute(): string
    {
        if (!isset($this->route) || $this->route === '') {
            $this->route = $this->requestManager->attributesBag()->getString('_route');
        }

        return $this->route;
    }

    public function isRoute(string $route): bool
    {
        return $this->getRoute() === $route;
    }

    public function getLocale(): string
    {
        if (!isset($this->locale)) {
            $this->locale = $this->mainRequest->getRequest()->getLocale();
        }

        return $this->locale;
    }

    public function getUserIp(): ?string
    {
        if (!isset($this->userIp)) {
            $this->userIp = (string)$this->mainRequest->getRequest()->getClientIp();
        }

        return $this->requestPropertyNormalizer->getString($this->userIp);
    }

    public function getUserAgent(): string
    {
        if (!isset($this->userAgent)) {
            $this->userAgent = $this->requestManager->headersBag()->getString('User-Agent');
        }

        return $this->userAgent;
    }

    /**
     * @inheritDoc
     */
    public function getUserAgentClientHints(): array
    {
        if (!isset($this->userAgentClientHints)) {
            $headersBag = $this->requestManager->headersBag();
            $this->userAgentClientHints = [];

            foreach ($headersBag->getParameters() as $parameter) {
                // Collect user agent client hints headers only
                if (!str_starts_with($parameter, self::USER_AGENT_CLIENT_HINTS_HEADER_PREFIX)) {
                    continue;
                }

                if (strlen($parameter) > self::USER_AGENT_CLIENT_HINTS_MAX_HEADER_LENGTH) {
                    continue;
                }

                $value = $headersBag->getNullableString($parameter);

                if ($value === null) {
                    continue;
                }

                if (strlen($value) > self::USER_AGENT_CLIENT_HINTS_MAX_HEADER_VALUE_LENGTH) {
                    continue;
                }

                $this->userAgentClientHints[$parameter] = $value;
            }
        }

        return $this->userAgentClientHints;
    }

    public function getAcceptLanguage(): string
    {
        if (!isset($this->acceptLanguage)) {
            $this->acceptLanguage = $this->requestManager->headersBag()->getString('Accept-Language');
        }

        return $this->acceptLanguage;
    }

    public function getHost(): string
    {
        if (!isset($this->host)) {
            $this->host = $this->mainRequest->getRequest()->getHost();
        }

        return $this->host;
    }

    public function getNormalisedHost(): string
    {
        if (!isset($this->normalisedHost)) {
            $this->normalisedHost = $this->developHostHelper->removeDevelopFromHost(
                $this->getHost(),
            );
        }

        return $this->normalisedHost;
    }

    public function hasUrlParameters(): bool
    {
        return !$this->requestManager->queryBag()->isEmpty();
    }

    public function getTlsVersion(): ?string
    {
        if (!isset($this->tlsVersion)) {
            $this->tlsVersion = $this->requestManager->headersBag()->getString('X-Loadbalancer-TLS-Version');
        }

        return $this->requestPropertyNormalizer->getString($this->tlsVersion);
    }

    public function getReferer(): ?string
    {
        if (!isset($this->referer)) {
            $this->referer = $this->requestManager->headersBag()->getString('referer');
        }

        return $this->requestPropertyNormalizer->getString($this->referer);
    }

    public function getPathInfo(): string
    {
        if (!isset($this->pathInfo)) {
            $this->pathInfo = $this->mainRequest->getRequest()->getPathInfo();
        }

        return $this->pathInfo;
    }

    public function getMethod(): string
    {
        if (!isset($this->method)) {
            $this->method = $this->requestManager->serverBag()->getString('REQUEST_METHOD');
        }

        return $this->method;
    }

    public function getScheme(): string
    {
        if (!isset($this->scheme)) {
            $this->scheme = $this->mainRequest->getRequest()->getScheme();
        }

        return $this->scheme;
    }

    public function getProtocol(): string
    {
        if (!isset($this->protocol)) {
            $this->protocol = $this->requestManager->serverBag()->getString('SERVER_PROTOCOL');
        }

        return $this->protocol;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_URL                     => $this->getUrl(),
            self::KEY_RELATIVE_URL            => $this->getRelativeUrl(),
            self::KEY_ROUTE                   => $this->getRoute(),
            self::KEY_LOCALE                  => $this->getLocale(),
            self::KEY_USER_IP                 => $this->getUserIp(),
            self::KEY_USER_AGENT              => $this->getUserAgent(),
            self::KEY_USER_AGENT_CLIENT_HINTS => $this->getUserAgentClientHints(),
            self::KEY_ACCEPT_LANGUAGE         => $this->getAcceptLanguage(),
            self::KEY_HOST                    => $this->getHost(),
            self::KEY_NORMALISED_HOST         => $this->getNormalisedHost(),
            self::KEY_HAS_URL_PARAMETERS      => $this->hasUrlParameters(),
            self::KEY_TLS_VERSION             => $this->getTlsVersion(),
            self::KEY_REFERER                 => $this->getReferer(),
            self::KEY_PATH_INFO               => $this->getPathInfo(),
            self::KEY_METHOD                  => $this->getMethod(),
            self::KEY_SCHEME                  => $this->getScheme(),
            self::KEY_PROTOCOL                => $this->getProtocol(),
        ];
    }
}
