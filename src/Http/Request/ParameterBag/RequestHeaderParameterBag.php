<?php

declare(strict_types=1);

namespace App\Http\Request\ParameterBag;

class RequestHeaderParameterBag extends RequestParameterBag
{
    protected function getScalar(string $parameter): mixed
    {
        $parameter = $this->normalizeParameter($parameter);
        $values = $this->parameters[$parameter] ?? [];
        $value = $values[0] ?? null;

        return is_scalar($value) ? $value : null;
    }
}
