<?php

declare(strict_types=1);

namespace App\Http\Request\Manager;

use App\Http\Request\FlagBag\RequestFlagBagInterface;
use App\Http\Request\ParameterBag\RequestParameterBagInterface;

interface RequestManagerInterface
{
    public function attributesBag(): RequestParameterBagInterface;

    public function queryBag(): RequestParameterBagInterface;

    public function requestBag(): RequestParameterBagInterface;

    public function cookiesBag(): RequestParameterBagInterface;

    public function headersBag(): RequestParameterBagInterface;

    public function serverBag(): RequestParameterBagInterface;

    public function flagBag(): RequestFlagBagInterface;
}
