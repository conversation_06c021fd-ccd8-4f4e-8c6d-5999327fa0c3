<?php

declare(strict_types=1);

namespace App\Http\Response\EventSubscriber;

use App\Kernel\KernelResponseEvent;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;

class SplitTestHeadersEventSubscriber implements EventSubscriberInterface
{
    private const string HEADER_X_LOG_SPLIT_TEST_VARIANT = 'X-Log-Split_Test_Variant';
    private const string HEADER_X_LOG_SPLIT_TEST_ID      = 'X-Log-Split_Test_Id';

    public function __construct(private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader)
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelResponseEvent::NO_REDIRECT->value => 'setSplitTestHeadersResponse',
        ];
    }

    public function setSplitTestHeadersResponse(ResponseEvent $responseEvent): void
    {
        $response = $responseEvent->getResponse();
        $this->setSplitTestVariantHeader($response);
    }

    private function setSplitTestVariantHeader(Response $response): void
    {
        $id = $this->splitTestExtendedReader->getId();
        $variant = $this->splitTestExtendedReader->getVariant();

        // Make load balancer add split test id to access log
        if ($id !== null) {
            $response->headers->set(self::HEADER_X_LOG_SPLIT_TEST_ID, (string)$id);
        }

        // Make load balancer add split test variant to access log
        if ($variant !== null) {
            $response->headers->set(self::HEADER_X_LOG_SPLIT_TEST_VARIANT, $variant);
        }
    }
}
