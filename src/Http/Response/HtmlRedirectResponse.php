<?php

declare(strict_types=1);

namespace App\Http\Response;

use Symfony\Component\HttpFoundation\Response;

class HtmlRedirectResponse extends Response
{
    /**
     * @param array<string, string> $headers
     */
    public function __construct(string $redirectUrl, int $status = 200, array $headers = [])
    {
        $content = <<<HERE
<!doctype html>
<html>
<head>
    <meta http-equiv="Refresh" content="0; url='{$redirectUrl}'" />
</head>
<body>
<script type="text/javascript" charset="utf-8">
    window.location.replace("{$redirectUrl}");
</script>
<p>If you are not redirected in five seconds, please <a href="{$redirectUrl}">click here</a>.</p>
</body>
</html>
HERE;

        parent::__construct($content, $status, $headers);
    }
}
