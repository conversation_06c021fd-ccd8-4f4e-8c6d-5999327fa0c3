<?php

declare(strict_types=1);

namespace App\Http\Response\HeaderLink;

use Symfony\Component\HttpFoundation\Response;

final class HeaderLinkRegistry
{
    /** @var HeaderLink[] */
    private array $headerLinks = [];

    public function add(HeaderLink $headerLink): self
    {
        $this->headerLinks[] = $headerLink;

        return $this;
    }

    /**
     * Add header links to the response
     *
     * @link https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Link
     */
    public function addToResponse(Response $response): void
    {
        $linkHeaderValue = array_map(
            static fn (HeaderLink $headerLink) => $headerLink->toHeaderValue(),
            $this->headerLinks,
        );
        $linkHeaderValue = array_unique($linkHeaderValue);

        if ($linkHeaderValue !== []) {
            $response->headers->set('Link', implode(', ', $linkHeaderValue));
        }
    }
}
