<?php

declare(strict_types=1);

namespace App\Http\Response;

use App\Http\Response\Event\ResponseCachingStartedEvent;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final class ResponseCachingHelper
{
    private ?int $timeToLiveInSeconds = null;

    public function __construct(
        private readonly EventDispatcherInterface $eventDispatcher,
        private readonly DateTimeFactory $dateTimeFactory
    )
    {
    }

    public function startResponseCaching(int $timeToLiveInSeconds): void
    {
        $this->timeToLiveInSeconds = $timeToLiveInSeconds;

        $startResponseCachingEvent = new ResponseCachingStartedEvent();
        $this->eventDispatcher->dispatch($startResponseCachingEvent, ResponseCachingStartedEvent::NAME);
    }

    public function responseCachingStarted(): bool
    {
        return $this->timeToLiveInSeconds !== null;
    }

    public function getTimeToLiveInSeconds(): ?int
    {
        return $this->timeToLiveInSeconds;
    }

    public function enableResponseCaching(Response $response): void
    {
        if ($this->timeToLiveInSeconds === null) {
            return;
        }

        $expiresAt = $this->dateTimeFactory->createNow(TimezoneEnum::UTC);
        $expiresAt->modify(sprintf('+%d seconds', $this->timeToLiveInSeconds));

        $response->setExpires($expiresAt);
        $response->setMaxAge($this->timeToLiveInSeconds);
        $response->setPublic();

        // Remove all `x-log` headers
        foreach (array_keys($response->headers->all()) as $headerName) {
            if (str_starts_with($headerName, 'x-log')) {
                $response->headers->remove($headerName);
            }
        }
    }
}
