<?php

declare(strict_types=1);

namespace App\Http\Url;

final class SlugHelper
{
    public function create(string $value): ?string
    {
        $slug = (string)transliterator_transliterate('Any-Latin; Latin-ASCII; Lower()', $value);
        $slug = str_replace([' ', '\'s', '+'], ['-', 's', ' plus '], $slug);
        $slug = (string)preg_replace('~[^a-z0-9\-]~', '-', $slug);
        $slug = trim($slug, '-');
        $slug = (string)preg_replace('~-+~', '-', $slug);

        return $slug !== '' ? $slug : null;
    }

    public function maximizeLength(string $slug, int $maxLength): string
    {
        if (mb_strlen($slug) <= $maxLength) {
            return $slug;
        }

        $slugParts = explode('-', $slug);
        $newSlug = array_shift($slugParts);
        $newSlugLength = mb_strlen($newSlug);

        foreach ($slugParts as $slugPart) {
            $slugPart = sprintf('-%s', $slugPart);
            $slugPartLength = mb_strlen($slugPart);

            if ($newSlugLength + $slugPartLength > $maxLength) {
                break;
            }

            $newSlug .= $slugPart;
            $newSlugLength += $slugPartLength;
        }

        return $newSlug;
    }
}
