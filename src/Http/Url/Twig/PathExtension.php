<?php

declare(strict_types=1);

namespace App\Http\Url\Twig;

use App\Http\Url\PersistentUrlParametersHelper;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;
use Twig\Environment;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class PathExtension extends AbstractExtension
{
    public function __construct(
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private readonly PersistentUrlParametersHelper $persistentUrlParametersHelper
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'persistent_path',
                $this->getPersistentPath(...),
                [
                    'is_safe' => ['html'],
                ],
            ),
            new TwigFunction(
                'persistent_new_search_path',
                $this->getPersistentNewSearchPath(...),
                [
                    'is_safe' => ['html'],
                ],
            ),
            new TwigFunction(
                'persistent_related_terms_path',
                $this->getPersistentRelatedTermsPath(...),
                [
                    'is_safe' => ['html'],
                ],
            ),
            new TwigFunction(
                'persistent_path_query_string',
                $this->getPersistentPathQueryString(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'persistent_path_search_bar_input_fields',
                $this->getPersistentPathSearchBarInputFields(...),
                [
                    'is_safe'           => ['html'],
                    'needs_environment' => true,
                ],
            ),
        ];
    }

    /**
     * @param array<string, string|null> $routeParameters
     */
    public function getPersistentPath(string $route, array $routeParameters = []): string
    {
        return $this->persistentUrlParametersRouter->generate($route, $routeParameters);
    }

    /**
     * @param array<string, string|null> $routeParameters
     */
    public function getPersistentNewSearchPath(string $route, array $routeParameters = []): string
    {
        return $this->persistentUrlParametersRouter->generate($route, $routeParameters, PersistentUrlParametersPageType::NEW_SEARCH);
    }

    /**
     * @param array<string, string|null> $routeParameters
     */
    public function getPersistentRelatedTermsPath(string $route, array $routeParameters = []): string
    {
        return $this->persistentUrlParametersRouter->generate($route, $routeParameters, PersistentUrlParametersPageType::RELATED_TERMS);
    }

    /**
     * @param array<string, string> $routeParameters
     */
    public function getPersistentPathQueryString(
        array $routeParameters = [],
        ?string $pageType = null
    ): string
    {
        $pageTypeEnum = PersistentUrlParametersPageType::DEFAULT;

        if ($pageType !== null) {
            $pageTypeEnum = PersistentUrlParametersPageType::from($pageType);
        }

        return http_build_query(
            [
                ...$this->persistentUrlParametersHelper->getPersistentParameters($pageTypeEnum),
                ...$routeParameters,
            ],
        );
    }

    /**
     * @param array<string, string|null> $adjustUrlParameters
     */
    public function getPersistentPathInputFields(
        Environment $twig,
        array $adjustUrlParameters = [],
        PersistentUrlParametersPageType $pageType = PersistentUrlParametersPageType::DEFAULT
    ): string
    {
        $inputFields = [...$this->persistentUrlParametersHelper->getPersistentParameters($pageType), ...$adjustUrlParameters];
        $inputFields = $this->arrayRemoveNullValues($inputFields);

        return $twig->render(
            '@theme/http/_persistent_path_input_fields.html.twig',
            [
                'inputFields' => $inputFields,
            ],
        );
    }

    /**
     * @param array<string, string|null> $adjustUrlParameters
     */
    public function getPersistentPathSearchBarInputFields(Environment $twig, array $adjustUrlParameters = []): string
    {
        return $this->getPersistentPathInputFields($twig, $adjustUrlParameters, PersistentUrlParametersPageType::NEW_SEARCH);
    }

    /**
     * @param array<string, string|null> $adjustUrlParameters
     */
    public function getPersistentPathUrl(string $url, array $adjustUrlParameters = []): string
    {
        $query = parse_url($url, PHP_URL_QUERY);

        if (!is_string($query)) {
            $query = '';
        }

        parse_str($query, $queryParameters);

        $persistentParameters = [
            ...$this->persistentUrlParametersHelper->getPersistentParameters(PersistentUrlParametersPageType::DEFAULT),
            ...$adjustUrlParameters,
        ];

        $persistentParameters = $this->arrayRemoveNullValues($persistentParameters);

        if ($persistentParameters === []) {
            return $url;
        }

        $queryParameters = array_merge(
            $queryParameters,
            $persistentParameters,
        );

        // Faster than rebuilding url
        $pos = mb_strpos($url, '?');

        if ($pos !== false) {
            $url = mb_substr($url, 0, $pos);
        }

        // Check for path and add / for https://www.example.com
        if (parse_url($url, PHP_URL_PATH) === null) {
            $url = sprintf('%s/', $url);
        }

        $query = http_build_query($queryParameters);

        return sprintf('%s?%s', $url, $query);
    }

    /**
     * @param mixed[] $array
     *
     * @return mixed[]
     */
    private function arrayRemoveNullValues(array $array): array
    {
        return array_filter($array, static fn (?string $value) => $value !== null);
    }
}
