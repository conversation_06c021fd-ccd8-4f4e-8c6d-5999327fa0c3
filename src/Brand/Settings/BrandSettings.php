<?php

declare(strict_types=1);

namespace App\Brand\Settings;

class BrandSettings
{
    public const string KEY_NAME         = 'name';
    public const string KEY_SLUG         = 'slug';
    public const string KEY_PARTNER_SLUG = 'partner_slug';

    public function __construct(
        protected string $name,
        protected string $slug,
        protected ?string $partnerSlug
    )
    {
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getPartnerSlug(): ?string
    {
        return $this->partnerSlug;
    }

    /**
     * @return mixed[]
     */
    public function toArray(): array
    {
        return [
            self::KEY_NAME         => $this->getName(),
            self::KEY_SLUG         => $this->getSlug(),
            self::KEY_PARTNER_SLUG => $this->getPartnerSlug(),
        ];
    }
}
