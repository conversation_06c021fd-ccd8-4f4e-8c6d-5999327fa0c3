<?php

declare(strict_types=1);

namespace App\Brand\DebugInfoProvider;

use App\Brand\Settings\BrandSettingsHelper;
use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;

class BrandSettingsDebugInfoProvider implements DebugInfoProviderInterface
{
    private const string KEY_BRAND_SETTINGS = 'brand settings';

    public function __construct(
        private readonly BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        return [
            new DebugInfo(
                self::KEY_BRAND_SETTINGS,
                [
                    $this->brandSettingsHelper->getSettings()->toArray(),
                ],
            ),
        ];
    }

    public static function getDefaultPriority(): int
    {
        return 30;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_BRAND_SETTINGS,
        ];
    }
}
