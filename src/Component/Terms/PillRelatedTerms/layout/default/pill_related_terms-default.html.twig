{{ component_style(['pillRelatedTermsDefault']) }}
{% set component_class = 'pill-related-terms' %}
{% set item_class = component_class ~ '__item' %}

<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <ul class="{{ component_class }}__terms">
        {% for pill_related_term in active_terms %}
            {% set term_text = pill_related_term.term|title %}
            {% set is_last_term = active_terms|last == pill_related_term %}
            <li class="{{ item_class }} {{ item_class }}--selected {% if is_last_term %}{{ item_class }}--last{% endif %}">
                <a
                    href="{{ pill_related_term_path(pill_related_term, false) }}"
                    title="{{ term_text }}"
                    class="{{ component_class }}__link vsi"
                >
                    {{- term_text -}}
                </a>
            </li>
        {% endfor %}
        {% for pill_related_term in inactive_terms %}
            {% set term_text = pill_related_term.term|title %}
            <li class="{{ item_class }}">
                <a href="{{ pill_related_term_path(pill_related_term, true) }}"
                   title="{{ term_text }}"
                   class="{{ component_class }}__link vsi"
                >
                    {{- term_text -}}
                </a>
            </li>
        {% endfor %}
    </ul>
</div>

