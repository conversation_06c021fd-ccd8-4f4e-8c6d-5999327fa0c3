<?php

declare(strict_types=1);

namespace App\Component\Terms\RelatedTerms;

use App\JsonTemplate\View\ViewInterface;

final readonly class RelatedTermsProvider
{
    /**
     * @param \Traversable<RelatedTermsProviderInterface> $relatedTermsProviders
     */
    public function __construct(
        private iterable $relatedTermsProviders,
        private RelatedTermsAmountRegistry $relatedTermsAmountRegistry
    )
    {
    }

    /**
     * @return array<string>
     */
    public function getRelatedTerms(RelatedTermsComponent $component, ViewInterface $view): array
    {
        $relatedTerms = null;

        /** @var RelatedTermsProviderInterface $relatedTermsProvider */
        foreach ($this->relatedTermsProviders as $relatedTermsProvider) {
            $relatedTerms = $relatedTermsProvider->getRelatedTerms($component, $view);

            if ($relatedTerms !== null) {
                break;
            }
        }

        $relatedTerms ??= [];

        // Note: the repeat terms yes/no setting has no effect on the first related terms render
        if ($component->repeatTerms) {
            $sliceOffset = 0;
            $this->relatedTermsAmountRegistry->setAmountIfMax($component->amount);
        } else {
            $sliceOffset = max(0, $this->relatedTermsAmountRegistry->getAmount());
            $this->relatedTermsAmountRegistry->increaseAmount($component->amount);
        }

        return array_slice($relatedTerms, $sliceOffset, $component->amount);
    }

    public function requiresSearchResponse(): bool
    {
        /** @var RelatedTermsProviderInterface $relatedTermsProvider */
        foreach ($this->relatedTermsProviders as $relatedTermsProvider) {
            $requiresSearchResponse = $relatedTermsProvider->requiresSearchResponse();

            if ($requiresSearchResponse !== null) {
                return $requiresSearchResponse;
            }
        }

        return false;
    }
}
