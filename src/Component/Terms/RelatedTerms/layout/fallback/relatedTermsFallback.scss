/** @define related-terms */
.related-terms--fallback {
    .related-terms {
        &__column {
            display: block;
        }

        // stylelint-disable plugin/selector-bem-pattern
        &__columns--2 .related-terms {
            &__column {
                display: inline-block;
                padding-left: 1rem;
                vertical-align: top;
                width: 50%;

                &:first-child {
                    padding-left: 0;
                    padding-right: 1rem;
                }
            }
        }

        // stylelint-enable plugin/selector-bem-pattern

        &__link {
            align-items: center;
            background: #1a73e8;
            border: 0.1rem solid #dddddd;
            border-radius: 0.8rem;
            color: #ffffff;
            display: flex;
            font-size: 2rem;
            line-height: 3.6rem;
            max-width: 100%;
            overflow: hidden;
            padding: 2.1rem 0;
            position: relative;
            text-align: left;
            text-decoration: none;

            // stylelint-disable selector-class-pattern
            // Text decoration underline on link hover shows underline on pseudo selector too.
            &:hover .related-terms__label {
                text-decoration: underline;
            }

            // stylelint-enable selector-class-pattern

            &::before {
                color: #ffffff;
                content: $vsi-chevron-right;
                display: inline-block;
                font-size: 2rem;
                height: 3rem;
                line-height: 3rem;
                margin: 0.5rem 0;
                position: relative;
                text-decoration: none;
            }
        }

        // stylelint-disable no-descending-specificity
        &__label {
            display: block;
            padding: 0 0 0 1.5rem;
        }

        // stylelint-enable no-descending-specificity

        @media #{map-get($media-min, c)} {
            &__item {
                margin: 0 0 1.2rem 0;
            }

            &__link::before {
                left: 0.5rem;
            }
        }

        @media #{map-get($media-max, b)} {
            // stylelint-disable max-nesting-depth
            // stylelint-disable plugin/selector-bem-pattern
            &__columns--2 .related-terms {
                &__column {
                    display: block;
                    padding: 0;
                    width: auto;

                    &:first-child {
                        padding: 0;
                    }
                }
            }

            // stylelint-enable plugin/selector-bem-pattern
            // stylelint-enable max-nesting-depth

            &__item {
                border-bottom: 0;
                margin: 0 0 1.2rem 0;
            }

            &__link {
                display: flex;
                line-height: 2.6rem;
            }

            &__link::before {
                content: $vsi-chevron-right;
                left: 0.5rem;
            }
        }
    }
}
