<?php

declare(strict_types=1);

namespace App\Component\Generic\ContextErrorMessage;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ContextErrorMessageLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/ContextErrorMessage/layout/default/context_error_message-default.html.twig',
        };
    }
}
