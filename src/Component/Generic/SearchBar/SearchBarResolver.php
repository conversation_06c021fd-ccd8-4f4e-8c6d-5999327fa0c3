<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchBar;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

class SearchBarResolver extends AbstractSpaceResolver
{
    public const string KEY_SHOW_SEARCH_QUERY        = 'show_search_query';
    public const string KEY_ALLOW_START_QUERY_SEARCH = 'allow_start_query_search';
    public const string KEY_AUTOFOCUS                = 'autofocus';

    public static function getSupportedComponent(): string
    {
        return SearchBarComponent::class;
    }

    protected function defineOptions(): void
    {
        $booleanOptions = [
            self::KEY_AUTOFOCUS => false,
        ];

        foreach ($booleanOptions as $booleanOption => $defaultValue) {
            $this->defineBooleanOption($booleanOption, $defaultValue);
        }

        $this->optionsResolver->defineLayout(SearchBarLayout::class);

        $this->optionsResolver->define(self::KEY_SHOW_SEARCH_QUERY)
            ->setDefaultValue(true)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        $this->optionsResolver->define(self::KEY_ALLOW_START_QUERY_SEARCH)
            ->setDefaultValue(true)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);
    }

    private function defineBooleanOption(string $key, bool $defaultValue): void
    {
        $this->optionsResolver->define($key)
            ->setDefaultValue($defaultValue)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);
    }
}
