@import "searchBarMixins";

/** @define search-bar */
// stylelint-disable no-duplicate-selectors
// stylelint-disable visymo/sort-properties-alphabetically
.search-bar--default {
    --_field-button_border-radius: 0 var(--field-corner_border-radius, 0.4rem)
        var(--field-corner_border-radius, 0.4rem) 0;
    --_field_border-radius: var(--field-corner_border-radius, 0.4rem);
    --_field-query_border-radius: var(--field-corner_border-radius, 0.4rem) 0 0
        var(--field-corner_border-radius, 0.4rem);
    --field-corner_border-radius: 0;
    // stylelint-disable-next-line prettier/prettier
    --_height: var(--field-input_line-height, 3.8rem);
    --query_border-color: #eceff1;
    --field-button-text_color: #ffffff;
    --field-button_background-color: var(--brand-primary-color);
    --field-button_border-color: var(--brand-primary-color);
    --field-button_width: 5rem;

    @if $language-direction == "rtl" {
        /* stylelint-disable-next-line prettier/prettier */
        --_field-button_border-radius: var(--field-corner_border-radius, 0.4rem) 0 0 var(--field-corner_border-radius, 0.4rem);
        /* stylelint-disable-next-line prettier/prettier */
        --_field-query_border-radius: 0 var(--field-corner_border-radius, 0.4rem) var(--field-corner_border-radius, 0.4rem) 0;
        /* stylelint-disable-next-line prettier/prettier */
        --_field-input_padding: var(--field-input_padding_rtl, var(--field-input_padding, 0 1rem 0 0));
    } @else {
        /* stylelint-disable-next-line prettier/prettier */
        --_field-button_border-radius: 0 var(--field-corner_border-radius, 0.4rem) var(--field-corner_border-radius, 0.4rem) 0;
        /* stylelint-disable-next-line prettier/prettier */
        --_field-query_border-radius: var(--field-corner_border-radius, 0.4rem) 0 0 var(--field-corner_border-radius, 0.4rem);
        /* stylelint-disable-next-line prettier/prettier */
        --_field-input_padding: var(--field-input_padding_ltr, var(--field-input_padding, 0 0 0 1rem));
    }
}

// stylelint-enable visymo/sort-properties-alphabetically

.search-bar--default {
    min-height: 4rem;
    min-width: 100%;
    padding: var(--padding, 0 1.5rem 0 var(--padding-left, 1.5rem));
    position: relative;

    .search-bar {
        &__form {
            margin: var(--margin, 0);
        }

        &__field {
            background: #ffffff;
            border-radius: var(--_field_border-radius);
            box-shadow: var(--field_box-shadow, none);
            display: grid;
            grid-template-columns: 1fr var(--field-button_width);
            position: relative;

            &-button,
            &-query {
                border: var(--field_border, 0.1rem solid #eceff1);
            }

            &-close-fullscreen {
                display: none;
            }

            &-button {
                background-color: transparent;
                border-left: 0;
                border-radius: var(--_field-button_border-radius);
                color: var(--brand-primary-color);
                height: var(--field-input_line-height, 4rem);
                overflow: hidden;
                width: var(--field-button_width);

                /* stylelint-disable selector-pseudo-element-colon-notation */
                &::before {
                    content: "\e901";
                    display: block;
                    font-size: var(--field-button_font-size, 1.7rem);
                    font-weight: var(--field-button-icon_font-weight, 400);
                    height: var(--field-input_line-height, 4rem);
                    line-height: var(--field-input_line-height, 4rem);
                    text-align: center;
                    width: var(--field-button_width);
                }

                &-text {
                    color: #ffffff;
                }
            }

            &-query {
                background-color: transparent;
                border-radius: var(--_field-query_border-radius);
                border-right: 0;
                color: var(--brand-primary-color);
                display: flex;
                height: var(--field-input_line-height, 4rem);
                padding-left: var(--query_padding-left);
                position: relative;
            }

            &-input {
                background: transparent;
                border: 0;
                color: var(--field-input_color, #505058);
                display: block;
                font-size: var(--field-input_font-size, 1.6rem);
                height: var(--_height);
                line-height: var(--_height);
                margin: auto 0;
                outline: 0;
                padding: var(--_field-input_padding);
                width: 100%;
            }

            // stylelint-disable plugin/selector-bem-pattern
            // stylelint-disable selector-class-pattern
            // stylelint-disable max-nesting-depth

            &--auto-suggest {
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;

                .search-bar__auto-suggest {
                    grid-row: 2;
                }

                .search-bar__field {
                    &-query {
                        border-bottom-left-radius: 0;
                    }

                    &-submit {
                        border-bottom-right-radius: 0;
                    }
                }

                .auto-suggest__icon {
                    width: var(--field-button_width);
                }
            }

            // stylelint-enable max-nesting-depth
        }

        &--invisible-query {
            .search-bar-input {
                color: transparent;

                &:focus {
                    color: #505058;
                }
            }

            .clear-input__button-text {
                color: transparent;
            }
        }

        @include search-bar-clear-input;
    }

    &:hover,
    &:focus {
        .search-bar__field-button {
            background-color: var(--field-button_background-color);
            border-color: var(--field-button_background-color);
            color: var(--field-button-text_color, var(--brand-primary-color));
        }
    }

    @include search-bar-full-page;

    // stylelint-enable plugin/selector-bem-pattern
    // stylelint-enable selector-class-pattern

    // Device responsive specific
    @media #{map-get($media-min, d)} {
        @include search-bar-device(desktop);
    }

    @media #{map-get($media-max, c)} {
        @include search-bar-device(tablet);

        // stylelint-disable-next-line selector-class-pattern
        .search-bar__field-button {
            background-color: var(--field-button_background-color);
            border-color: var(--field-button_background-color);
            color: var(--field-button-text_color, var(--brand-primary-color));
        }
    }

    @media #{map-get($media-max, b)} {
        @include search-bar-device(mobile);
    }
}
