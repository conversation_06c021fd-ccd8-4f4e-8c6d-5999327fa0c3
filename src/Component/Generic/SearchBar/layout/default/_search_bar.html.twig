{# @var query string|null #}
{# @var allow_start_query_search bool #}
{# @var autofocus bool #}
{# @var component_space_modifiers string[] #}

{{ component_javascript('searchBarInitializer') }}
{% set component_class = 'search-bar' %}
{% set field_class = component_class ~ '__field' %}
{% set field_button_class = field_class ~ '-button' %}
{% set field_button_background_class = field_button_class ~ '--background' %}
{% set html_field_button_class_value = [
    field_button_class,
    'vsi',
]|filter(value => value != '')|join(' ') %}
{% set component_space_modifiers = component_space_modifiers|pushString('no-default-space') %}

<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <form
        method="get"
        action="{{ path(search_route) }}"
        class="{{ component_class }}__form"
    >
        <div class="{{ field_class }} vsi"
             data-erase-history-label="{{ 'search_field.erase_history_label'|trans|escape }}"
             data-autosuggest-active-class="{{ field_class }}--auto-suggest"
        >
            <button class="{{ field_class }}-close-fullscreen vsi" type="button"></button>
            <label class="{{ field_class }}-query">
                <input type="text" name="q" autocomplete="off"
                       value="{{ query }}"{{ autofocus ? ' data-autofocus="true"' }}
                       class="{{ field_class }}-input"
                       aria-label="{{ 'search_field.search'|trans|escape }}"
                >
                <button type="button" aria-label="Clear input" class="{{ component_class }}__clear-input vsi"></button>
            </label>
            <button type="submit" class="{{ html_field_button_class_value }}">
                <span class="{{ field_button_class }}-text">
                    {{ 'search_field.search'|trans }}
                </span>
            </button>
            {{ auto_suggest|raw }}
        </div>
        {{ persistent_path_search_bar_input_fields() }}
    </form>
</div>
