<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchBar;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

class SearchBarComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly SearchBarLayout $layout,
        public readonly bool $showSearchQuery,
        public readonly bool $allowStartQuerySearch,
        public readonly bool $autofocus,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'search_bar';
    }

    public function getRenderer(): string
    {
        return SearchBarRenderer::class;
    }
}
