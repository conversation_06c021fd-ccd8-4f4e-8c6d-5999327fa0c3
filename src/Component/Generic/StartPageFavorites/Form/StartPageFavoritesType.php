<?php

declare(strict_types=1);

namespace App\Component\Generic\StartPageFavorites\Form;

use App\Component\Generic\StartPageFavorites\StartpageFavoritesHelper;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Url;

final class StartPageFavoritesType extends AbstractType
{
    private const int NAME_MAX_LENGTH = 30;

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add(
                StartpageFavoritesHelper::KEY_NAME,
                TextType::class,
                [
                    'label'       => 'Na<PERSON> van de site',
                    'trim'        => true,
                    'constraints' => [
                        new NotBlank(),
                        new Length(null, null, self::NAME_MAX_LENGTH),
                    ],
                ],
            )
            ->add(
                StartpageFavoritesHelper::KEY_URL,
                UrlType::class,
                [
                    'label'            => 'Adres van de site',
                    'default_protocol' => null,
                    'trim'             => true,
                    'constraints'      => [
                        new NotBlank(),
                        new Url(),
                    ],
                ],
            )
            ->add(
                'submit',
                SubmitType::class,
                [
                    'label' => 'Opslaan',
                ],
            );
    }
}
