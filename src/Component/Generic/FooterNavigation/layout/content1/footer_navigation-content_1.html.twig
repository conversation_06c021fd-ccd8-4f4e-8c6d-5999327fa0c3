{# @var footer_navigation_list_items FooterNavigationListItem[] #}
{# @var component_space_modifiers string[] #}
{# @var layout string #}
{{ component_style(['footerNavigationContent1']) }}
{% set component_class = 'footer-navigation' %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}">
    <div class="{{ component_class }}__container">
        <ul class="{{ component_class }}__list">
            {% for footer_navigation_list_item in footer_navigation_list_items %}
                {% include '@component/Generic/FooterNavigation/layout/default/_footer_navigation-default_list_item.html.twig' with {
                    component_class: component_class,
                    brand_name: brand_name,
                    footer_navigation_list_item: footer_navigation_list_item
                } only %}
            {% endfor %}
        </ul>
    </div>
</div>
