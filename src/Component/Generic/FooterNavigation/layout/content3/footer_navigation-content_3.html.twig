{# @var footer_navigation_list_items FooterNavigationListItem[] #}
{# @var component_space_modifiers string[] #}
{{ component_style(['footerNavigationContent3']) }}
{% set component_class = 'footer-navigation' %}

<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}">
    <div class="{{ component_class }}__container">
        <a href="{{ persistent_new_search_path('route_home') }}" class="{{ component_class }}__brand-link">
            <img src="{{ brand_image_base64(logo) }}" loading="lazy" alt="{{ brand_name }}" class="{{ component_class }}__brand-image">
        </a>
        <div class="{{ component_class }}__copyright">
            <a href="{{ info_pages_url('copyright') }}" target="_blank" rel="nofollow noopener noreferrer" class="{{ component_class }}__link">&copy; {{ current_year() }} {{ brand_name }}. {{ 'footer.all_rights_reserved'|trans }}</a>
        </div>
        <ul class="{{ component_class }}__list">
            {% for footer_navigation_list_item in footer_navigation_list_items %}
                {% include '@component/Generic/FooterNavigation/layout/default/_footer_navigation-default_list_item.html.twig' with {
                    component_class: component_class,
                    brand_name: brand_name,
                    footer_navigation_list_item: footer_navigation_list_item
                } only %}
            {% endfor %}
        </ul>
    </div>
</div>
