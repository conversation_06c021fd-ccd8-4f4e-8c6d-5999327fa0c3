/** @define footer-navigation */
.footer-navigation--borderless {
    color: var(--container__section_color, #666666);
    line-height: 2.6rem;
    padding-bottom: 1rem;
    padding-top: 1rem;
    position: relative;
    text-align: center;
    width: 100%;

    .footer-navigation {
        &__list {
            font-size: 0;
        }

        &__item {
            display: inline-flex;
            font-size: 1.3rem;

            &::before {
                content: "-";
                display: inline-block;
                padding: 0 0.8rem;
            }

            &--copyright {
                display: block;
                margin-top: 1rem;
            }

            &:first-child::before,
            &--copyright::before {
                display: none;
            }
        }

        &__link {
            color: var(--container__section_color, #666666);
            cursor: pointer;
            white-space: nowrap;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}
