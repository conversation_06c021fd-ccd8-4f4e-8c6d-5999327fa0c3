/** @define pagination */
.pagination--modern {
    .pagination {
        &__list {
            height: 4rem;
            margin: 2.5rem 0;
        }

        &__link {
            box-shadow: 0 0.1rem 0.4rem rgba(20, 23, 26, 0.1);
            color: #657786;
            display: block;
            font-size: 1.5rem;
            font-weight: 400;
            height: 4rem;
            line-height: 4rem;
            min-width: 4rem;
            overflow: hidden;
            text-align: center;

            &:hover {
                background-color: #e0e0e0;
            }

            &--chevron {
                background-color: #f5f8fa;
                color: var(--brand-primary-color);
                width: 6rem;
            }
        }

        &__text {
            display: none;
        }

        &__item {
            display: inline-block;
            margin-right: 0.7rem;

            &--active {
                // stylelint-disable selector-class-pattern
                .pagination__link {
                    background-color: var(--brand-primary-color);
                    border-color: #25185c;
                    color: #ffffff;
                }

                // stylelint-enable selector-class-pattern
            }

            &:last-child {
                margin-right: 0;
            }

            &--hidden {
                display: none;
            }
        }

        &__icon {
            font-size: 2.4rem;

            &-prev::before {
                content: $vsi-arrow-left;
            }

            &-next::before {
                content: $vsi-arrow-right;
            }
        }

        @media #{map-get($media-max, b)} {
            &__item {
                &--number {
                    display: none;
                }
            }

            &__link--chevron {
                font-size: 1.4rem;
                padding: 0 1.4rem;
            }
        }
    }
}
