<?php

declare(strict_types=1);

namespace App\Component\Generic\ScrollToTop;

use App\JsonTemplate\Component\AbstractComponent;

final class ScrollToTopComponent extends AbstractComponent
{
    public function __construct(
        public readonly ScrollToTopLayout $layout
    )
    {
    }

    public static function getType(): string
    {
        return 'scroll_to_top';
    }

    public function getRenderer(): string
    {
        return ScrollToTopRenderer::class;
    }
}
