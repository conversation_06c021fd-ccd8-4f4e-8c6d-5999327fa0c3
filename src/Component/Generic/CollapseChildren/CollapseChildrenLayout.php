<?php

declare(strict_types=1);

namespace App\Component\Generic\CollapseChildren;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum CollapseChildrenLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/CollapseChildren/layout/default/collapse_children-default.html.twig'
        };
    }

    public static function getDefault(): LayoutInterface
    {
        return self::DEFAULT;
    }
}
