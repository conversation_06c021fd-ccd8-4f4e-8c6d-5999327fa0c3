<?php

declare(strict_types=1);

namespace App\Component\Generic\CollapseChildren;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\Component\Parent\AbstractComponentParentRenderer;
use App\JsonTemplate\View\ViewInterface;

class CollapseChildrenRenderer extends AbstractComponentParentRenderer
{
    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof CollapseChildrenComponent) {
            throw UnsupportedComponentException::create($component, [CollapseChildrenComponent::class]);
        }

        $childrenOutput = $this->renderComponents($component->children, $view);

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'   => $component->layout->value,
                'children' => $childrenOutput,
            ],
        );
    }

    /**
     * @param ComponentInterface[] $components
     */
    private function renderComponents(array $components, ViewInterface $view): ?string
    {
        $output = $this->jsonTemplateViewRenderer->renderComponents(
            components: $components,
            view      : $view,
        );

        return $output === '' ? null : $output;
    }

    /**
     * @inheritDoc
     */
    protected function getComponents(ComponentInterface $component): array
    {
        if (!$component instanceof CollapseChildrenComponent) {
            throw UnsupportedComponentException::create($component, [CollapseChildrenComponent::class]);
        }

        return $component->getComponents();
    }
}
