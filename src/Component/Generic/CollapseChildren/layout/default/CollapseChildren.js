function CollapseChildren() {
    return {
        _bemBlockClass: 'collapse-children',
        browserSupported: function () {
            return document.querySelector && document.createElement;
        },
        create: function () {
            if (!this.browserSupported()) {
                return null;
            }

            this._attachEvents();

            return this;
        },
        _attachEvents: function () {
            var self = this;

            var linkBemClass = Helper.getBemClassSelector(this._bemBlockClass, 'link');
            var selector = 'a' + linkBemClass;

            Helper.iterateHtmlElements(selector, function (element) {
                Helper.addEvent('click', element, function (event) {
                    self._linkClicked(event, element);
                });
            });
        },
        _linkClicked: function (event, linkElement) {
            Helper.eventPreventDefault(event);

            var collapseChildrenElement = linkElement.closest('div');

            if (collapseChildrenElement === null) {
                return;
            }

            this._expandChildren(collapseChildrenElement);
        },
        _expandChildren: function (collapseChildrenElement) {
            var childrenSelector = Helper.getBemClassSelector(this._bemBlockClass, 'children');
            var linkSelector = Helper.getBemClassSelector(this._bemBlockClass, 'link');
            collapseChildrenElement.querySelector(childrenSelector).style.display = 'block';
            collapseChildrenElement.querySelector(linkSelector).style.display = 'none';
        }
    };
}