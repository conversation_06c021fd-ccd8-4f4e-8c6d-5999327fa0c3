{# @var layout string #}
{# @var children string #}

{{ component_style(['collapseChildrenDefault']) }}
{{ component_javascript('CollapseChildrenEntry') }}

{% set component_class = 'collapse-children' %}

<div class="{{ component_class(component_class, [layout], ['no-default-space']) }}">
    <div class="{{ component_class }}"{{ delayed_container_attributes() }}>
        <div class="{{ component_class }}__children">{{ children|raw }}</div>
        <a class="vsi {{ component_class }}__link" href="#">
            {{ 'collapse_children.read_more'|trans }}
        </a>
    </div>
</div>
