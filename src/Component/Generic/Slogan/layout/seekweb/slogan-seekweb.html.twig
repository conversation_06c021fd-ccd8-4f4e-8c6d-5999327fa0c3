{# @var brand_name string #}
{# @var component_space_modifiers string[] #}
{# @var layout string #}
{# @var translation_id string #}
{# @var about_url_is_external bool #}
{{ component_style('sloganSeekweb') }}
{% set component_class = 'slogan' %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}">
    <div class="{{ component_class }}__text">
        {{ translation_id|trans({"%brand%": brand_name}) }}
    </div>
    <a
        href="{{ about_url }}" class="{{ component_class }}__source"
        {% if about_url_is_external %} target="_blank" rel="nofollow noopener noreferrer"{% endif %}
    >
        {{- 'slogan.source'|trans -}}
    </a>
</div>
