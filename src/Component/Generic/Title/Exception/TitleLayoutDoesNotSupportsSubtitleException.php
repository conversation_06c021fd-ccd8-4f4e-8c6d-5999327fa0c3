<?php

declare(strict_types=1);

namespace App\Component\Generic\Title\Exception;

use App\Component\Generic\Title\TitleLayout;

final class TitleLayoutDoesNotSupportsSubtitleException extends \RuntimeException
{
    public static function create(TitleLayout $layout, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('Title layout "%s" does not support subtitle', $layout->value),
            0,
            $previous,
        );
    }
}
