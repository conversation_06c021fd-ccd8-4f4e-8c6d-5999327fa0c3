{# @var title App\Component\Generic\Title\Text #}
{# @var subtitle App\Component\Generic\Title\Text|null #}
{# @var layout string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['titleDisplay']) }}
{% set component_class = 'title' %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__container">
        <span class="{{ component_class }}__text">{{ title.text }}</span>
    </div>
</div>
