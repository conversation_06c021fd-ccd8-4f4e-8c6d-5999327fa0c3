{# @var title App\Component\Generic\Title\Text #}
{# @var subtitle App\Component\Generic\Title\Text|null #}
{# @var layout string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['titleContent1']) }}
{% set component_class = 'title' %}
{% set title_html_element = title_html_element|default('h1') %}
{% set subtitle_html_element = subtitle_html_element|default('p') %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__container">
        <{{ title_html_element }} class="{{ component_class }}__title">{{ title.text|highlight_with_class(title.highlight, component_class~"__title-highlighted") }}</{{ title_html_element }}>
    {% if subtitle is not null %}
    <{{ subtitle_html_element }} class="{{ component_class }}__subtitle">{{ subtitle.text|highlight_with_class(subtitle.highlight, component_class~"__subtitle-highlighted") }}</{{ subtitle_html_element }}>
{% endif %}
</div>
</div>
