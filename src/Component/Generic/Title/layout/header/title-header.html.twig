{# @var title App\Component\Generic\Title\Text #}
{# @var subtitle App\Component\Generic\Title\Text|null #}
{# @var layout string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['titleHeader']) }}
{% set component_class = 'title' %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__container">
        <h1 class="{{ component_class }}__text">{{ title.text }}</h1>
    </div>
</div>
