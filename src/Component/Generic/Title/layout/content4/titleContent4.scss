/** @define title */
.title--content-4 {
    .title {
        &__title {
            color: var(--container__section_color, #101828);
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 1.8rem;
        }

        &__title-highlighted {
            color: var(--brand-primary-color);
        }
    }

    @media #{map-get($media-max, b)} {
        .title {
            &__title {
                font-size: 1.4rem;
                line-height: 2rem;
            }
        }
    }
}
