<?php

declare(strict_types=1);

namespace App\Component\Generic\InfoPage;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum InfoPageLayout: string implements LayoutInterface
{
    case CONTENT = 'content';
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::CONTENT => '@component/Generic/InfoPage/layout/content/info_page-content.html.twig',
            self::DEFAULT => '@component/Generic/InfoPage/layout/default/info_page-default.html.twig',
        };
    }
}
