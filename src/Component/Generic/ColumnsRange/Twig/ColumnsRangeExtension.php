<?php

declare(strict_types=1);

namespace App\Component\Generic\ColumnsRange\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class ColumnsRangeExtension extends AbstractExtension
{
    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'render_columns_range_style_attribute_values',
                $this->renderColumnsRangeStyleAttributeValues(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    public function renderColumnsRangeStyleAttributeValues(string $columnStart, string $columnEnd): string
    {
        return sprintf('--grid-column: %s / %s;', $columnStart, $columnEnd);
    }
}
