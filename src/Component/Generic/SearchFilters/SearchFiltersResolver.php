<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchFilters;

use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use App\JsonTemplate\Component\SearchFilter\SearchFilter;
use App\JsonTemplate\Component\SearchFilter\SearchFilterOption;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

final class SearchFiltersResolver implements ComponentResolverInterface
{
    public const string KEY_FILTERS = 'filters';

    public function __construct(
        private readonly ComponentOptionsResolverInterface $optionsResolver
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return SearchFiltersComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(SearchFiltersLayout::class);

        $this->optionsResolver->define(self::KEY_FILTERS)
            ->setAllowedType(OptionType::TYPE_ARRAY)
            ->setRequired()
            ->setNestedResolver(
                static function (OptionsResolverInterface $optionsResolver): void {
                    /**
                     * @see SearchFilter
                     */
                    $optionsResolver->define('label')
                        ->setAllowedType(OptionType::TYPE_STRING)
                        ->setRequired();

                    $optionsResolver->define('options')
                        ->setAllowedType(OptionType::TYPE_ARRAY)
                        ->setRequired()
                        ->setNestedResolver(
                            static function (OptionsResolverInterface $optionsResolver): void {
                                /**
                                 * @see SearchFilterOption
                                 */
                                $optionsResolver->define('value')
                                    ->setAllowedType(OptionType::TYPE_STRING)
                                    ->setRequired();

                                $optionsResolver->define('label')
                                    ->setAllowedType(OptionType::TYPE_STRING)
                                    ->setRequired();

                                $optionsResolver->define('url')
                                    ->setAllowedType(OptionType::TYPE_STRING)
                                    ->setRequired();
                            },
                        );
                },
            );

        return $this->optionsResolver->resolve($options);
    }
}
