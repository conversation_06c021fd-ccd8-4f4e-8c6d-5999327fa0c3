<?php

declare(strict_types=1);

namespace App\Component\Generic\CurrentPageMatches;

use App\Component\Generic\AbstractCondition\AbstractConditionComponent;
use App\JsonTemplate\Component\ComponentInterface;

final class CurrentPageMatchesComponent extends AbstractConditionComponent
{
    /**
     * @param ComponentInterface[] $matchingChildren
     * @param ComponentInterface[] $nonMatchingChildren
     */
    public function __construct(
        private readonly int $page,
        array $matchingChildren,
        array $nonMatchingChildren
    )
    {
        parent::__construct($matchingChildren, $nonMatchingChildren);
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public static function getType(): string
    {
        return 'current_page_matches';
    }

    public function getRenderer(): string
    {
        return CurrentPageMatchesRenderer::class;
    }
}
