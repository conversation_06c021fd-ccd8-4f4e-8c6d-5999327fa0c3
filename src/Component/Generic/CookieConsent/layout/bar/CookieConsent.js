function CookieConsent() {
    return {
        _acceptButtonId: 'cookie-consent__accept',
        _baseFooterClass: 'base__footer',
        _baseFooterElement: null,
        _callbacksHandled: false,
        _componentClass: 'cookie-consent',
        _componentContainerElement: null,
        _componentElement: null,
        _footerClass: 'footer',
        _footerElement: null,
        create: function () {
            this._componentElement = document.querySelector('.' + this._componentClass);

            if (this._componentElement === null) {
                this._handleCookieConsentCallbacks();

                return null;
            }

            this._componentContainerElement = this._componentElement
                .querySelector('.' + this._componentClass + '__container');

            this._acceptButtonElement = document.getElementById(this._acceptButtonId);
            this._baseFooterElement = document.querySelector('.' + this._baseFooterClass);
            this._footerElement = document.querySelector('.' + this._footerClass);

            // Check consent for cached pages
            if (this._hasCookieConsent()) {
                this._hideComponent();
            } else {
                this._attachEvents();
            }

            this._handleCookieConsentCallbacks();

            return this;
        },
        _attachEvents: function () {
            var self = this;

            Helper.addEvent('click', this._acceptButtonElement, function () {
                self._hideComponent();
                self._setCookieConsent();
                self._setFooterSpace(0);
                self._handleCookieConsentCallbacks();
            });

            // Footer reposition is only required for bar layout
            if (cookieConsentOptions.layout !== 'bar') {
                return;
            }

            Helper.addEvent('resize', window, function () {
                self._repositionFooter();
            });

            Helper.addEvent('orientationchange', window, function () {
                self._repositionFooter();
            });

            this._repositionFooter();
        },
        _repositionFooter: function () {
            if (this._hasCookieConsent()) {
                return;
            }

            this._setFooterSpace(this._componentContainerElement.offsetHeight);
        },
        _setFooterSpace: function (space) {
            this._footerElement.style.marginBottom = space + 'px';
        },
        _hideComponent: function () {
            ClassList.add(this._componentElement, this._componentClass + '--hidden');
        },
        _hasCookieConsent: function () {
            var consentCookie = CookieHelper.getCookie(cookieConsentOptions.name);

            if (consentCookie === null) {
                return false;
            }

            return consentCookie >= cookieConsentOptions.consent_version;
        },
        _setCookieConsent: function () {
            if (!navigator.cookieEnabled) {
                return;
            }

            document.cookie = cookieConsentOptions.name
                + '=' + cookieConsentOptions.consent_version
                + ';max-age=' + cookieConsentOptions.expires
                + ';domain=' + cookieConsentOptions.domain
                + ';path=/;SameSite=None;Secure';
        },
        _handleCookieConsentCallbacks: function () {
            if (this._callbacksHandled || !this._hasCookieConsent()) {
                return;
            }

            this._callbacksHandled = true;

            // Override push function to apply future callbacks too
            appCcc.push = function (callback) {
                callback();
            };

            var callback;

            while (callback = appCcc.shift()) {
                callback();
            }
        }
    };
}
