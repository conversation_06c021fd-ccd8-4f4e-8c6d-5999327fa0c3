/** @define cookie-consent */
.cookie-consent--bar {
    // stylelint-disable plugin/selector-bem-pattern
    &.cookie-consent {
        &--hidden {
            display: none;
        }
    }

    // stylelint-enable plugin/selector-bem-pattern

    .cookie-consent {
        &__container {
            bottom: 0;
            left: 0;
            position: fixed;
            right: 0;
            z-index: 1000;
        }

        &__container-inner {
            background-color: var(--container__background, #ffffff);
            border-top: 0.1rem solid #eeeeee;
            color: var(--container__section_color, #666666);
            display: block;
            font-size: 1.2rem;
            line-height: 1.4rem;
            padding: 0.9rem 0;
            position: relative;
            text-align: center;
            width: 100%;
        }

        &__content {
            display: inline-block;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
            position: relative;
        }

        &__message,
        &__options {
            display: inline;
        }

        &__accept,
        &__more-info {
            color: inherit;
            display: inline;
            padding: 0;
            text-decoration: underline;
            white-space: nowrap;
        }

        &__accept {
            color: inherit;
            cursor: pointer;
            outline: none;
            text-align: center;

            &::before {
                content: "-";
                display: inline-block;
                padding-left: 0.15rem;
                padding-right: 0.5rem;
                text-decoration: none;
            }
        }

        &__more-info {
            color: inherit;
        }

        &__more-info:hover,
        &__accept:hover {
            text-decoration: underline;
        }
    }
}
