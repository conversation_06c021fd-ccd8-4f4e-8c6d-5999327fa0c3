{# @var startpage_block_helper App\Startpage\Helper\StartpageBlockHelper #}
{# @var block App\Startpage\Model\Block #}
{# @var block_item App\Startpage\Model\BlockItem #}
{# @var block_item_link App\Startpage\Model\BlockItemLink #}
{# @var string component_class #}
<div class="{{ component_class }}-links__block{{ block.color is not null ? ' '~component_class~'-links__block--color-'~block.color }}" id="r{{ block.id }}">
    <div class="{{ component_class }}-links__block-header">
        <span class="{{ component_class }}-links__caret"></span>
        <h2 class="{{ component_class }}-links__block-title">{{ block.title }}</h2>
    </div>
    <ul class="{{ component_class }}-links__block-content">
        {% if block.html %}
            {% if block.favorites %}
                <ul class="{{ component_class }}-favorites"></ul>
                <a href="{{ url('route_startpage_favorites') }}" rel="nofollow">toevoegen / wijzigen</a>
            {% else %}
                {{ block.html|raw }}
            {% endif %}
        {% else %}
            {% for block_item in startpage_block_helper.blockItems(block) %}
                <li class="{{ component_class }}-links__block-item">
                    {% for block_item_link in block_item.links %}
                        {% set classAttr = [component_class~'-links__link'] %}
                        {% if block_item_link.internal %}
                            {% set classAttr = classAttr|merge([component_class~'-links__link--more']) %}
                        {% endif %}

                        <a href="{{ block_item_link.url }}" class="{{ classAttr|join(' ') }}"{{ block_item_link.noFollow ? ' rel="nofollow"' }} target="{{ block_item_link.target }}">
                            {{- block_item_link.title|capitalize -}}
                            {%- for labelType, labelValue in block_item_link.labels -%}
                                <span class="{{ component_class }}-links__link-label {{ component_class }}-links__link-label--{{ labelType }}"> {{ labelValue }}</span>
                            {%- endfor -%}
                        </a>
                        {{- not loop.last ? "<span class=\"#{component_class}-links__link-separator\"> | </span>"|raw -}}
                    {% endfor %}
                </li>
            {% endfor %}
        {% endif %}
    </ul>
</div>
