<?php

declare(strict_types=1);

namespace App\Component\Generic\StartPage;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class StartPageFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return StartPageComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new StartPageComponent(
            layout: StartPageLayout::from($options[LayoutInterface::KEY]),
        );
    }
}
