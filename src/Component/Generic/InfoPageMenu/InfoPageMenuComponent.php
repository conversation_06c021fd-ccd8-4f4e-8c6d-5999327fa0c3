<?php

declare(strict_types=1);

namespace App\Component\Generic\InfoPageMenu;

use App\JsonTemplate\Component\AbstractComponent;

final class InfoPageMenuComponent extends AbstractComponent
{
    public function __construct(
        public readonly InfoPageMenuLayout $layout
    )
    {
    }

    public static function getType(): string
    {
        return 'info_page_menu';
    }

    public function getRenderer(): string
    {
        return InfoPageMenuRenderer::class;
    }
}
