{# @var query string #}
{# @var layout string #}
{# @var navigation_sections array #}
{{ component_style(['infoPageMenuDefault']) }}
{% set component_class = 'info-page-menu' %}

<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    {# @var section \App\InfoPages\Navigation\PageNavigationSection[] #}
    {% for section in navigation_sections %}
        <div class="{{ component_class }}__section">
            <h3 class="{{ component_class }}__section-title">
                {{ section.title }}
            </h3>
            <ul class="{{ component_class }}__list">
                {% for item in section.items %}
                    {% set target_attr = item.target ? ' target="'~item.target~'"' %}
                    <li class="{{ component_class }}__list-item">
                        <a href="{{ item.url }}"{{ target_attr }} class="{{ component_class }}__list-link{{ ' '~component_class~'__list-link--active' }}">
                            {{ item.text|raw }}
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endfor %}
</div>
