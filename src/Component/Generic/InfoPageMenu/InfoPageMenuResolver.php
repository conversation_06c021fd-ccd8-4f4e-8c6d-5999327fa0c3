<?php

declare(strict_types=1);

namespace App\Component\Generic\InfoPageMenu;

use App\JsonTemplate\Component\AbstractComponentResolver;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;

final class InfoPageMenuResolver extends AbstractComponentResolver
{
    public static function getSupportedComponent(): string
    {
        return InfoPageMenuComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(InfoPageMenuLayout::class);

        return $this->optionsResolver->resolve($options);
    }
}
