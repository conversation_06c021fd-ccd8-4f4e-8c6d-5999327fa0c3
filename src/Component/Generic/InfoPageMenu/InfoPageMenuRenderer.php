<?php

declare(strict_types=1);

namespace App\Component\Generic\InfoPageMenu;

use App\InfoPages\Navigation\InfoPagesNavigationDefinition;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

final class InfoPageMenuRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly InfoPagesNavigationDefinition $infoPagesNavigationDefinition
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof InfoPageMenuComponent) {
            throw UnsupportedComponentException::create($component, [InfoPageMenuComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'              => $component->layout->value,
                'navigation_sections' => $this->infoPagesNavigationDefinition->getInfoPagesNavigationSections(),
            ],
        );
    }
}
