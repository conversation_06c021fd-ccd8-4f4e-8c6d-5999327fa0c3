<?php

declare(strict_types=1);

namespace App\Component\Generic\UnexpectedErrorMessage;

use App\JsonTemplate\Component\AbstractComponent;

class UnexpectedErrorMessageComponent extends AbstractComponent
{
    public function __construct(
        public readonly UnexpectedErrorMessageLayout $layout
    )
    {
    }

    public static function getType(): string
    {
        return 'unexpected_error_message';
    }

    public function getRenderer(): string
    {
        return UnexpectedErrorMessageRenderer::class;
    }
}
