<?php

declare(strict_types=1);

namespace App\Component\Generic\Section\Twig;

use App\Component\Generic\Section\SectionCssProperty;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class SectionExtension extends AbstractExtension
{
    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'add_section_class',
                $this->addSectionClass(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'render_section_style_attribute',
                $this->renderSectionStyleAttribute(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'render_section_style_attribute_values',
                $this->renderSectionStyleAttributeValues(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    /**
     * @param string[] $classes
     *
     * @return string[]
     */
    public function addSectionClass(
        ?string $section,
        bool $sectionVisible,
        array $classes
    ): array
    {
        if ($section !== null) {
            $classes[] = 'section';
            $classes[] = sprintf('section--%s', $section);

            if (!$sectionVisible) {
                $classes[] = 'component--hidden';
            }
        }

        return $classes;
    }

    /**
     * @param SectionCssProperty[] $sectionCssProperties
     */
    public function renderSectionStyleAttribute(?string $section, array $sectionCssProperties): string
    {
        $styleProperties = $this->getSectionStyleAttributeValues($section, $sectionCssProperties);

        if ($styleProperties === []) {
            return '';
        }

        return sprintf(' style="%s"', implode(' ', $styleProperties));
    }

    /**
     * @param SectionCssProperty[] $sectionCssProperties
     */
    public function renderSectionStyleAttributeValues(
        ?string $section,
        array $sectionCssProperties,
        bool $addSpacePrefix = false
    ): string
    {
        $styleProperties = $this->getSectionStyleAttributeValues($section, $sectionCssProperties);

        if ($styleProperties === []) {
            return '';
        }

        return sprintf(
            '%s%s',
            $addSpacePrefix ? ' ' : '',
            implode(' ', $styleProperties),
        );
    }

    /**
     * @param SectionCssProperty[] $sectionCssProperties
     *
     * @return string[]
     */
    private function getSectionStyleAttributeValues(
        ?string $section,
        array $sectionCssProperties
    ): array
    {
        if ($section === null) {
            return [];
        }

        // Styling is done in the container layout. We need to "un-style" the CSS properties that are not enabled.
        $styleProperties = [];

        foreach (SectionCssProperty::cases() as $cssProperty) {
            if (!in_array($cssProperty, $sectionCssProperties, true)) {
                $styleProperties[] = sprintf(
                    '--container__section_%s: unset;',
                    $cssProperty->value,
                );
            }
        }

        return $styleProperties;
    }
}
