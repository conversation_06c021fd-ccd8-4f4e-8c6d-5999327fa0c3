<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeader;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\Generic\Logo\LogoStyleFilter;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\Layout\LogoLayoutInterface;

final class SearchHeaderFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return SearchHeaderComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new SearchHeaderComponent(
            layout                    : SearchHeaderLayout::from($options[LayoutInterface::KEY]),
            autofocus                 : $options[SearchHeaderResolver::KEY_AUTOFOCUS],
            logoDarkMode              : $options[LogoLayoutInterface::KEY_LOGO_DARK_MODE],
            logoStyleFilter           : LogoStyleFilter::tryFrom((string)$options[LogoLayoutInterface::KEY_LOGO_STYLE_FILTER]),
            showBackgroundOnDesktop   : $options[SearchHeaderResolver::KEY_SHOW_BACKGROUND_ON_DESKTOP],
            showBackgroundOnMobile    : $options[SearchHeaderResolver::KEY_SHOW_BACKGROUND_ON_MOBILE],
            showBackgroundOnTablet    : $options[SearchHeaderResolver::KEY_SHOW_BACKGROUND_ON_TABLET],
            showHamburgerMenuOnDesktop: $options[SearchHeaderResolver::KEY_SHOW_HAMBURGER_MENU_ON_DESKTOP],
            showHamburgerMenuOnMobile : $options[SearchHeaderResolver::KEY_SHOW_HAMBURGER_MENU_ON_MOBILE],
            showHamburgerMenuOnTablet : $options[SearchHeaderResolver::KEY_SHOW_HAMBURGER_MENU_ON_TABLET],
            showMainMenuMoreOnDesktop : $options[SearchHeaderResolver::KEY_SHOW_MAIN_MENU_MORE_ON_DESKTOP],
            showMainMenuMoreOnMobile  : $options[SearchHeaderResolver::KEY_SHOW_MAIN_MENU_MORE_ON_MOBILE],
            showMainMenuMoreOnTablet  : $options[SearchHeaderResolver::KEY_SHOW_MAIN_MENU_MORE_ON_TABLET],
            showMainMenuOnDesktop     : $options[SearchHeaderResolver::KEY_SHOW_MAIN_MENU_ON_DESKTOP],
            showMainMenuOnMobile      : $options[SearchHeaderResolver::KEY_SHOW_MAIN_MENU_ON_MOBILE],
            showMainMenuOnTablet      : $options[SearchHeaderResolver::KEY_SHOW_MAIN_MENU_ON_TABLET],
            showSubMenuOnDesktop      : $options[SearchHeaderResolver::KEY_SHOW_SUB_MENU_ON_DESKTOP],
            showSubMenuOnMobile       : $options[SearchHeaderResolver::KEY_SHOW_SUB_MENU_ON_MOBILE],
            showSubMenuOnTablet       : $options[SearchHeaderResolver::KEY_SHOW_SUB_MENU_ON_TABLET],
            showSearchQuery           : $options[SearchHeaderResolver::KEY_SHOW_SEARCH_QUERY],
            componentSpaceModifiers   : $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}
