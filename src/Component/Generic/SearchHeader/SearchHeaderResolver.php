<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeader;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\Generic\Logo\LogoStyleFilter;
use App\Generic\Validator\AllowedEnumCaseValidator;
use App\JsonTemplate\Component\Layout\LogoLayoutInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

final class SearchHeaderResolver extends AbstractSpaceResolver
{
    public const string KEY_AUTOFOCUS                      = 'autofocus';
    public const string KEY_SHOW_BACKGROUND_ON_DESKTOP     = 'show_background_on_desktop';
    public const string KEY_SHOW_BACKGROUND_ON_MOBILE      = 'show_background_on_mobile';
    public const string KEY_SHOW_BACKGROUND_ON_TABLET      = 'show_background_on_tablet';
    public const string KEY_SHOW_HAMBURGER_MENU_ON_DESKTOP = 'show_hamburger_menu_on_desktop';
    public const string KEY_SHOW_HAMBURGER_MENU_ON_MOBILE  = 'show_hamburger_menu_on_mobile';
    public const string KEY_SHOW_HAMBURGER_MENU_ON_TABLET  = 'show_hamburger_menu_on_tablet';
    public const string KEY_SHOW_MAIN_MENU_MORE_ON_DESKTOP = 'show_main_menu_more_on_desktop';
    public const string KEY_SHOW_MAIN_MENU_MORE_ON_MOBILE  = 'show_main_menu_more_on_mobile';
    public const string KEY_SHOW_MAIN_MENU_MORE_ON_TABLET  = 'show_main_menu_more_on_tablet';
    public const string KEY_SHOW_MAIN_MENU_ON_DESKTOP      = 'show_main_menu_on_desktop';
    public const string KEY_SHOW_MAIN_MENU_ON_MOBILE       = 'show_main_menu_on_mobile';
    public const string KEY_SHOW_MAIN_MENU_ON_TABLET       = 'show_main_menu_on_tablet';
    public const string KEY_SHOW_SUB_MENU_ON_DESKTOP       = 'show_sub_menu_on_desktop';
    public const string KEY_SHOW_SUB_MENU_ON_MOBILE        = 'show_sub_menu_on_mobile';
    public const string KEY_SHOW_SUB_MENU_ON_TABLET        = 'show_sub_menu_on_tablet';
    public const string KEY_SHOW_SEARCH_QUERY              = 'show_search_query';

    public static function getSupportedComponent(): string
    {
        return SearchHeaderComponent::class;
    }

    protected function defineOptions(): void
    {
        $booleanOptions = [
            LogoLayoutInterface::KEY_LOGO_DARK_MODE  => false,
            self::KEY_AUTOFOCUS                      => false,
            self::KEY_SHOW_BACKGROUND_ON_DESKTOP     => false,
            self::KEY_SHOW_BACKGROUND_ON_MOBILE      => false,
            self::KEY_SHOW_BACKGROUND_ON_TABLET      => false,
            self::KEY_SHOW_HAMBURGER_MENU_ON_DESKTOP => false,
            self::KEY_SHOW_HAMBURGER_MENU_ON_MOBILE  => false,
            self::KEY_SHOW_HAMBURGER_MENU_ON_TABLET  => false,
            self::KEY_SHOW_MAIN_MENU_MORE_ON_DESKTOP => false,
            self::KEY_SHOW_MAIN_MENU_MORE_ON_MOBILE  => false,
            self::KEY_SHOW_MAIN_MENU_MORE_ON_TABLET  => false,
            self::KEY_SHOW_MAIN_MENU_ON_DESKTOP      => false,
            self::KEY_SHOW_MAIN_MENU_ON_MOBILE       => false,
            self::KEY_SHOW_MAIN_MENU_ON_TABLET       => false,
            self::KEY_SHOW_SUB_MENU_ON_DESKTOP       => false,
            self::KEY_SHOW_SUB_MENU_ON_MOBILE        => false,
            self::KEY_SHOW_SUB_MENU_ON_TABLET        => false,
        ];

        foreach ($booleanOptions as $booleanOption => $defaultValue) {
            $this->defineBooleanOption($booleanOption, $defaultValue);
        }

        $this->optionsResolver->define(LogoLayoutInterface::KEY_LOGO_STYLE_FILTER)
            ->setAllowedType(OptionType::TYPE_STRING, true)
            ->setDefaultValue(null)
            ->addValidator(
                new AllowedEnumCaseValidator(LogoStyleFilter::cases()),
            );

        $this->optionsResolver->define(self::KEY_SHOW_SEARCH_QUERY)
            ->setDefaultValue(true)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        $this->optionsResolver->defineLayout(SearchHeaderLayout::class);
    }

    private function defineBooleanOption(string $key, bool $defaultValue): void
    {
        $this->optionsResolver->define($key)
            ->setDefaultValue($defaultValue)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);
    }
}
