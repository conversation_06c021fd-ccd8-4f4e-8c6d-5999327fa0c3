<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeader;

use App\Brand\Settings\BrandSettingsHelper;
use App\Component\Generic\OverlayMenu\OverlayMenuManager;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Menu\Helper\MenuHelper;
use App\Search\Registry\RouteRegistry;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Twig\Environment;

final class SearchHeaderRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly OverlayMenuManager $overlayMenuManager,
        private readonly SearchHeaderSearchBarRenderer $searchHeaderSearchBarRenderer,
        private readonly BrandSettingsHelper $brandSettingsHelper,
        private readonly RouteRegistry $routeRegistry,
        private readonly MenuHelper $menuHelper,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        if (!$component instanceof SearchHeaderComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderComponent::class]);
        }

        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
            ],
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof SearchHeaderComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderComponent::class]);
        }

        $darkModeLogo = $this->splitTestExtendedReader->isVariantActive('wsdm') || $component->logoDarkMode;

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'view'                           => $view,
                'query'                          => $component->showSearchQuery
                    ? $view->getDataRegistry()->getQuery()
                    : null,
                'layout'                         => $component->layout->value,
                'autofocus'                      => $component->autofocus,
                'show_background_on_desktop'     => $component->showBackgroundOnDesktop,
                'show_background_on_mobile'      => $component->showBackgroundOnMobile,
                'show_background_on_tablet'      => $component->showBackgroundOnTablet,
                'show_hamburger_menu_on_desktop' => $component->showHamburgerMenuOnDesktop,
                'show_hamburger_menu_on_mobile'  => $component->showHamburgerMenuOnMobile,
                'show_hamburger_menu_on_tablet'  => $component->showHamburgerMenuOnTablet,
                'show_main_menu_more_on_desktop' => $component->showMainMenuMoreOnDesktop,
                'show_main_menu_more_on_mobile'  => $component->showMainMenuMoreOnMobile,
                'show_main_menu_more_on_tablet'  => $component->showMainMenuMoreOnTablet,
                'show_main_menu_on_desktop'      => $component->showMainMenuOnDesktop,
                'show_main_menu_on_mobile'       => $component->showMainMenuOnMobile,
                'show_main_menu_on_tablet'       => $component->showMainMenuOnTablet,
                'show_sub_menu_on_desktop'       => $component->showSubMenuOnDesktop,
                'show_sub_menu_on_mobile'        => $component->showSubMenuOnMobile,
                'show_sub_menu_on_tablet'        => $component->showSubMenuOnTablet,
                'show_favicon'                   => $component->layout->getShowFavicon(),
                'logo'                           => $component->layout->getLogo($darkModeLogo)?->value,
                'logo_style_filter'              => $component->logoStyleFilter,
                'component_space_modifiers'      => $component->componentSpaceModifiers,
                'overlay_menu'                   => $this->overlayMenuManager->renderOverlayMenu($view),
                'search_bar'                     => $this->searchHeaderSearchBarRenderer->renderSearchBar(
                    $component,
                    $view,
                ),
                'search_route'                   => $this->routeRegistry->getSearchRoute(),
                'brand_name'                     => $this->brandSettingsHelper->getSettings()->getName(),
                'menu_helper'                    => $this->menuHelper,
            ],
        );
    }
}
