@import "../../../../../../resources/shared/assets/scss/BlockSets/overlay";
@import "../default/searchHeaderMixins";

/** @define search-header */
// stylelint-disable plugin/selector-bem-pattern
.search-header--rounded {
    --column-search_width: calc(var(--container__column-one_width) - 2.4rem);
}

.search-header--rounded {
    @if $language-direction == "rtl" {
        padding: var(--padding, 0 var(--container__column-space_width) 1.5rem 1.5rem);
    } @else {
        padding: var(--padding, 0 1.5rem 1.5rem var(--container__column-space_width));
    }
    position: relative;

    // stylelint-disable-next-line selector-class-pattern
    .search-header__menu {
        display: flex;
        flex-wrap: nowrap;
        font-size: 1.3rem;
        height: 3.3rem;
        line-height: 3.4rem;

        // flexbox gap is not supported in all modern browsers

        &-item {
            padding: 0 0.9rem;
        }

        &-item--more {
            display: none;
        }

        &-link {
            color: #777777;
            display: inline-block;
            white-space: nowrap;

            &--active,
            &:hover {
                color: #393e46;
            }

            &--active {
                font-weight: 700;
            }
        }
    }

    .search-header {
        // Rows
        &__row-menu {
            display: grid;
            grid-template-areas: "main-menu sub-menu";
            grid-template-columns: 1fr auto;
            min-height: var(--row-menu_min-height, 2.4rem);
            padding-left: 0.9rem;
            padding-top: 0.2rem;
        }

        &__row-search {
            display: grid;
            grid-template-areas: "search options logo";
            grid-template-columns: var(--column-search_width) auto auto;
            padding-left: 0.9rem;
        }

        // Area
        &__area {
            display: none;
        }

        // Main menu
        &__main-menu {
            grid-area: main-menu;
            padding-left: 1rem;

            // stylelint-disable-next-line selector-class-pattern
            .search-header__menu-item:first-child {
                padding-left: 0;
            }
        }

        // Sub menu
        &__sub-menu {
            grid-area: sub-menu;
            justify-self: end;

            // stylelint-disable-next-line selector-class-pattern
            .search-header__menu-item:last-child {
                padding-right: 0;
            }
        }

        // Options
        &__options {
            align-self: center;
            grid-area: options;
            justify-self: start;
            padding-left: 2rem;
        }

        // Brand logo
        &__logo {
            align-self: center;
            grid-area: logo;
            justify-self: end;
            padding-left: 1rem;

            @media #{map-get($media-max, b)} {
                display: none;
            }
        }

        &__brand-image {
            height: max-content;
            max-width: 100%;
            object-fit: scale-down;
        }
    }

    // stylelint-disable-next-line no-invalid-position-at-import-rule
    @import "../default/searchHeaderMoreMenu";

    .search-bar--default {
        --field-button-highlight_color: var(--brand-primary-color);
        --field-button-icon_font-size: 2.1rem;
        --field-button-icon_font-weight: 400;
        --field-button_background-color: #ffffff;
        --field-button_font-size: var(--field-button-icon_font-size, 1.4rem);
        --field-button_width: 6rem;
        --field-corner_border-radius: 2.2rem;
        --field-input_color: #000000;
        --field-input_font-size: 1.7rem;
        --field-input_line-height: 4.4rem;
        --field-input_padding_ltr: 0 0 0 2.2rem;
        --field-input_padding_rtl: 0 2.2rem 0 0;
        --field_border: 0;
        --field_border-width: 0;
        --field_box-shadow: 0 0 1rem 0.5rem rgba(0, 0, 0, 0.12);
        --padding: 0;
        grid-area: search;
    }

    // stylelint-disable-next-line selector-class-pattern
    .auto-suggest--default {
        --border-color: rgba(0, 0, 0, 0);
        --border-radius: 0.8rem;
        --border-width: 0;
        --box-shadow: 0 1.1rem 1rem 0.5rem rgba(0, 0, 0, 0.12);
        --history-erase_color: #a0a0b0;
        --history-erase_height: 4.4rem;
        --history_color: #2800c8;
        --item-active_color: #000000;
        --item_color: #000000;
        --item_height: 4.4rem;
        --padding: 0 2.2rem;
        --separator_border-color: #c2c1c0;
        --separator_display: block;
    }

    // Search options
    // stylelint-disable-next-line selector-class-pattern
    .search-header__button-menu {
        &-icon {
            background: var(--button-menu-icon_background, #eeeeee);
            border-radius: var(--button-menu-icon_border-radius, 2.2rem);
            box-shadow: var(--button-menu-icon_box-shadow, none);
            cursor: pointer;
            display: block;
            height: 4rem;
            overflow: hidden;
            padding: 1rem;
            text-align: center;
            width: 4rem;

            &:hover {
                background: var(--button-menu-icon-hover_background, #ebebeb);
            }
        }

        &-line {
            background: var(--button-menu-line_background, var(--brand-primary-color));
            height: 0.2rem;
            margin: 0.4rem 0;
        }
    }

    // Device responsive specific
    @media #{map-get($media-min, d)} {
        @include search-header-device(desktop);

        &.search-header--background-desktop {
            background: var(--with-background-desktop_background, #fcfcfc);
            border-bottom: var(--with-background-desktop_border-bottom, 0.1rem solid #eeeeee);
            margin-bottom: 0.4rem;

            .search-bar--default {
                --field_box-shadow: var(--with-background-desktop_box-shadow, none);
            }
        }
    }

    @media #{map-get($media, c)} {
        @include search-header-device(tablet);

        .search-header {
            &__row-menu {
                padding-left: 1.5rem;
            }

            &__row-search {
                grid-template-columns: 1fr auto auto;
                padding-left: 1.5rem;
            }
        }

        &.search-header--background-tablet {
            background: var(--with-background-tablet_background, #fcfcfc);
            border-bottom: var(--with-background-tablet_border-bottom, 0.1rem solid #eeeeee);
            padding-bottom: 1.1rem;

            .search-bar--default {
                --field_box-shadow: var(--with-background-tablet_box-shadow, none);
            }
        }
    }

    @media #{map-get($media-max, b)} {
        @include search-header-device(mobile);

        .search-header {
            &__row-menu {
                padding-left: 1.5rem;
            }

            &__row-search {
                grid-template-areas: "search options";
                grid-template-columns: 1fr auto;
                padding-left: 1.5rem;
            }

            &__menu-item--additional {
                display: none;
            }
        }

        &.search-header--background-mobile {
            background: var(--with-background-mobile_background, #fcfcfc);
            border-bottom: var(--with-background-mobile_border-bottom, 0.1rem solid #eeeeee);
            margin-bottom: 1rem;
            padding-bottom: 1.1rem;

            .search-bar--default {
                --field_box-shadow: var(--with-background-mobile_box-shadow, none);
            }
        }
    }
}
