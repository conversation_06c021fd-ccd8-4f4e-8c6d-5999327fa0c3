{# @var components App\JsonTemplate\Component\ComponentInterface[] #}
{# @var layout string #}
{# @var show_delayed_container_loader bool #}
{% if show_delayed_container_loader %}
    {{- component_style('delayedContainerLoader') -}}
{% endif %}
{% set component_class = 'container' %}
<div class="{{ component_class(component_class, [layout], ['no-default-space']) }}">
    {{ render_components(components, view) }}
</div>
