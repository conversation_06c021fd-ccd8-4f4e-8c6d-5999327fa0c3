<?php

declare(strict_types=1);

namespace App\Component\Image\Image;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\Generic\Validator\AllowedEnumCaseValidator;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

final class ImageResolver extends AbstractSpaceResolver
{
    public const string KEY_IMAGE = 'image';

    public static function getSupportedComponent(): string
    {
        return ImageComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->defineLayout(ImageLayout::class);

        $this->optionsResolver->define(self::KEY_IMAGE)
            ->setAllowedType(OptionType::TYPE_STRING)
            ->setDefaultValue(ImageType::HEADER_DARK_JPG->value)
            ->addValidator(
                new AllowedEnumCaseValidator(ImageType::cases()),
            );
    }
}
