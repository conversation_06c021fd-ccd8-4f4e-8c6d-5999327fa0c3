<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicPagination;

use App\Component\Generic\Pagination\PaginationLayout;
use App\JsonTemplate\Component\AbstractComponent;

final class OrganicPaginationComponent extends AbstractComponent
{
    public function __construct(
        private readonly PaginationLayout $layout
    )
    {
    }

    public function getLayout(): PaginationLayout
    {
        return $this->layout;
    }

    public static function getType(): string
    {
        return 'organic_pagination';
    }

    public function getRenderer(): string
    {
        return OrganicPaginationRenderer::class;
    }
}
