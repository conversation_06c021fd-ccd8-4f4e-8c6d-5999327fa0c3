<?php

declare(strict_types=1);

namespace App\Component\Web\HasOrganicResults;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

final class HasOrganicResultsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return HasOrganicResultsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        $matchingChildren = $options[AbstractSearchApiConditionResolver::KEY_YES];
        $nonMatchingChildren = $options[AbstractSearchApiConditionResolver::KEY_NO];

        return new HasOrganicResultsComponent(
            matchingChildren   : $componentFactory->createMultipleFromOptions($matchingChildren),
            nonMatchingChildren: $componentFactory->createMultipleFromOptions($nonMatchingChildren),
        );
    }
}
