<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicResults;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum OrganicResultsLayout: string implements LayoutInterface
{
    case DARK      = 'dark';
    case DEFAULT   = 'default';
    case DSR       = 'dsr';
    case DSR_DARK  = 'dsr-dark';
    case SEARCHLEY = 'searchley';
    case SEEKWEB   = 'seekweb';
    case VISYMO    = 'visymo';
    case ZAPMETA   = 'zapmeta';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DARK      => '@component/Web/OrganicResults/layout/dark/organic_results-dark.html.twig',
            self::DEFAULT   => '@component/Web/OrganicResults/layout/default/organic_results-default.html.twig',
            self::DSR       => '@component/Web/OrganicResults/layout/dsr/organic_results-dsr.html.twig',
            self::DSR_DARK  => '@component/Web/OrganicResults/layout/dsrDark/organic_results-dsr_dark.html.twig',
            self::SEARCHLEY => '@component/Web/OrganicResults/layout/searchley/organic_results-searchley.html.twig',
            self::SEEKWEB   => '@component/Web/OrganicResults/layout/seekweb/organic_results-seekweb.html.twig',
            self::VISYMO    => '@component/Web/OrganicResults/layout/visymo/organic_results-visymo.html.twig',
            self::ZAPMETA   => '@component/Web/OrganicResults/layout/zapmeta/organic_results-zapmeta.html.twig',
        };
    }
}
