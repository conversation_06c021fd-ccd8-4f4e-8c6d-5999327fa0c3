/** @define organic-results */
.organic-results--dsr {
    .organic-results {
        &__list {
            list-style: none;
        }

        &__item {
            margin: 3.2rem 0;

            &:first-child {
                margin-top: 0;
            }
        }

        &__title {
            color: #101828;
            font-size: 2rem;
            font-weight: 600;
            line-height: 2.5rem;
            margin-bottom: 0.8rem;
            overflow: hidden;
            text-overflow: ellipsis;

            @media #{map-get($media-max, a)} {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
            }
        }

        &__excerpt {
            color: #475467;
            font-size: 1.6rem;
            font-weight: 400;
            line-height: 2.4rem;
            margin-bottom: 0.4rem;
            overflow: hidden;
            text-overflow: ellipsis;

            @media #{map-get($media-max, a)} {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 5;
            }
        }

        &__footer {
            display: flex;
            margin-top: 0.4rem;
        }

        &__link {
            color: var(--brand-primary-color);
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 2.4rem;
            text-transform: capitalize;
        }
    }
}
