<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicResults\StatisticsProvider;

use App\Statistics\Provider\AbstractStatisticsProvider;

class OrganicResultsStatisticsProvider extends AbstractStatisticsProvider
{
    public function __construct(
        OrganicResultsStatisticsResolver $organicResultsStatisticsResolver
    )
    {
        parent::__construct(
            $organicResultsStatisticsResolver,
        );
    }

    public static function getContextKey(): string
    {
        return 'organic_results';
    }

    public static function getPayloadKey(): string
    {
        return 'or';
    }
}
