<?php

declare(strict_types=1);

namespace App\Component\Web\AdvancedSearch;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class AdvancedSearchFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return AdvancedSearchComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new AdvancedSearchComponent(
            layout: AdvancedSearchLayout::from($options[LayoutInterface::KEY]),
        );
    }
}
