/** @define advanced-search */
.advanced-search--default {
    .advanced-search {
        &__field {
            color: #505058;
            line-height: 2rem;
        }

        &__label {
            cursor: pointer;
            font-size: 1.6rem;
        }

        &__description {
            font-size: 1.4rem;
        }

        &__input {
            background: #ffffff;
            border: 0.1rem solid #c2c1c0;
            border-radius: 0.3rem;
            color: #505058;
            display: block;
            font-size: 1.7rem;
            font-weight: normal;
            height: 3.8rem;
            line-height: 2.4rem;
            outline: 0;
            padding: 0.7rem 1rem;
            width: 100%;

            &:focus {
                border: 0.1rem solid #505058;
            }
        }

        &__buttons {
            display: block;
            margin-top: 2.4rem;
            text-align: right;
        }

        &__button {
            background: #ffffff;
            border: 0.1rem solid var(--brand-primary-color);
            border-radius: var(--button_border-radius, 2rem);
            color: var(--brand-primary-color);
            cursor: pointer;
            display: inline-block;
            height: 4rem;
            line-height: 2.8rem;
            min-width: 20rem;
            padding: 0 1rem;
            white-space: nowrap;
        }

        @media #{map-get($media-min, c)} {
            &__fields {
                border-bottom: 0.1rem solid #c2c1c0;
                border-collapse: collapse;
                display: table;
                width: 100%;
            }

            &__field {
                border-top: 0.1rem solid #c2c1c0;
                display: table-row;
            }

            &__cell {
                display: table-cell;
                padding: 1.3rem 0;
                vertical-align: middle;

                &--widget {
                    text-align: right;
                    width: 50%;
                }
            }
        }

        @media #{map-get($media-max, b)} {
            &__cell {
                padding: 1rem 0;

                &:first-child {
                    padding-bottom: 0;
                }
            }
        }
    }
}
