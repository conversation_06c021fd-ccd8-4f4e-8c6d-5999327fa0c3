<?php

declare(strict_types=1);

namespace App\Component\Ads\DynamicAds;

use App\Component\Ads\DynamicAds\Factory\DynamicAdsFactoryInterface;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;

final class DynamicAdsFactory implements ComponentFactoryInterface
{
    /** @var array<string, DynamicAdsFactoryInterface> */
    private array $dynamicAdsFactories = [];

    /**
     * @param iterable<DynamicAdsFactoryInterface> $dynamicAdsFactories
     */
    public function __construct(iterable $dynamicAdsFactories)
    {
        $this->setDynamicAdsFactories($dynamicAdsFactories);
    }

    /**
     * @param iterable<DynamicAdsFactoryInterface> $dynamicAdsFactories
     */
    private function setDynamicAdsFactories(iterable $dynamicAdsFactories): void
    {
        /** @var DynamicAdsFactoryInterface $dynamicAdsFactory */
        foreach ($dynamicAdsFactories as $dynamicAdsFactory) {
            $this->dynamicAdsFactories[$dynamicAdsFactory->getType()] = $dynamicAdsFactory;
        }

        // Sort on priority from high to low
        uasort(
            $this->dynamicAdsFactories,
            static fn (
                DynamicAdsFactoryInterface $a,
                DynamicAdsFactoryInterface $b
            ): int => $b->getPriority() <=> $a->getPriority(),
        );
    }

    public static function getSupportedComponent(): string
    {
        return DynamicAdsComponent::class;
    }

    /**
     * @param mixed[] $options
     */
    public function create(array $options, ComponentFactory $componentFactory): DynamicAdsComponent
    {
        $unit = DynamicAdsUnit::from($options[DynamicAdsResolver::KEY_UNIT]);
        $children = null;

        foreach ($this->dynamicAdsFactories as $dynamicAdsFactory) {
            if (!$dynamicAdsFactory->isEnabled($unit)) {
                continue;
            }

            $children = $dynamicAdsFactory->generateComponents(
                $unit,
                $options,
                $componentFactory,
            );

            break;
        }

        $children ??= [];

        return new DynamicAdsComponent($children);
    }
}
