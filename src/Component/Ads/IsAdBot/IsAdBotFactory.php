<?php

declare(strict_types=1);

namespace App\Component\Ads\IsAdBot;

use App\Component\Generic\AbstractCondition\AbstractConditionResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

final class IsAdBotFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return IsAdBotComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        $matchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractConditionResolver::KEY_YES],
        );
        $nonMatchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractConditionResolver::KEY_NO],
        );

        return new IsAdBotComponent(
            matchingChildren   : $matchingChildren,
            nonMatchingChildren: $nonMatchingChildren,
        );
    }
}
