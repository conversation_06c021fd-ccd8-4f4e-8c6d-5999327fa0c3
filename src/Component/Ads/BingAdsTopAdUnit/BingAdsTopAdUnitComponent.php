<?php

declare(strict_types=1);

namespace App\Component\Ads\BingAdsTopAdUnit;

use App\Component\Ads\BingAdsAdUnit\AbstractBingAdsAdUnitComponent;
use App\Component\Ads\BingAdsAdUnit\BingAdsAdUnitLayout;

class BingAdsTopAdUnitComponent extends AbstractBingAdsAdUnitComponent
{
    public function __construct(
        BingAdsAdUnitLayout $layout,
        int $amount,
        ?int $adStyleId = null
    )
    {
        parent::__construct($layout, self::POSITION_TOP, $amount, $adStyleId);
    }

    public static function getType(): string
    {
        return 'bing_ads_top_ad_unit';
    }
}
