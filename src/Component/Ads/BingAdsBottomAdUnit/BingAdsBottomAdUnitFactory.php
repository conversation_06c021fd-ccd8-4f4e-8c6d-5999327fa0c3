<?php

declare(strict_types=1);

namespace App\Component\Ads\BingAdsBottomAdUnit;

use App\Component\Ads\BingAdsAdUnit\AbstractBingAdsAdUnitResolver;
use App\Component\Ads\BingAdsAdUnit\BingAdsAdUnitLayout;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class BingAdsBottomAdUnitFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return BingAdsBottomAdUnitComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new BingAdsBottomAdUnitComponent(
            layout   : BingAdsAdUnitLayout::from($options[LayoutInterface::KEY]),
            amount   : $options[AbstractBingAdsAdUnitResolver::KEY_AMOUNT],
            adStyleId: $options[AbstractBingAdsAdUnitResolver::KEY_AD_STYLE_ID] ?? null,
        );
    }
}
