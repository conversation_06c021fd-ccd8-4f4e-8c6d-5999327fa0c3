<?php

declare(strict_types=1);

namespace App\Component\News\NewsErrorMessage;

use App\Component\Generic\ContextErrorMessage\ContextErrorMessageComponent;
use App\Component\Generic\ContextErrorMessage\ContextErrorMessageLayout;
use App\Component\Generic\ContextErrorMessage\ContextErrorMessageRenderer;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\News\Response\NewsResponseContext;

class NewsErrorMessageRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly ContextErrorMessageRenderer $contextErrorMessageRenderer
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::SEARCH_RESPONSES,
                ViewDataProperty::NEWS_RESULTS,
            ],
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof NewsErrorMessageComponent) {
            throw UnsupportedComponentException::create($component, [NewsErrorMessageComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $searchResponse = $viewDataRegistry->getSearchResponses();
        /** @var NewsResponseContext|null $newsResults */
        $newsResults = $searchResponse->getSearchResponseContext(ViewDataProperty::NEWS_RESULTS);

        $hasError = $searchResponse->hasFatalError() || (bool)$newsResults?->hasErrors();

        return $this->contextErrorMessageRenderer->render(
            new ContextErrorMessageComponent(
                layout    : ContextErrorMessageLayout::DEFAULT,
                hasResults: (bool)$newsResults?->hasResults(),
                hasError  : $hasError,
            ),
            $view,
        );
    }
}
