<?php

declare(strict_types=1);

namespace App\Component\News\NewsErrorMessage;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

class NewsErrorMessageFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return NewsErrorMessageComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new NewsErrorMessageComponent();
    }
}
