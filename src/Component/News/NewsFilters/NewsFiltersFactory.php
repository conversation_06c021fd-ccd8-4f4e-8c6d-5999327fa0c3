<?php

declare(strict_types=1);

namespace App\Component\News\NewsFilters;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

class NewsFiltersFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return NewsFiltersComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new NewsFiltersComponent();
    }
}
