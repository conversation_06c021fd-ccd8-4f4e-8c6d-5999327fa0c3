<?php

declare(strict_types=1);

namespace App\Component\News\NewsResults;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Option\LinkTypeOption;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

final class NewsResultsRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly SearchApiManager $searchApiManager
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::NEWS_RESULTS,
            ],
        );
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $newsViewDataRequest = $request->news()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::NEWS_RESULTS,
            searchApiViewDataRequest: $newsViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof NewsResultsComponent) {
            throw UnsupportedComponentException::create($component, [NewsResultsComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();
        $newsResultsResponse = $viewDataRegistry->getNewsResults($component);

        if (!$newsResultsResponse->hasResults()) {
            return '';
        }

        $newsResults = $newsResultsResponse->getResults();

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'           => $component->layout->value,
                'results'          => $newsResults,
                'link_type_option' => LinkTypeOption::DEFAULT_TARGET_BLANK,
            ],
        );
    }
}
