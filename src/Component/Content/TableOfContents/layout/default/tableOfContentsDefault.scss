/** @define table-of-contents */
.table-of-contents--default {
    position: sticky;
    top: 10rem;

    .table-of-contents {
        &__content {
            border: 0.1rem solid #cccccc;
            border-radius: 0.3rem;
            color: var(--container__section_color, #000000);
            overflow: hidden;
        }

        &__header {
            padding: 1.5rem;
        }

        &__list {
            line-height: 2.2rem;
            list-style: disc outside;
            margin-left: 1.9rem;
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        &__link {
            color: var(--container__section_color, #000000);
        }

        @media #{map-get($media-min, b)} {
            &__title {
                font-size: 2rem;
                font-weight: 600;
                line-height: 3rem;
            }

            &__link {
                &:hover {
                    text-decoration: underline;
                }
            }
        }

        @media #{map-get($media-max, b)} {
            &__header {
                align-items: baseline;
                display: flex;
                justify-content: space-between;
            }

            &__title {
                font-size: 1.8rem;
                font-weight: 600;
                line-height: 2.4rem;
            }

            &__chevron {
                &::before {
                    content: $vsi-chevron-down;
                }

                &--unfolded::before {
                    content: $vsi-chevron-up;
                }
            }

            &__list {
                display: none;

                &--unfolded {
                    display: block;
                }
            }
        }
    }
}
