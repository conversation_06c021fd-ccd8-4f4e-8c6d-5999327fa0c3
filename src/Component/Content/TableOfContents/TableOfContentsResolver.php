<?php

declare(strict_types=1);

namespace App\Component\Content\TableOfContents;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;

final class TableOfContentsResolver extends AbstractSpaceResolver
{
    public static function getSupportedComponent(): string
    {
        return TableOfContentsComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->defineLayout(TableOfContentsLayout::class);
    }
}
