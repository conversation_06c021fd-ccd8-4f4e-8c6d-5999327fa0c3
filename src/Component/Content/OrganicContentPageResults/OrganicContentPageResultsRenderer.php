<?php

declare(strict_types=1);

namespace App\Component\Content\OrganicContentPageResults;

use App\Component\Content\OrganicContentPageResults\Result\OrganicContentPageResult;
use App\Component\Content\OrganicContentPageResults\Result\OrganicContentPageResultFactory;
use App\Component\Generic\Results\ResultsAmountOptimizer;
use App\Component\Generic\Results\ResultsAmountRegistry;
use App\Component\Generic\Results\ResultsType;
use App\ContentPage\Request\ContentPageRequestInterface;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\Component\Processed\ComponentRendererWithSearchApiDependencyInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Option\KeywordHighlightOption;
use App\Preferences\Option\LinkTypeOption;
use App\SearchApi\SearchApiManager;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Organic\Response\OrganicResponseContext;

class OrganicContentPageResultsRenderer extends AbstractComponentRenderer
    implements ComponentRendererWithSearchApiDependencyInterface
{
    public function __construct(
        private readonly Environment $twig,
        private readonly OrganicContentPageResultFactory $organicContentPageResultFactory,
        private readonly OrganicContentPageResultsSupport $organicContentPageResultsSupport,
        private readonly ContentPageRequestInterface $contentPageRequest,
        private readonly ResultsAmountRegistry $resultsAmountRegistry,
        private readonly SearchApiManager $searchApiManager,
        private readonly ResultsAmountOptimizer $resultsAmountOptimizer
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
                ViewDataProperty::CONTENT_PAGES,
            ],
        );
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof OrganicContentPageResultsComponent) {
            throw UnsupportedComponentException::create($component, [OrganicContentPageResultsComponent::class]);
        }

        if (!$this->organicContentPageResultsSupport->isSupported($component)) {
            return;
        }

        $contentPagesViewDataRequest = $request->contentPages()
            ->enable()
            ->increasePageSize($component->getAmount())
            ->excludeParagraphs();

        if ($component->linkToActiveBrand !== null) {
            $contentPagesViewDataRequest->setMultipleCollectionsSupported($component->linkToActiveBrand === false);
        }

        if ($this->contentPageRequest->getPreviousPublicId() !== null) {
            $contentPagesViewDataRequest->setRelevantPublicId(
                $this->contentPageRequest->getPreviousPublicId(),
            );
        }

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGES,
            searchApiViewDataRequest: $contentPagesViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function handleSearchApiCompleted(
        ComponentInterface $component,
        ViewInterface $view
    ): void
    {
        if (!$component instanceof OrganicContentPageResultsComponent) {
            throw UnsupportedComponentException::create($component, [OrganicContentPageResultsComponent::class]);
        }

        if (!$this->organicContentPageResultsSupport->isSupported($component)) {
            return;
        }

        $this->resultsAmountOptimizer->optimizeAmountOfResults($component);

        $contentPagesResponse = $view->getDataRegistry()->getContentPages($component);

        $this->resultsAmountRegistry->registerResults(
            component  : $component,
            resultsType: ResultsType::ORGANIC,
            resultsKey : spl_object_hash($contentPagesResponse),
            results    : $contentPagesResponse->getResults(),
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof OrganicContentPageResultsComponent) {
            throw UnsupportedComponentException::create($component, [OrganicContentPageResultsComponent::class]);
        }

        if (!$this->organicContentPageResultsSupport->isSupported($component)) {
            return '';
        }

        $viewDataRegistry = $view->getDataRegistry();

        $results = $this->getOrganicContentPageResults($component);

        if ($results === []) {
            return '';
        }

        $maxDescriptionLength = $this->getMaxDescriptionLength($component, $viewDataRegistry);

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'results'                      => $results,
                'query'                        => $viewDataRegistry->getQuery(),
                'keyword_highlight_option'     => $viewDataRegistry->getOrganicKeywordHighlight(KeywordHighlightOption::DEFAULT_FALSE),
                'link_type_option'             => $viewDataRegistry->getOrganicLinkType(LinkTypeOption::DEFAULT_TARGET_BLANK),
                'layout'                       => $component->layout->value,
                'result_description_more_link' => $component->resultDescriptionMoreLink,
                'result_display_url_link'      => $component->resultDisplayUrlLink,
                'result_title_link'            => $component->resultTitleLink,
                'result_image_link'            => $component->resultImageLink,
                'show_result_display_url'      => $component->showResultDisplayUrl,
                'max_description_length'       => $maxDescriptionLength,
                'component_space_modifiers'    => $component->componentSpaceModifiers,
            ],
        );
    }

    /**
     * @return OrganicContentPageResult[]
     */
    private function getOrganicContentPageResults(
        OrganicContentPageResultsComponent $component
    ): array
    {
        $results = $this->resultsAmountRegistry->getResultsByComponent(
            component: $component,
        );

        if ($results === []) {
            return [];
        }

        $organicContentPages = [];

        foreach ($results as $contentPage) {
            $organicContentPage = $this->organicContentPageResultFactory->createFromContentPage(
                $contentPage,
                $component->linkToActiveBrand,
            );

            if ($organicContentPage === null) {
                break;
            }

            $organicContentPages[] = $organicContentPage;
        }

        return $organicContentPages;
    }

    private function getMaxDescriptionLength(
        OrganicContentPageResultsComponent $component,
        ViewDataRegistry $viewDataRegistry
    ): int
    {
        if ($component->maxDescriptionLength !== null) {
            return $component->maxDescriptionLength;
        }

        $organicResponse = $viewDataRegistry
            ->getSearchResponses()
            ->getSearchResponseContext(ViewDataProperty::ORGANIC_RESULTS);

        $organicSource = $organicResponse instanceof OrganicResponseContext
            ? $organicResponse->getSource()
            : null;

        return match ($organicSource) {
            'google_web_search_api_cache_loader',
            'google_web_search_api' => 160,
            default                 => 320
        };
    }
}
