<?php

declare(strict_types=1);

namespace App\Component\Content\OrganicContentPageResults;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;

class OrganicContentPageResultsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return OrganicContentPageResultsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new OrganicContentPageResultsComponent(
            amount                   : $options[OrganicResultsComponentInterface::KEY_AMOUNT],
            resultAmountOptimization : $options[OrganicResultsComponentInterface::KEY_RESULT_AMOUNT_OPTIMIZATION],
            resultDescriptionMoreLink: $options[OrganicContentPageResultsResolver::KEY_RESULT_DESCRIPTION_MORE_LINK],
            resultDisplayUrlLink     : $options[OrganicContentPageResultsResolver::KEY_RESULT_DISPLAY_URL_LINK],
            resultTitleLink          : $options[OrganicContentPageResultsResolver::KEY_RESULT_TITLE_LINK],
            resultImageLink          : $options[OrganicContentPageResultsResolver::KEY_RESULT_IMAGE_LINK],
            showResultDisplayUrl     : $options[OrganicContentPageResultsResolver::KEY_SHOW_RESULT_DISPLAY_URL],
            linkToActiveBrand        : $options[OrganicContentPageResultsResolver::KEY_LINK_TO_ACTIVE_BRAND],
            maxDescriptionLength     : $options[OrganicContentPageResultsResolver::KEY_MAX_DESCRIPTION_LENGTH],
            layout                   : OrganicContentPageResultsLayout::from($options[LayoutInterface::KEY]),
            componentSpaceModifiers  : $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}
