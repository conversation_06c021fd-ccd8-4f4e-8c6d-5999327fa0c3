<?php

declare(strict_types=1);

namespace App\Component\Content\OrganicContentPageResults;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum OrganicContentPageResultsLayout: string implements LayoutInterface
{
    case DEFAULT  = 'default';
    case DARK     = 'dark';
    case DSR      = 'dsr';
    case DSR_DARK = 'dsr-dark';
    case DSR_TT   = 'dsr-tt';
    case TT       = 'tt';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Content/OrganicContentPageResults/layout/default/organic_content_page_results-default.html.twig',
            self::DARK    => '@component/Content/OrganicContentPageResults/layout/dark/organic_content_page_results-dark.html.twig',
            self::DSR     => '@component/Content/OrganicContentPageResults/layout/dsr/organic_content_page_results-dsr.html.twig',
            self::DSR_DARK
                          => '@component/Content/OrganicContentPageResults/layout/dsrDark/organic_content_page_results-dsr_dark.html.twig',
            self::DSR_TT  => '@component/Content/OrganicContentPageResults/layout/dsrTt/organic_content_page_results-dsr_tt.html.twig',
            self::TT      => '@component/Content/OrganicContentPageResults/layout/tt/organic_content_page_results-tt.html.twig',
        };
    }
}
