{# @var results App\Component\Content\OrganicContentPageResults\Result\OrganicContentPageResult[] #}
{# @var query string #}
{# @var keyword_highlight_option string #}
{# @var result_description_more_link bool #}
{# @var result_display_url_link bool #}
{# @var result_title_link bool #}
{# @var show_result_display_url bool #}
{# @var link_type_option string #}
{# @var max_description_length int #}
{# @var component_space_modifiers string[] #}
{% if results %}
    {{ component_style(['organicContentPageResultsTt']) }}

    {% include '@component/Content/OrganicContentPageResults/layout/default/_organic_content_page_results-default_base.html.twig' with {
        results: results,
        query: query,
        keyword_highlight_option: keyword_highlight_option,
        result_description_more_link: result_description_more_link,
        result_display_url_link: result_display_url_link,
        result_title_link: result_title_link,
        result_image_link: result_image_link,
        show_result_display_url: show_result_display_url,
        link_type_option: link_type_option,
        max_description_length: max_description_length,
        component_space_modifiers: component_space_modifiers,
        layout: layout,
        component_class: 'organic-content-page-results'
    } only %}
{% endif %}
