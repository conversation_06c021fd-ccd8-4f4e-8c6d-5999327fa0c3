/** @define organic-content-page-results */
.organic-content-page-results--tt {
    .organic-content-page-results {
        &__list {
            list-style: none;
            margin-bottom: 2.5rem;
            max-width: 60rem;
        }

        &__item {
            margin-bottom: 2.5rem;
        }

        &__favicon {
            display: inline;
            margin-right: 0.4rem;
            vertical-align: text-bottom;
            width: 1.6rem;
        }

        &__title {
            font-size: 1.4rem;
            font-weight: 400;
            line-height: 2.2rem;
            margin-bottom: 0.4rem;
        }

        &__link {
            color: #ffffff;

            &:hover {
                text-decoration: underline;
            }
        }

        &__description {
            color: #999999;
            font-size: 1.4rem;
            line-height: 2rem;
        }

        &__display-url {
            color: #999999;
            font-size: 1.4rem;
            font-style: normal;
            line-height: 2rem;
            margin-bottom: 0.4rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /** Mobile / tablet */
        // stylelint-disable plugin/selector-bem-pattern
        @media #{map-get($media-max, c)} {
            &__list {
                max-width: none;
            }

            &__item {
                margin-bottom: 1.5rem;
            }

            &__title {
                font-size: 1.4rem;
                line-height: 1.6rem;
            }

            &__description {
                color: #999999;
                font-size: 1.3rem;
                line-height: 1.6rem;
                max-height: 4.8rem;
                overflow: hidden;
            }

            &__display-url {
                color: #999999;
                font-size: 1.3rem;
                line-height: 2rem;
            }
        }

        /** Mobile */
        @media #{map-get($media-max, b)} {
            &__link {
                color: #ffffff;
                display: inline-block;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            &__title {
                font-size: 1.4rem;
                line-height: 1.6rem;
            }
        }

        @media #{map-get($media-max, c)} {
            &__list {
                max-width: none;
            }

            &__item {
                margin-bottom: 1.5rem;
            }

            &__title {
                font-size: 1.4rem;
                line-height: 1.6rem;
            }

            &__description {
                color: #999999;
                font-size: 1.3rem;
                line-height: 1.6rem;
                max-height: 4.8rem;
                overflow: hidden;
            }

            &__display-url {
                color: #999999;
                font-size: 1.3rem;
                line-height: 2rem;
            }
        }
    }
}
