/** @define organic-content-page-results */
// stylelint-disable-next-line selector-class-pattern
.organic-content-page-results--dsr-tt {
    margin-top: 2.4rem;

    .organic-content-page-results {
        &__list {
            list-style: none;
        }

        &__item {
            display: flex;
            margin: 3.2rem 0;

            &:first-child {
                margin-top: 0;
            }
        }

        &__item-image-container {
            display: flex;
            max-width: 25rem;
            padding-right: 3rem;
            padding-top: 0.3rem;
        }

        &__item-image {
            object-fit: scale-down;
            object-position: top;
        }

        @media #{map-get($media-max, b)} {
            &__item {
                flex-direction: column;
            }

            &__item-image-container {
                max-width: none;
                padding-right: 0;
                padding-top: 0;
            }

            &__item-image {
                object-fit: cover;
                width: 100%;
            }
        }

        &__header {
            align-items: flex-start;
            align-self: stretch;
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.8rem;
        }

        &__category {
            color: #cecfd2;
            font-size: 1.4rem;
            font-weight: 600;
            line-height: 2rem;
        }

        &__reading-time {
            color: #94969c;
            font-size: 1.2rem;
            font-weight: 400;
            line-height: 1.8rem;
        }

        &__title {
            color: #f5f5f6;
            font-size: 2rem;
            font-weight: 600;
            line-height: 2.5rem;
            margin-bottom: 0.8rem;
            overflow: hidden;
            text-overflow: ellipsis;

            @media #{map-get($media-max, a)} {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
            }
        }

        &__excerpt {
            color: #94969c;
            font-size: 1.6rem;
            font-weight: 400;
            line-height: 2.4rem;
            margin-bottom: 0.4rem;
            overflow: hidden;
            text-overflow: ellipsis;

            @media #{map-get($media-max, a)} {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 5;
            }
        }

        &__footer {
            display: flex;
            margin-top: 0.4rem;
        }

        &__link {
            color: #ffffff;
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 2.4rem;
            text-transform: capitalize;
        }
    }
}
