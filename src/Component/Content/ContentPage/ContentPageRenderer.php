<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPage;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

final class ContentPageRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly SearchApiManager $searchApiManager
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGE,
            ],
        );
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageComponent::class]);
        }

        $contentPageViewDataRequest = $request->contentPage()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE,
            searchApiViewDataRequest: $contentPageViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageComponent::class]);
        }

        $contentPage = $view->getDataRegistry()->getContentPage()->page;

        if ($contentPage === null) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'query'                     => (string)$view->getDataRegistry()->getQuery(),
                'content_page'              => $contentPage,
                'layout'                    => $component->layout->value,
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }
}
