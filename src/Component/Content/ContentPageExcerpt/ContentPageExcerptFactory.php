<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageExcerpt;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class ContentPageExcerptFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ContentPageExcerptComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ContentPageExcerptComponent(
            maxLength              : $options[ContentPageExcerptResolver::KEY_MAX_LENGTH],
            startAfterLength       : $options[ContentPageExcerptResolver::KEY_START_AFTER_LENGTH],
            splitOnLineEnd         : $options[ContentPageExcerptResolver::KEY_SPLIT_ON_LINE_END],
            layout                 : ContentPageExcerptLayout::from($options[LayoutInterface::KEY]),
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}
