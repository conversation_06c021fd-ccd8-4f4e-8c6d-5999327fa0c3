<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageFooter;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class ContentPageFooterComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly ContentPageFooterLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'content_page_footer';
    }

    public function getRenderer(): string
    {
        return ContentPageFooterRenderer::class;
    }
}
