{# @var paragraphs \Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPage\Response\ContentPageParagraph[] #}
{# @var query string #}
{# @var layout string #}
{# @var keyword_highlight_option string #}
{# @var component_space_modifiers string[] #}
{% if paragraphs %}
    {{ component_style('contentPageParagraphsDefault') }}
    {% set component_class = 'content-page-paragraphs' %}
    <div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
        <div class="{{ component_class }}__items">
            {% for paragraph in paragraphs %}
                <div class="{{ component_class }}__item">
                    {% if paragraph.title is not null %}
                        <div class="{{ component_class }}__title">{{ paragraph.title|apply_keyword_highlight(query, keyword_highlight_option) }}</div>
                    {% endif %}
                    <p class="{{ component_class }}__description">{{ paragraph.content|markdown_to_html|apply_keyword_highlight(query, keyword_highlight_option) }}</p>
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}
