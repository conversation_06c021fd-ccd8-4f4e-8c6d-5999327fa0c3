<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageHeader;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ContentPageHeaderLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Content/ContentPageHeader/layout/default/content_page_header-default.html.twig',
        };
    }
}
