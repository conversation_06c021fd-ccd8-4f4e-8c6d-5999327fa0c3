<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageHeader;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class ContentPageHeaderFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ContentPageHeaderComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ContentPageHeaderComponent(
            layout                 : ContentPageHeaderLayout::from($options[LayoutInterface::KEY]),
            showReadTime           : $options[ContentPageHeaderResolver::KEY_SHOW_READ_TIME],
            showPageNumber         : $options[ContentPageHeaderResolver::KEY_SHOW_PAGE_NUMBER],
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}
