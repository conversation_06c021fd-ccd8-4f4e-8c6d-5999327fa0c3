<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageHeader;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

class ContentPageHeaderComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly ContentPageHeaderLayout $layout,
        public readonly bool $showReadTime,
        public readonly bool $showPageNumber,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'content_page_header';
    }

    public function getRenderer(): string
    {
        return ContentPageHeaderRenderer::class;
    }
}
