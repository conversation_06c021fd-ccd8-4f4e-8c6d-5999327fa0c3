<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageCategoryHeader;

use App\ContentPageHome\Request\ContentPageCategoryRequestInterface;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

final class ContentPageCategoryHeaderRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly ContentPageCategoryRequestInterface $contentPageCategoryRequest,
        private readonly SearchApiManager $searchApiManager
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGE_CATEGORY,
            ],
        );
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageCategoryHeaderComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageCategoryHeaderComponent::class]);
        }

        if ($this->contentPageCategoryRequest->getPublicId() !== null) {
            $contentPageCategoryViewDataRequest = $request->contentPageCategory()
                ->enable();

            $this->searchApiManager->registerComponentSearchRequest(
                component               : $component,
                viewDataProperty        : ViewDataProperty::CONTENT_PAGE_CATEGORY,
                searchApiViewDataRequest: $contentPageCategoryViewDataRequest,
                conditions              : $conditions,
            );
        }
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageCategoryHeaderComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageCategoryHeaderComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $category = $viewDataRegistry->getContentPageCategory($component)->category;

        if ($category === null) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                    => $component->layout->value,
                'component_space_modifiers' => $component->componentSpaceModifiers,
                'title'                     => $category->title,
            ],
        );
    }
}
