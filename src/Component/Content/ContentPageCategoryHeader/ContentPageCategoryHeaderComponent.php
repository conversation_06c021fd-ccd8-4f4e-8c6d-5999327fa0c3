<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageCategoryHeader;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class ContentPageCategoryHeaderComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly ContentPageCategoryHeaderLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'content_page_category_header';
    }

    public function getRenderer(): string
    {
        return ContentPageCategoryHeaderRenderer::class;
    }
}
