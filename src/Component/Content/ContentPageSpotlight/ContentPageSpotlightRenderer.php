<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageSpotlight;

use App\Component\Generic\Results\ResultsAmountOptimizer;
use App\Component\Generic\Results\ResultsAmountRegistry;
use App\Component\Generic\Results\ResultsType;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\Component\Processed\ComponentRendererWithSearchApiDependencyInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

class ContentPageSpotlightRenderer extends AbstractComponent<PERSON>ender<PERSON>
    implements ComponentRendererWithSearchApiDependencyInterface
{
    public function __construct(
        private readonly Environment $twig,
        private readonly ResultsAmountRegistry $resultsAmountRegistry,
        private readonly SearchApiManager $searchApiManager,
        private readonly ResultsAmountOptimizer $resultsAmountOptimizer
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGES,
            ],
        );
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageSpotlightComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageSpotlightComponent::class]);
        }

        $contentPagesViewDataRequest = $request->contentPages()
            ->enable()
            ->increasePageSize($component->getAmount());

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGES,
            searchApiViewDataRequest: $contentPagesViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function handleSearchApiCompleted(
        ComponentInterface $component,
        ViewInterface $view
    ): void
    {
        if (!$component instanceof ContentPageSpotlightComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageSpotlightComponent::class]);
        }

        $this->resultsAmountOptimizer->optimizeAmountOfResults($component);

        $contentPagesResponse = $view->getDataRegistry()->getContentPages($component);

        $this->resultsAmountRegistry->registerResults(
            component  : $component,
            resultsType: ResultsType::ORGANIC,
            resultsKey : spl_object_hash($contentPagesResponse),
            results    : $contentPagesResponse->getResults(),
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageSpotlightComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageSpotlightComponent::class]);
        }

        $contentPages = $this->resultsAmountRegistry->getResultsByComponent(
            component: $component,
        );

        if ($contentPages === []) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'title'                     => $component->title,
                'content_pages'             => $contentPages,
                'layout'                    => $component->layout->value,
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }
}
