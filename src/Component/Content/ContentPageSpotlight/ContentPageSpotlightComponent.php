<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageSpotlight;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;

class ContentPageSpotlightComponent extends AbstractSpaceComponent implements OrganicResultsComponentInterface
{
    /**
     * @param array{text: string, highlight: ?string}|null $title
     * @param string[]                                     $componentSpaceModifiers
     */
    public function __construct(
        public readonly ?array $title,
        public int $additionalAmount,
        public readonly ContentPageSpotlightLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public function getAmount(): int
    {
        return 1 + $this->additionalAmount;
    }

    public static function getType(): string
    {
        return 'content_page_spotlight';
    }

    public function getRenderer(): string
    {
        return ContentPageSpotlightRenderer::class;
    }

    public function decreaseAmount(int $amount): int
    {
        $this->additionalAmount = max(0, $this->additionalAmount - $amount);

        return $this->additionalAmount;
    }

    public function resultAmountOptimization(): bool
    {
        return false;
    }
}
