{# @var layout string #}
{# @var align string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['contentPageImageHeader']) }}
{% set component_class = 'content-page-image' %}
{% set component_space_modifiers = component_space_modifiers|pushString('no-default-space') %}
<div class="{{ component_class(component_class, [layout, 'align-' ~ align], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <img src="{{ content_page_image_url(content_page_image, 'c368x207') }}" alt="{{ content_page_image.title }}" class="{{ component_class }}__image"/>
    {% if caption is not null %}
        <span class="{{ component_class }}__caption">{{ caption }}</span>
    {% endif %}
</div>
