{# @var layout string #}
{# @var component_space_modifiers string[] #}
{# @var amount_in_row int #}
{# @var content_pages Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage[] #}
{{ component_style('contentPageResultsList2') }}
{% set component_class = 'content-page-results' %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__items" style="--items_grid-template-columns-amount: {{ amount_in_row }};">
        {% for content_page in content_pages %}
            {% set content_page_link = persistent_article_path(content_page) %}
            <div class="{{ component_class }}__item">
                <a href="{{ content_page_link }}" class="{{ component_class }}__container" title="{{ content_page.title }}">
                    <div class="{{ component_class }}__content-container">
                        <div class="{{ component_class }}__category">{{ get_content_page_category_title(content_page) }}</div>
                        <h2 class="{{ component_class }}__title">{{ content_page.title }}</h2>
                    </div>
                    <img src="{{ content_page_image_url(content_page.image, 'c80x80') }}" alt="{{ content_page.image.title }}" class="{{ component_class }}__image"/>
                </a>
            </div>
        {% endfor %}
    </div>
</div>
