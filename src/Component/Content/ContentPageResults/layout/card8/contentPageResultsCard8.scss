/** @define content-page-results */
.content-page-results--card-8 {
    .content-page-results {
        &__items {
            display: grid;
            gap: 3rem;
            grid-template-columns: repeat(var(--items_grid-template-columns-amount), 1fr);

            @media #{map-get($media, c)} {
                grid-template-columns: repeat(var(--_items_grid-template-columns), 1fr);

                --_items_grid-template-columns: min(var(--items_grid-template-columns-amount), 2);
            }

            @media #{map-get($media-max, b)} {
                grid-template-columns: 1fr;
            }
        }

        &__container {
            column-gap: 1rem;
            display: grid;
            grid-template-columns: 5fr 4fr;

            @media #{map-get($media-max, b)} {
                grid-template-columns: 1fr;
                row-gap: 1.6rem;
            }
        }

        // Image
        &__image {
            border-radius: 1rem;
            width: 100%;
        }

        // Badges
        &__badge-container {
            align-items: center;
            border: 0.1rem solid #d0d5dd;
            border-radius: 1rem;
            color: #344054;
            column-gap: 1rem;
            display: inline-grid;
            font-size: 1.2rem;
            font-weight: 500;
            grid-template-columns: auto 1fr;
            line-height: 1.8rem;
            padding: 0.4rem 0.8rem 0.4rem 0.4rem;
            white-space: nowrap;
        }

        &__category {
            border: 0.1rem solid #d0d5dd;
            border-radius: 0.6rem;
            color: #344054;
            display: inline-block;
            overflow: hidden;
            padding: 0.2rem 0.6rem 0.2rem 0.6rem;
            position: relative;
            text-indent: 1.3rem;
            text-overflow: ellipsis;

            // Dot
            &::before {
                background: var(--brand-primary-color);
                border-radius: 50%;
                box-shadow: 0 0 0 0.2rem var(--brand-primary-color_light);
                content: "";
                display: block;
                height: 0.8rem;
                left: 0.6rem;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 0.8rem;
            }
        }

        // Title
        &__title {
            -webkit-box-orient: vertical;
            color: var(--container__section_color, #101828);
            display: -webkit-box;
            font-size: 1.6rem;
            font-weight: 600;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.4rem;
            margin-top: 1.6rem;
            max-height: 4.8rem;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
        }

        &__description {
            -webkit-box-orient: vertical;
            color: #475467;
            display: -webkit-box;
            font-size: 1.6rem;
            font-weight: 400;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.4rem;
            max-height: 4.8rem;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        &__read {
            color: var(--brand-primary-color);
            display: inline-block;
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 2.4rem;
            margin-top: 0.8rem;
        }
    }
}
