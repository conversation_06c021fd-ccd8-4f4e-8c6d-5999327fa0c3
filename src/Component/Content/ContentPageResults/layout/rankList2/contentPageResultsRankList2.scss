@import "../contentPageResultsMixins";

/** @define content-page-results */
.content-page-results--rank-list-2 {
    .content-page-results {
        &__items {
            column-gap: 3rem;
            display: grid;
            grid-template-columns: repeat(var(--items_grid-template-columns-amount), 1fr);

            @media #{map-get($media-max, c)} {
                grid-template-columns: 1fr;
            }
        }

        &__item {
            border-top: 0.1rem solid var(--brand-primary-color);
            counter-increment: items;
            padding: 1.6rem 0;
        }

        &__container {
            align-items: stretch;
            column-gap: 1rem;
            display: flex;
        }

        // Image
        &__image-container {
            height: 8rem;
            position: relative;
            width: 8rem;

            &::after {
                background: var(--brand-primary-color);
                border-radius: 1.2rem;
                color: #ffffff;
                content: counter(items);
                cursor: pointer;
                display: block;
                font-size: 1.5rem;
                font-weight: 400;
                line-height: 2.4rem;
                min-width: 2.4rem;
                position: absolute;
                right: 0.4rem;
                text-align: center;
                top: 0.4rem;
            }
        }

        &__image {
            display: block;
            height: 8rem;
            width: 8rem;
        }

        // Category and title
        &__content-container {
            display: grid;
            grid-gap: 0.2rem 0.5rem;
            grid-template-areas: "category category" "title icon";
            grid-template-columns: 1fr max-content;
            padding-right: 3rem;
            position: relative;
            width: 100%;

            &::after {
                color: var(--brand-primary-color);
                content: $vsi-arrow-right;
                display: inline-block;
                font-size: 2.8rem;
                font-weight: 400;
                grid-area: icon;
                position: absolute;
                top: 0.5rem;
                transform: rotate(-45deg);
            }
        }

        &__category {
            color: var(--brand-primary-color);
            display: inline-block;
            font-size: 1.4rem;
            font-weight: 700;
            grid-area: category;
            line-height: 2rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        &__title {
            -webkit-box-orient: vertical;
            color: var(--container__section_color, #101828);
            display: -webkit-box;
            font-size: 2rem;
            font-weight: 600;
            grid-area: title;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.5rem;
            max-height: 5rem;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
        }
    }
}

.content-page-results--reset-counter {
    counter-reset: items;
}

@include rtl-rotate-fix(".content-page-results--rank-list-2");
