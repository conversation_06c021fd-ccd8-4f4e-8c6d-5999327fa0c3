<?php

declare(strict_types=1);

namespace App\Locale\Settings;

use App\Locale\Exception\InvalidLocaleSettingsConfigException;
use App\Locale\Model\LocaleFactory;

readonly class LocaleSettingsFactory
{
    public function __construct(
        private LocaleFactory $localeFactory
    )
    {
    }

    /**
     * @param mixed[] $localeConfig
     */
    public function create(array $localeConfig): LocaleSettings
    {
        try {
            return new LocaleSettings(
                $this->localeFactory->create($localeConfig[LocaleSettings::KEY_LOCALE]),
                $localeConfig[LocaleSettings::KEY_IS_DEFAULT],
            );
        } catch (\Throwable $exception) {
            throw InvalidLocaleSettingsConfigException::create($exception);
        }
    }
}
