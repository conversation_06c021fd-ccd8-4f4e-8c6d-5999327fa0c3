<?php

declare(strict_types=1);

namespace App\Locale\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class LocaleRequest implements LocaleRequestInterface
{
    private string $locale;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getLocale(): ?string
    {
        if (!isset($this->locale)) {
            $this->locale = $this->requestManager->queryBag()->getString(self::PARAMETER_LOCALE);

            if (preg_match('~^[a-z]{2}_[A-Z]{2}$~', $this->locale) !== 1) {
                $this->locale = '';
            }
        }

        return $this->requestPropertyNormalizer->getString($this->locale);
    }

    /**
     * @inheritDoc
     */
    public function getUrlParameters(): array
    {
        return $this->toArray();
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_LOCALE => $this->getLocale(),
        ];
    }
}
