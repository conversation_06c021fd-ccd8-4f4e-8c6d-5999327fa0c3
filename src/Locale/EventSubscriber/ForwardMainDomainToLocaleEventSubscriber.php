<?php

declare(strict_types=1);

namespace App\Locale\EventSubscriber;

use App\GeoIp\GeoIp2CountryHelper;
use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Response\DomainRedirectResponseFactory;
use App\WebsiteSettings\Configuration\RedirectDomain\RedirectDomainConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

readonly class ForwardMainDomainToLocaleEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private string $defaultLocale,
        private RequestInfoInterface $requestInfo,
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private GeoIp2CountryHelper $geoIpCountryHelper,
        private DomainRedirectResponseFactory $domainRedirectResponseFactory
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        /**
         * Priority needs to be higher than InitLocaleEventSubscriber, to prevent processing there first.
         * {@see \App\WebsiteSettings\EventSubscriber\InitLocaleEventSubscriber::getSubscribedEvents()}
         */
        return [
            KernelEvents::REQUEST => [
                ['forwardToLocaleDomain', 50],
            ],
        ];
    }

    public function forwardToLocaleDomain(RequestEvent $event): void
    {
        $host = $this->requestInfo->getNormalisedHost();
        $websiteConfiguration = $this->websiteConfigurationHelper->getConfiguration();
        $isRedirectDomain = $websiteConfiguration->hasRedirectDomainConfig($host);

        if (!$isRedirectDomain) {
            return;
        }

        $redirectDomainConfig = $websiteConfiguration->getRedirectDomainConfig($host);

        if ($redirectDomainConfig->getStrategy() !== RedirectDomainConfiguration::REDIRECT_STRATEGY_GEO_IP) {
            throw new \RuntimeException(sprintf('Unsupported redirect strategy "%s"', $redirectDomainConfig->getStrategy()));
        }

        // Try to forward to domain matching with visitor's locale
        $visitorCountryCode = $this->geoIpCountryHelper->getVisitorCountryCode();

        if ($visitorCountryCode !== null) {
            $visitorCountryCodeDomain = sprintf(
                '%s.%s',
                strtolower($visitorCountryCode),
                $this->trimWwwDomain($host),
            );

            // validate locale domain
            if ($websiteConfiguration->hasDomainConfig($visitorCountryCodeDomain)) {
                $this->forwardToDomain($event, $visitorCountryCodeDomain);

                return;
            }
        }

        // There is no domain that matches visitor's locale
        // Fallback to forwarding to domain which supports default locale
        $this->forwardToDomain(
            $event,
            $websiteConfiguration->getDomainForDefaultLocale($this->defaultLocale),
        );
    }

    private function forwardToDomain(RequestEvent $event, string $host): void
    {
        $event->setResponse(
            $this->domainRedirectResponseFactory->createForCurrentRequest($host),
        );
    }

    private function trimWwwDomain(string $host): string
    {
        if (strncasecmp($host, 'www.', 4) === 0) {
            return substr($host, 4);
        }

        return $host;
    }
}
