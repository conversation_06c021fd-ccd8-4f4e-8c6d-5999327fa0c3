<?php

declare(strict_types=1);

namespace App\BrandAssets\File\Exception;

class BrandAssetConfigurationFileNotFoundException extends \RuntimeException
{
    public static function create(string $brand, ?string $fileType = null, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf(
                'Could not find%s brand asset configuration file for brand "%s"',
                $fileType !== null ? sprintf(' %s', $fileType) : '',
                $brand,
            ),
            0,
            $previous,
        );
    }
}
