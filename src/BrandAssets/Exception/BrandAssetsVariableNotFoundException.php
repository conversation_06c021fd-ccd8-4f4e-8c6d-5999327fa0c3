<?php

declare(strict_types=1);

namespace App\BrandAssets\Exception;

final class BrandAssetsVariableNotFoundException extends BrandAssetsException
{
    public static function create(string $variableName, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('Could not find "%s" variable in brand assets config', $variableName),
            0,
            $previous,
        );
    }
}
