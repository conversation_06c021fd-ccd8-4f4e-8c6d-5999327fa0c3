<?php

declare(strict_types=1);

namespace App\BrandAssets\Twig;

use App\BrandAssets\File\BrandAssetsImageFileName;
use App\BrandAssets\Settings\BrandAssetsSettings;
use App\Generic\Generator\HexColorGenerator;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class BrandAssetsExtension extends AbstractExtension
{
    public function __construct(
        private readonly BrandAssetsSettings $brandAssetsSettings,
        private readonly HexColorGenerator $hexColorGenerator
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'brand_color',
                $this->brandAssetsSettings->getBrandPrimaryColor(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'brand_asset_style',
                $this->getBrandAssetStyleVariables(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'brand_image_base64',
                $this->imageBase64(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    private function getBrandAssetStyleVariables(): string
    {
        $primaryColor = $this->brandAssetsSettings->getBrandPrimaryColor();
        $primaryColorDark = $this->hexColorGenerator->darken($primaryColor, 5);
        $primaryColorLight = $this->hexColorGenerator->lighten($primaryColor, 20);

        return <<<HTML
<style>
:root {
    --brand-primary-color: {$primaryColor};
    --brand-primary-color_dark: {$primaryColorDark};
    --brand-primary-color_light: {$primaryColorLight};
}
</style>
HTML;
    }

    private function imageBase64(string $imageName): string
    {
        return $this->brandAssetsSettings->getImage(BrandAssetsImageFileName::from($imageName));
    }
}
