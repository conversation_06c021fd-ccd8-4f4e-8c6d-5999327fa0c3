<?php

declare(strict_types=1);

namespace App\Startpage\News\Twig;

use App\Startpage\News\Helper\ImageUrlSerializer;
use App\Startpage\News\Helper\NewsHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class NewsExtension extends AbstractExtension
{
    public function __construct(
        private readonly ImageUrlSerializer $imageUrlSerializer,
        private readonly NewsHelper $newsHelper
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('news_image_url_serializer', $this->imageUrlSerializer->serialize(...), ['is_safe' => ['html']]),
            new TwigFunction('news', $this->newsHelper->getNews(...), ['is_safe' => ['html']]),
        ];
    }
}
