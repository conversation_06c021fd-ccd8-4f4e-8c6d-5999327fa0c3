<?php

declare(strict_types=1);

namespace App\Startpage\News\Repository;

use App\Startpage\News\Exception\NewsRubricException;
use App\Startpage\News\Model\Rubric;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Log\LoggerInterface;

class RubricCacheRepository implements RubricRepositoryInterface
{
    private const string RUBRIC_CACHE_KEY_TEMPLATE = 'vinden.news.rubrics.%s';

    public function __construct(
        private readonly RubricRepositoryInterface $rubricRepository,
        private readonly CacheItemPoolInterface $cacheAdapter,
        private readonly LoggerInterface $logger
    )
    {
    }

    public function findByName(string $name): ?Rubric
    {
        $rubric = null;

        try {
            $rubric = $this->rubricRepository->findByName($name);
        } catch (\Throwable $previousException) {
            // Wrap all exceptions in NewsRubricException
            if (!$previousException instanceof NewsRubricException) {
                $exception = NewsRubricException::create(
                    message : $previousException->getMessage(),
                    previous: $previousException,
                );
            } else {
                $exception = $previousException;
            }

            $this->logger->notice(
                sprintf('Caught %s while loading rubric {rubric}', $exception::class),
                [
                    'rubric'    => $name,
                    'message'   => $exception->getMessage(),
                    'exception' => $exception,
                ],
            );
        }

        $item = $this->cacheAdapter->getItem(sprintf(self::RUBRIC_CACHE_KEY_TEMPLATE, $name));

        if ($rubric !== null) {
            $item->set($rubric)->expiresAfter(null);

            $this->cacheAdapter->save($item);
        }

        return $item->get();
    }
}
