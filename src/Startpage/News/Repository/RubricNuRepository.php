<?php

declare(strict_types=1);

namespace App\Startpage\News\Repository;

use App\Startpage\News\Exception\NewsRubricException;
use App\Startpage\News\Model\NewsItem;
use App\Startpage\News\Model\Rubric;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestFactoryInterface;

class RubricNuRepository implements RubricRepositoryInterface
{
    private const string METHOD_GET = 'GET';

    private const int HTTP_OK = 200;

    private const array RUBRIC_NAME_TO_FEED = [
        Rubric::GENERAL_NAME       => 'https://www.nu.nl/rss/Algemeen',
        Rubric::NATIONAL_NAME      => 'https://www.nu.nl/rss/Binnenland',
        Rubric::INTERNATIONAL_NAME => 'https://www.nu.nl/rss/Buitenland',
        Rubric::SPORT_NAME         => 'https://www.nu.nl/rss/Sport',
        Rubric::ECONOMICS_NAME     => 'https://www.nu.nl/rss/Economie',
        Rubric::TECH_NAME          => 'https://www.nu.nl/rss/Tech',
        Rubric::ENTERTAINMENT_NAME => 'https://www.nu.nl/rss/Entertainment',
    ];

    private const string NEWS_ITEM_ERROR_MESSAGE_TEMPLATE   = 'News item does not contain a valid %s in the Nu.nl feed';
    private const string RUBRIC_ITEM_ERROR_MESSAGE_TEMPLATE = 'Rubric item does not contain a valid %s in the Nu.nl feed';

    public function __construct(
        private readonly RequestFactoryInterface $requestFactory,
        private readonly ClientInterface $httpClient
    )
    {
    }

    public function findByName(string $name): ?Rubric
    {
        if (!array_key_exists($name, self::RUBRIC_NAME_TO_FEED)) {
            throw NewsRubricException::create(
                sprintf('No Nu.nl RSS feed defined for rubric %s', $name),
                [
                    'rubric_name' => $name,
                ],
            );
        }

        $feedUrl = self::RUBRIC_NAME_TO_FEED[$name];
        $request = $this->requestFactory->createRequest(self::METHOD_GET, $feedUrl);

        $response = $this->httpClient->sendRequest($request);
        $httpStatusCode = $response->getStatusCode();

        if ($httpStatusCode !== self::HTTP_OK) {
            throw NewsRubricException::create(
                sprintf('Received HTTP status code %u from Nu.nl RSS feed', $httpStatusCode),
                [
                    'http_status_code' => $httpStatusCode,
                    'rubric_name'      => $name,
                    'feed_url'         => $feedUrl,
                ],
            );
        }

        $xmlContent = null;

        try {
            $xmlContent = $response->getBody()->getContents();
            $xmlElement = new \SimpleXMLElement($xmlContent);
        } catch (\Throwable $exception) {
            throw NewsRubricException::create(
                'Invalid XML content found in Nu.nl RSS feed',
                [
                    'rubric_name' => $name,
                    'feed_url'    => $feedUrl,
                    'xml_content' => $xmlContent,
                ],
                $exception,
            );
        }

        if (!isset($xmlElement->channel)) {
            throw NewsRubricException::create(
                'Channel XML element not found in Nu.nl RSS feed',
                [
                    'rubric_name' => $name,
                    'feed_url'    => $feedUrl,
                    'xml_content' => $xmlContent,
                ],
            );
        }

        return $this->hydrateRubric($xmlElement->channel, $name);
    }

    public function hydrateRubric(\SimpleXMLElement $rubricElement, string $name): Rubric
    {
        $newsItems = [];

        foreach ($rubricElement->item as $newsItemElement) {
            $newsItem = $this->hydrateNewsItem($newsItemElement);
            $newsItems[$newsItem->getPublicationDateTime()->format('YmdHis')] = $newsItem;
        }

        krsort($newsItems);

        return new Rubric(
            $name,
            $this->getStringValue($rubricElement, 'link', self::RUBRIC_ITEM_ERROR_MESSAGE_TEMPLATE),
            array_values($newsItems),
        );
    }

    public function hydrateNewsItem(\SimpleXMLElement $newsItemElement): NewsItem
    {
        return new NewsItem(
            $this->getStringValue($newsItemElement, 'title', self::NEWS_ITEM_ERROR_MESSAGE_TEMPLATE),
            $this->getStringValue($newsItemElement, 'link', self::NEWS_ITEM_ERROR_MESSAGE_TEMPLATE),
            $this->getImageUrl($newsItemElement),
            $this->getPubDate($newsItemElement),
        );
    }

    private function getStringValue(\SimpleXMLElement $newsItemElement, string $key, string $messageTemplate): string
    {
        if (!property_exists($newsItemElement, $key)) {
            throw new \RuntimeException(
                sprintf($messageTemplate, $key),
            );
        }

        $value = (string)$newsItemElement->{$key}; // @phpstan-ignore-line

        if ($value === '') {
            throw new \RuntimeException(
                sprintf($messageTemplate, $key),
            );
        }

        return $value;
    }

    private function getImageUrl(\SimpleXMLElement $newsItemElement): string
    {
        $enclosureAttributes = $newsItemElement->enclosure->attributes();

        if ($enclosureAttributes === null) {
            throw new \RuntimeException('Enclosure attributes not set for news item');
        }

        $imageUrl = (string)$enclosureAttributes->url;

        if ($imageUrl === '') {
            throw new \RuntimeException('Url not set in enclosure attribute for news item');
        }

        return $imageUrl;
    }

    private function getPubDate(\SimpleXMLElement $newsItemElement): \DateTimeInterface
    {
        $pubDate = \DateTimeImmutable::createFromFormat(
            \DateTimeImmutable::RSS,
            (string)$newsItemElement->pubDate,
            new \DateTimeZone('Europe/Amsterdam'),
        );

        if ($pubDate === false) {
            throw new \RuntimeException('Invalid pub date time format');
        }

        return $pubDate;
    }
}
