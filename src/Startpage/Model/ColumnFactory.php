<?php

declare(strict_types=1);

namespace App\Startpage\Model;

readonly class ColumnFactory
{
    public function __construct(private BlockFactory $blockFactory)
    {
    }

    /**
     * @param mixed[] $data
     */
    public function createFromArray(array $data): Column
    {
        $blocks = [];

        foreach ($data[Column::KEY_BLOCK] ?? [] as $blockData) {
            $block = $this->blockFactory->createFromArray($blockData);

            if ($block->allowDisplay()) {
                $blocks[] = $block;
            }
        }

        return new Column(
            (int)$data[Column::KEY_ID],
            $blocks,
        );
    }
}
