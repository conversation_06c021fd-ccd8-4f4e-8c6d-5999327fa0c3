<?php

declare(strict_types=1);

namespace App\Startpage\Model;

use App\Startpage\Helper\StartpageConfigDataPropertyHelper;

readonly class BlockFactory
{
    public function __construct(
        private BlockContentFactory $blockContentFactory,
        private StartpageConfigDataPropertyHelper $dataPropertyHelper
    )
    {
    }

    /**
     * @param mixed[] $data
     */
    public function createFromArray(array $data): Block
    {
        $html = null;
        $isHtml = $this->dataPropertyHelper->getBoolValue($data[Block::KEY_IS_HTML] ?? false);

        if ($isHtml) {
            $html = urldecode($data[Block::KEY_HTML] ?? '');
            $html = str_replace('##EURO##', '&euro;', $html);
        }

        return new Block(
            (int)$data[Block::KEY_ID],
            (string)$data[Block::KEY_TITLE],
            $this->dataPropertyHelper->getBoolValue($data[Block::KEY_USER_BLOCK]),
            $this->dataPropertyHelper->getBoolValue($data[Block::KEY_VISIBLE]),
            $this->dataPropertyHelper->getNullableStringValue($data[Block::KEY_COLOR]),
            $this->dataPropertyHelper->getBoolValue($data[Block::KEY_ADULT]),
            $this->dataPropertyHelper->getBoolValue($data[Block::KEY_NO_MOBILE]),
            $html,
            $this->createBlockContents($data),
        );
    }

    /**
     * @param mixed[] $data
     *
     * @return BlockContent[]
     */
    private function createBlockContents(array $data): array
    {
        $blockContents = [];

        foreach ($data[Block::KEY_BLOCK_CONTENT] ?? [] as $blockContentData) {
            $blockContent = $this->blockContentFactory->createFromArray($blockContentData);

            if ($blockContent->allowDisplay()) {
                $blockContents[] = $blockContent;
            }
        }

        return $blockContents;
    }
}
