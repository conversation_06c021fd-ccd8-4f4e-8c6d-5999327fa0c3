<?php

declare(strict_types=1);

namespace App\BrandOverride;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\DependencyInjection\DependencyInjectionModules;
use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class BrandOverrideModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'brand_override';
    }

    public function buildConfig(NodeBuilder $rootNodeChildren): void
    {
        // @formatter:off
        $moduleNodeChildren = $rootNodeChildren
            ->arrayNode(self::getModuleName())
                ->arrayPrototype()
                ->children();

        foreach (DependencyInjectionModules::getInstance()->overrideModules as $module) {
            $overrideModuleClass = $module::class;
            $overrideModule = new $overrideModuleClass();
            $overrideModule->buildConfig($moduleNodeChildren);
        }
        // @formatter:on
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $moduleConfig = $this->getModuleConfig($config);

        $container->setParameter('brand_website.brand_override.config', $moduleConfig);

        $container->registerForAutoconfiguration(BrandOverrideModuleInterface::class)
            ->addTag('brand_website.brand_override.module');
    }
}
