<?php

declare(strict_types=1);

namespace App\InfoPages\Controller;

use App\InfoPages\Page\PageType;
use App\InfoPages\Settings\InfoPagesSettings;
use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class InfoPagesAboutController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer,
        private readonly InfoPagesSettings $infoPagesSettings
    )
    {
    }

    #[Route(
        path   : '/about',
        name   : 'route_info_pages_about',
        methods: ['GET'],
    )]
    public function about(): Response
    {
        if ($this->infoPagesSettings->pageType === PageType::CONTENT) {
            return $this->jsonTemplateRenderer->render(
                '@themeJson/info_page/info_page_about_content.json',
            );
        }

        return $this->jsonTemplateRenderer->render(
            '@themeJson/info_page/info_page_about.json',
        );
    }
}
