<?php

declare(strict_types=1);

namespace App\InfoPages\Helper;

use App\Brand\Settings\BrandSettingsHelper;
use App\InfoPages\Page\InfoPage;
use App\InfoPages\Settings\InfoPagesSettings;

final readonly class InfoPagesUrlHelper
{
    public function __construct(
        private InfoPagesSettings $infoPagesSettings,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function getExternalUrl(InfoPage|string|null $page = null, ?string $hash = null): string
    {
        $infoPage = is_string($page) ? InfoPage::from($page) : $page;

        $urlHash = (string)$hash !== '' ? sprintf('#%s', $hash) : '';
        $url = $this->infoPagesSettings->linkToVisymoPublishing
            ? 'https://www.visymo-publishing.com'
            : 'https://www.visymo.com';

        return sprintf(
            '%s/%s?ref=%s%s',
            $url,
            (string)$infoPage?->value,
            urlencode($this->brandSettingsHelper->getSettings()->getSlug()),
            $urlHash,
        );
    }
}
